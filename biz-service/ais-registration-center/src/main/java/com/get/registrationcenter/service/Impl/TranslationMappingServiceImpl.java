package com.get.registrationcenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.registrationcenter.dao.PrivacyPolicyMapper;
import com.get.registrationcenter.dao.TranslationMapper;
import com.get.registrationcenter.dao.TranslationMappingMapper;
import com.get.registrationcenter.vo.TranslationMappingVo;
import com.get.registrationcenter.entity.RegistrationPrivacyPolicy;
import com.get.registrationcenter.entity.RegistrationTranslation;
import com.get.registrationcenter.entity.RegistrationTranslationMapping;
import com.get.registrationcenter.service.ITranslationMappingService;
import com.get.registrationcenter.dto.TranslationDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/5/14 17:21
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationMappingServiceImpl implements ITranslationMappingService {
    @Resource
    private TranslationMappingMapper translationMappingMapper;
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private PrivacyPolicyMapper privacyPolicyMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto) {
        if (GeneralTool.isEmpty(translationDto.getType()) || GeneralTool.isEmpty(translationDto.getLanguageCode())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(TranslationMapping.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<RegistrationTranslationMapping> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(translationDto)) {
            if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                lambdaQueryWrapper.eq(RegistrationTranslationMapping::getFkTableName, translationDto.getFkTableName());
            }
        }
//        List<TranslationMapping> translationMappings = translationMappingMapper.selectByExample(example);
        List<RegistrationTranslationMapping> translationMappings = translationMappingMapper.selectList(lambdaQueryWrapper);


        Map<String, Object> map = fromJavaBean(translationDto.getFkTableName(), translationDto.getFkTableId());
        List<TranslationMappingVo> translationMappingVos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
        if (translationDto.getType() == 1 && translationDto.getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        /*translationMappingVo.setStandardContent(map.get(key).toString());
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));*/
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        if (GeneralTool.isNotEmpty(map.get(key))) {
                            translationMappingVo.setTranslationContent(map.get(key).toString());
                        }
                    }
                }
            }
        } else {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));
                    }
                }
            }
        }

        return translationMappingVos;
    }

    @Override
    public void updateTranslations(List<TranslationDto> translationDtos) {
        if (GeneralTool.isEmpty(translationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (translationDtos.get(0).getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Simplified_Chinese_not_translation"));
        }
        for (TranslationDto translationDto : translationDtos) {
//            Example example = new Example(Translation.class);
//            Example.Criteria criteria = example.createCriteria();
            LambdaQueryWrapper<RegistrationTranslation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (GeneralTool.isNotEmpty(translationDto)) {
                if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
//                    criteria.andEqualTo("fkTableName",translationDto.getFkTableName());
                    lambdaQueryWrapper.eq(RegistrationTranslation::getFkTableName, translationDto.getFkTableName());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTableId())) {
//                    criteria.andEqualTo("fkTableId",translationDto.getFkTableId());
                    lambdaQueryWrapper.eq(RegistrationTranslation::getFkTableId, translationDto.getFkTableId());
                }
                if (GeneralTool.isNotEmpty(translationDto.getLanguageCode())) {
//                    criteria.andEqualTo("languageCode",translationDto.getLanguageCode());
                    lambdaQueryWrapper.eq(RegistrationTranslation::getLanguageCode, translationDto.getLanguageCode());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTranslationMappingId())) {
//                    criteria.andEqualTo("fkTranslationMappingId",translationDto.getFkTranslationMappingId());
                    lambdaQueryWrapper.eq(RegistrationTranslation::getFkTranslationMappingId, translationDto.getFkTranslationMappingId());
                }
            }
            int i = translationMapper.delete(lambdaQueryWrapper);
            if (i < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
            RegistrationTranslation translation = BeanCopyUtils.objClone(translationDto, RegistrationTranslation::new);
            utilService.updateUserInfoToEntity(translation);
            translationMapper.insert(translation);
        }
    }

//    @Override
//    public List<Map<String, Object>> findLanguageType()  {
//        return ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.LANGUAGE_TYPE);
//    }

    private Map<String, Object> fromJavaBean(String fkTableName, Long fkTableId) {
        if (GeneralTool.isEmpty(fkTableName) || GeneralTool.isEmpty(fkTableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Map<String, Object> map = null;
        if (fkTableName.equals(TableEnum.PRIVATE_POLICY.key)) {
            RegistrationPrivacyPolicy privacyPolicy = privacyPolicyMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(privacyPolicy)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(privacyPolicy);
        }
        return map;
    }
}
