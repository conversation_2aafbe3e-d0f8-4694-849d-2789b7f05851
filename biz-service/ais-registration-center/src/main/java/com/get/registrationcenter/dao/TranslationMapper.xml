<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.registrationcenter.dao.TranslationMapper">

  <insert id="insertSelective" parameterType="com.get.registrationcenter.entity.RegistrationTranslation" keyProperty="id" useGeneratedKeys="true">
    insert into s_translation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkTableId != null">
        fk_table_id,
      </if>
      <if test="fkTranslationMappingId != null">
        fk_translation_mapping_id,
      </if>
      <if test="languageCode != null">
        language_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="translation != null">
        translation,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="fkTranslationMappingId != null">
        #{fkTranslationMappingId,jdbcType=BIGINT},
      </if>
      <if test="languageCode != null">
        #{languageCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="translation != null">
        #{translation,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getTranslation" resultType="java.lang.String">
    select translation from s_translation
    <where>
      <if test="fkTableName != null and  fkTableName !=''">
        and fk_table_name =#{fkTableName}
      </if>
      <if test="fkTableId != null">
        and fk_table_id = #{fkTableId}
      </if>
      <if test="fkTranslationMappingId != null">
        and fk_translation_mapping_id = #{fkTranslationMappingId}
      </if>
      <if test="languageCode != null and  languageCode !=''">
        and language_code =#{languageCode}
      </if>
    </where>
  </select>
  <insert id="insert" parameterType="com.get.registrationcenter.entity.RegistrationTranslation">
    insert into s_translation (id, fk_table_name, fk_table_id,
                               fk_translation_mapping_id, language_code, gmt_create,
                               gmt_create_user, gmt_modified, gmt_modified_user,
                               translation)
    values (#{id,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, #{fkTableId,jdbcType=BIGINT},
            #{fkTranslationMappingId,jdbcType=BIGINT}, #{languageCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
            #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR},
            #{translation,jdbcType=LONGVARCHAR})
  </insert>
</mapper>