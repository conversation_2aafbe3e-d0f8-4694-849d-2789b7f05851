package com.get.registrationcenter.service;

import com.get.registrationcenter.entity.User;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.dto.ContactPersonDto;

/**
 * author:<PERSON>
 * Time: 14:58
 * Date: 2022/10/13
 * Description:
 */
public interface UserService {

    Boolean updateIssueContactPerson(ContactPersonDto contactPersonVo);

    Long insertAgentUser(Agent agent);

    User getUserByAgentId(Long agentId);
}
