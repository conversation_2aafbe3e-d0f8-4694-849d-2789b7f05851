package com.get.registrationcenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.MD5Utils;
import com.get.core.tool.utils.GeneralTool;
import com.get.registrationcenter.dao.UserMapper;
import com.get.registrationcenter.entity.User;
import com.get.registrationcenter.service.UserService;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.dto.ContactPersonDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * author:Neil
 * Time: 14:59
 * Date: 2022/10/13
 * Description:
 */
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;


    @Override
    public Boolean updateIssueContactPerson(ContactPersonDto contactPersonVo) {
        List<Long> fkUserIds = contactPersonVo.getFkUserIds();
        if (GeneralTool.isEmpty(fkUserIds)){
            return true;
        }
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getId,fkUserIds);
        User user = new User();
        user.setName(contactPersonVo.getName());
        user.setGender(contactPersonVo.getGender());
        user.setBirthday(contactPersonVo.getBirthday());
        user.setCompany(contactPersonVo.getCompany());
        user.setPosition(contactPersonVo.getTitle());
        user.setMobile(contactPersonVo.getMobile());
        user.setEmail(contactPersonVo.getEmail());
        user.setWechat(contactPersonVo.getWechat());
        user.setQq(contactPersonVo.getQq());
        user.setWhatsapp(contactPersonVo.getWhatsapp());
        user.setAddress(contactPersonVo.getContactAddress());
        userMapper.update(user,lambdaQueryWrapper);
        return true;
    }

    @Override
    public Long insertAgentUser(Agent agent) {

        User user = new User();
        String randomCodeAndChar = CommonUtil.getRandomCodeAndChar(6);
        user.setLoginId("apply_"+randomCodeAndChar+"_"+agent.getId());

        String uuidPasswd = UUID.randomUUID().toString();;
        user.setLoginPs(MD5Utils.encrypt(uuidPasswd));//内部账号生成随机密码，不允许单独登录，需要BMS跳转登录

        user.setIsActive(true);
        user.setName("（内部申请账号）"+agent.getName());
        user.setFkPlatformType("get_issue");
        String dateString = "2099-12-31 23:59:59";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
        Date date = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
        user.setGmtCreate(date);
        user.setGmtCreateUser(agent.getId().toString());
        userMapper.insert(user);

        return user.getId();
    }

    @Override
    public User getUserByAgentId(Long agentId) {
        List<User> users = userMapper.selectList(Wrappers.lambdaQuery(User.class)
                .likeLeft(User::getLoginId, "_" + agentId));
        if (GeneralTool.isNotEmpty(users)){
            return users.get(0);
        }
        return null;
    }
}
