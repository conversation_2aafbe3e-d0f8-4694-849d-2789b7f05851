package com.get.registrationcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.registrationcenter.vo.TranslationMappingVo;
import com.get.registrationcenter.service.ITranslationMappingService;
import com.get.registrationcenter.dto.TranslationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/14 17:19
 * @verison: 1.0
 * @description:
 */
@Api(tags = "翻译配置管理")
@RestController
@RequestMapping("/registration/translationmapping")
public class TranslationMappingController {

    @Resource
    private ITranslationMappingService translationMappingService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "注册中心/语言翻译管理/查询翻译")
    @PostMapping("getTranslationMappingDtos")
    public ResponseBo<TranslationMappingVo> getTranslationMappingDtos(@RequestBody TranslationDto translationDto) {
        List<TranslationMappingVo> datas = translationMappingService.getTranslationMappingDtos(translationDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 保存翻译配置
     *
     * @param translationDtos
     * @return
     * @
     */

    @ApiOperation(value = "保存翻译接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/保存翻译")
    @PostMapping("updateTranslations")
    public ResponseBo updateTranslations(@RequestBody @Validated(TranslationDto.Add.class) List<TranslationDto> translationDtos) {
        translationMappingService.updateTranslations(translationDtos);
        return ResponseBo.ok();
    }
}
