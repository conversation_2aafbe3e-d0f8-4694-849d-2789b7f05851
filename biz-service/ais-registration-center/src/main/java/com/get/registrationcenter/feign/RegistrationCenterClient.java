package com.get.registrationcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.registrationcenter.dao.UserMapper;
import com.get.registrationcenter.entity.User;
import com.get.registrationcenter.service.UserService;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.dto.ContactPersonDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class RegistrationCenterClient implements IRegistrationCenterClient {

    private final UserMapper userMapper;

    private final UserService userService;

    @Override
    public Result<List<User>> getUsersByIds(List<Long> userIds) {
        return Result.data(userMapper.selectBatchIds(userIds));
    }

    @Override
    public Result<Boolean> updateIssueContactPerson(ContactPersonDto contactPersonVo) {
        return Result.data(userService.updateIssueContact<PERSON>erson(contactPersonVo));
    }


    @Override
    public Result<Long> insertAgentUser(Agent agent) {
        return Result.data(userService.insertAgentUser(agent));
    }

    @Override
    public Result<User> getUserByAgentId(Long agentId) {
        return Result.data(userService.getUserByAgentId(agentId));
    }

}
