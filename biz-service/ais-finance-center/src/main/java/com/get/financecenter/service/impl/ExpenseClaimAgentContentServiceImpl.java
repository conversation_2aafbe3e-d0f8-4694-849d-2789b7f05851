package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ExpenseClaimAgentContentMapper;
import com.get.financecenter.dto.ExpenseClaimAgentContentDto;
import com.get.financecenter.entity.ExpenseClaimAgentContent;
import com.get.financecenter.service.ExpenseClaimAgentContentService;
import com.get.financecenter.vo.ExpenseClaimAgentContentVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 代理关联费用报销
 */
@Service("financeExpenseClaimAgentContentService")
public class ExpenseClaimAgentContentServiceImpl extends BaseServiceImpl<ExpenseClaimAgentContentMapper, ExpenseClaimAgentContent> implements ExpenseClaimAgentContentService {
    @Resource
    private ExpenseClaimAgentContentMapper expenseClaimAgentContentMapper;
    @Resource
    private  UtilService utilService;

    @Override
    public List<ExpenseClaimAgentContentVo> getExpenseClaimAgentContents(ExpenseClaimAgentContentDto expenseClaimAgentContentDto, Page page) {
        if (GeneralTool.isEmpty(expenseClaimAgentContentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        LambdaQueryWrapper<ExpenseClaimAgentContent> wrapper = new LambdaQueryWrapper();
        IPage<ExpenseClaimAgentContent> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);

        List<ExpenseClaimAgentContentVo> institutionPermissionGroupVos = expenseClaimAgentContentMapper.getExpenseClaimAgentContents(pages, expenseClaimAgentContentDto);
        page.setAll((int) pages.getTotal());
        return institutionPermissionGroupVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ExpenseClaimAgentContentDto expenseClaimAgentContentDto) {
        if (GeneralTool.isEmpty(expenseClaimAgentContentDto.getNames())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //检验名称是否重复
        for (String name : expenseClaimAgentContentDto.getNames()) {
            if (expenseClaimAgentContentMapper.selectByName(name)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name" ) +" (" +name+")");
            }
            ExpenseClaimAgentContent expenseClaimAgentContent = new ExpenseClaimAgentContent();
            expenseClaimAgentContent.setName(name);
            expenseClaimAgentContent.setViewOrder(expenseClaimAgentContentMapper.getMaxViewOrder());
            utilService.setCreateInfo(expenseClaimAgentContent);
            expenseClaimAgentContentMapper.insert(expenseClaimAgentContent);
        }
    }

    @Override
    public ExpenseClaimAgentContentVo update(ExpenseClaimAgentContentDto expenseClaimAgentContentDto) {
        if (GeneralTool.isEmpty(expenseClaimAgentContentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(expenseClaimAgentContentDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimAgentContent searchById = expenseClaimAgentContentMapper.selectById(expenseClaimAgentContentDto.getId());
        if (GeneralTool.isEmpty(searchById)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExpenseClaimAgentContent expenseClaimAgentContent = BeanCopyUtils.objClone(expenseClaimAgentContentDto, ExpenseClaimAgentContent::new);
        if (!expenseClaimAgentContent.getName().equals(searchById.getName())){
            expenseClaimAgentContent.setName(expenseClaimAgentContent.getName().replace(" ", "").trim());
            if (expenseClaimAgentContentMapper.checkName(expenseClaimAgentContent.getName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name" ) +" (" +expenseClaimAgentContentDto.getName()+")");

            }
        }
        utilService.updateUserInfoToEntity(expenseClaimAgentContent);
        expenseClaimAgentContentMapper.updateById(expenseClaimAgentContent);
        return findExpenseClaimAgentContentById(expenseClaimAgentContentDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimAgentContent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        expenseClaimAgentContentMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //检验参数是否存在
        for (Long id : ids) {
            ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(id);
            if (GeneralTool.isEmpty(expenseClaimAgentContent)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            expenseClaimAgentContentMapper.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<Long> ids) {
//        if (GeneralTool.isEmpty(ids)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//
//        if (ids.size() != 2){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
//        }
//        ExpenseClaimAgentContent expenseClaimAgentContentOne = expenseClaimAgentContentMapper.selectById(ids.get(0));
//        if (GeneralTool.isEmpty(expenseClaimAgentContentOne)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        ExpenseClaimAgentContent moveDataOne = BeanCopyUtils.objClone(expenseClaimAgentContentOne, ExpenseClaimAgentContent::new);
//        Integer oneViewOrder = moveDataOne.getViewOrder();
//
//        ExpenseClaimAgentContent expenseClaimAgentContentTwo = expenseClaimAgentContentMapper.selectById(ids.get(1));
//        if (GeneralTool.isEmpty(expenseClaimAgentContentTwo)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//
//        ExpenseClaimAgentContent moveDataTwo = BeanCopyUtils.objClone(expenseClaimAgentContentTwo, ExpenseClaimAgentContent::new);
//        Integer twoViewOrder = moveDataTwo.getViewOrder();
//
//        moveDataOne.setViewOrder(twoViewOrder);
//        utilService.updateUserInfoToEntity(moveDataOne);
//        moveDataTwo.setViewOrder(oneViewOrder);
//        utilService.updateUserInfoToEntity(moveDataTwo);
//        expenseClaimAgentContentMapper.updateById(moveDataOne);
//        expenseClaimAgentContentMapper.updateById(moveDataTwo);

        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }


        if (ids.size() != 2){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }

        List<ExpenseClaimAgentContent> expenseClaimAgentContents = expenseClaimAgentContentMapper.selectBatchIds(ids);
        if (expenseClaimAgentContents.size() != ids.size()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        Map<Long, Integer> orderMap = expenseClaimAgentContents.stream().collect(Collectors.toMap(ExpenseClaimAgentContent::getId, ExpenseClaimAgentContent::getViewOrder));
        List<ExpenseClaimAgentContent> updateList = new ArrayList<>();

        for (int i = 0; i < ids.size(); i++) {
            Long currentId = ids.get(i);
            Long swapId = ids.get((i + 1) % ids.size());
            if (orderMap.containsKey(swapId)) {
                ExpenseClaimAgentContent entity = expenseClaimAgentContentMapper.selectById(currentId);
                if (entity.getViewOrder() == orderMap.get(swapId)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
                }
                entity.setViewOrder(orderMap.get(swapId));
                utilService.updateUserInfoToEntity(entity);
                updateList.add(entity);
            }
        }

        if (!updateList.isEmpty()) {
            expenseClaimAgentContentMapper.updateBatchById(updateList);
        }


    }

    @Override
    public List<BaseSelectEntity> getExpenseClaimAgentContentList() {
        LambdaQueryWrapper<ExpenseClaimAgentContent> expenseClaimAgentContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        expenseClaimAgentContentLambdaQueryWrapper.select(ExpenseClaimAgentContent::getId, ExpenseClaimAgentContent::getName);
        expenseClaimAgentContentLambdaQueryWrapper.orderByDesc(ExpenseClaimAgentContent::getViewOrder);
        List<ExpenseClaimAgentContent> expenseClaimAgentContents = expenseClaimAgentContentMapper.selectList(expenseClaimAgentContentLambdaQueryWrapper);
        if (expenseClaimAgentContents.isEmpty()){
            return new ArrayList<>();
        }
        List<BaseSelectEntity> expenseClaimAgentContentList = new ArrayList<>();
        expenseClaimAgentContents.forEach(expenseClaimAgentContent -> {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(expenseClaimAgentContent.getId());
            baseSelectEntity.setName(expenseClaimAgentContent.getName());
            expenseClaimAgentContentList.add(baseSelectEntity);

        });
        return expenseClaimAgentContentList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<ExpenseClaimAgentContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(ExpenseClaimAgentContent::getViewOrder,start,end).orderByDesc(ExpenseClaimAgentContent::getViewOrder);
        }else {
            lambdaQueryWrapper.between(ExpenseClaimAgentContent::getViewOrder,end,start).orderByDesc(ExpenseClaimAgentContent::getViewOrder);

        }
        List<ExpenseClaimAgentContent> expenseClaimAgentContents = list(lambdaQueryWrapper);
        List<ExpenseClaimAgentContent> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<ExpenseClaimAgentContent> sortedList = Lists.newArrayList();
            ExpenseClaimAgentContent expenseClaimAgentContentLast = expenseClaimAgentContents.get(expenseClaimAgentContents.size() - 1);
            sortedList.add(expenseClaimAgentContentLast);
            expenseClaimAgentContents.remove(expenseClaimAgentContents.size() - 1);
            sortedList.addAll(expenseClaimAgentContents);
            for (ExpenseClaimAgentContent expenseClaimAgentContent : sortedList) {
                expenseClaimAgentContent.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<ExpenseClaimAgentContent> sortedList = Lists.newArrayList();
            ExpenseClaimAgentContent expenseClaimAgentContentFirst = expenseClaimAgentContents.get(0);
            expenseClaimAgentContents.remove(0);
            sortedList.addAll(expenseClaimAgentContents);
            sortedList.add(expenseClaimAgentContentFirst);
            for (ExpenseClaimAgentContent expenseClaimAgentContent : sortedList) {
                expenseClaimAgentContent.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }


    public ExpenseClaimAgentContentVo findExpenseClaimAgentContentById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimAgentContent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExpenseClaimAgentContentVo expenseClaimAgentContentVo = BeanCopyUtils.objClone(expenseClaimAgentContent, ExpenseClaimAgentContentVo::new);
        return expenseClaimAgentContentVo;
    }

}

