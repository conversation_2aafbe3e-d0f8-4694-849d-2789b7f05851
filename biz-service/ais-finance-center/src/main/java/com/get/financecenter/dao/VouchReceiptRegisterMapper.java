package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.VouchReceiptRegisterDto;
import com.get.financecenter.entity.VouchReceiptRegister;
import com.get.financecenter.vo.VouchReceiptRegisterVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VouchReceiptRegisterMapper extends BaseMapper<VouchReceiptRegister> {

    List<VouchReceiptRegisterVo> getVouchReceiptRegister(IPage<VouchReceiptRegister> page, @Param("vouchReceiptRegisterDto") VouchReceiptRegisterDto vouchReceiptRegisterDto);

}