package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.VouchApplicationFormPaymentQueryDto;
import com.get.financecenter.entity.VouchApplicationFormPayment;
import com.get.financecenter.vo.VouchApplicationFormPaymentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VouchApplicationFormPaymentMapper extends BaseMapper<VouchApplicationFormPayment> {

    /**
     * 付款列表
     *
     * @param iPage
     * @param vouchApplicationFormPaymentQueryDto
     * @return
     */
    List<VouchApplicationFormPaymentVo> getVouchApplicationFormPaymentDatas(IPage<VouchApplicationFormPaymentVo> iPage, @Param("vouchApplicationFormPaymentQueryDto") VouchApplicationFormPaymentQueryDto vouchApplicationFormPaymentQueryDto);

}