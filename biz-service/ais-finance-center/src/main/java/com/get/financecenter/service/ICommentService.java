package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.CommentDto;
import com.get.financecenter.vo.FCommentVo;
import com.get.financecenter.entity.FComment;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/5 11:29
 * @verison: 1.0
 * @description:
 */
public interface ICommentService extends BaseService<FComment> {

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentVo, page]
     * <AUTHOR>
     */
    List<FCommentVo> datas(CommentDto commentDto, Page page);

    /**
     * @return void
     * @Description :保存
     * @Param [comment]
     * <AUTHOR>
     */
    void addComment(FComment comment);

    /**
     * @return void
     * @Description :更新
     * @Param [comment]
     * <AUTHOR>
     */
    void updateComment(FComment comment);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);
}
