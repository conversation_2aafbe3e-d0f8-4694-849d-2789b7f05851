package com.get.financecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.dao.ReceiptFormInvoiceMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.vo.OccVo;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.entity.ReceiptFormInvoice;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IPaymentFormItemService;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.service.ImportService;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ImportServiceImpl implements ImportService {


    @Autowired
    private ReceiptFormMapper receiptFormMapper;

    @Autowired
    private ReceiptFormItemMapper receiptFormItemMapper;

    @Autowired
    private PaymentFormMapper paymentFormMapper;

    @Autowired
    private ReceiptFormInvoiceMapper receiptFormInvoiceMapper;

    @Autowired
    private PaymentFormItemMapper paymentFormItemMapper;

    @Autowired
    private IExchangeRateService exchangeRateService;

    @Autowired
    private IPaymentFormItemService paymentFormItemService;

    @Autowired
    private IPaymentFormService paymentFormService;


    private final static String CREATE_USER = "admin-tp";

    private final static Long COMPANY_ID = 2L;

    private final static String TYPE_KEY = "m_institution_provider";


    @Override
    public Result<BigDecimal> importReceivableData(List<OccVo> receivedOcList, Long receivablePlanId, Long targetId, String currency) {
        if (receivedOcList == null || receivedOcList.isEmpty()) {
            return Result.data(null);
        }
        BigDecimal fee = new BigDecimal(0);
        for (OccVo receivedOc : receivedOcList) {
            if (receivedOc != null) {
                BigDecimal receivedamount = receivedOc.getReceivedamount();
                if (receivedamount == null) {
                    receivedamount = new BigDecimal(0);
                }
                BigDecimal feesreceived = receivedOc.getFeesreceived();
                if (feesreceived == null) {
                    feesreceived = new BigDecimal(0);
                }
                fee.add(feesreceived);
                ReceiptForm receiptForm = new ReceiptForm();
                receiptForm.setFkCompanyId(COMPANY_ID);
                receiptForm.setFkTypeKey(TYPE_KEY);
                receiptForm.setFkTypeTargetId(-1L);
                /*  !!!!!!!! 等待给我*/
                receiptForm.setFkBankAccountId(1L);
                receiptForm.setNumBank("HKR20220425");
                receiptForm.setFkCurrencyTypeNum(receivedOc.getCurrency());
                receiptForm.setAmount(receivedamount);
                receiptForm.setServiceFee(feesreceived);
                receiptForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(receivedOc.getCurrency(), "HKD"));
                receiptForm.setAmountHkd(receivedamount.multiply(receiptForm.getExchangeRateHkd()));
                receiptForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(receivedOc.getCurrency(), "CNY"));
                receiptForm.setAmountRmb(receivedamount.multiply(receiptForm.getExchangeRateRmb()));
                receiptForm.setStatus(1);
                receiptForm.setGmtCreate(receivedOc.getCreatetime());
                receiptForm.setGmtCreateUser(CREATE_USER);
                receiptFormMapper.insert(receiptForm);
                ReceiptForm rf = receiptFormMapper.selectById(receiptForm.getId());
                if (GeneralTool.isNotEmpty(rf)) {
                    receiptForm.setNumSystem(getReceiptFormNum(receivedOc.getCreatetime(), rf.getId()));
                    receiptFormMapper.updateById(receiptForm);
                }
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                /*
                    创建收款单和发票绑定
                 */
                ReceiptFormInvoice rfi = new ReceiptFormInvoice();
                rfi.setFkInvoiceId(-1L);
                rfi.setFkReceiptFormId(rf.getId());
                rfi.setGmtCreate(receivedOc.getCreatetime());
                rfi.setGmtCreateUser(CREATE_USER);
                receiptFormInvoiceMapper.insert(rfi);
                log.info("绑定收款单ID{},发票ID{}", rf.getId(), -1L);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                         /*
                    创建收款单记录
                 */
                log.info("插入应付计划Id={}，对应courseId={},收款单Id={}", receivablePlanId, receivedOc.getCourseid(), receiptForm.getId());
                ReceiptFormItem receiptFormItem = new ReceiptFormItem();
                receiptFormItem.setFkReceiptFormId(rf.getId());
                receiptFormItem.setFkReceivablePlanId(receivablePlanId);
                receiptFormItem.setAmountReceipt(receivedamount);
                receiptFormItem.setServiceFee(feesreceived);
                receiptFormItem.setExchangeRateReceivable(exchangeRateService.getRateByCurrency(receivedOc.getCurrency(), currency));
                receiptFormItem.setAmountReceivable(receivedamount.add(feesreceived).multiply(receiptFormItem.getExchangeRateReceivable()));
//                if (receiptFormItem.getAmountReceivable().compareTo(new BigDecimal(0)) < 0) {
//                    System.err.println(receivedOc.getCourseid() + "实收小于0" + receivedamount);
//                }
                receiptFormItem.setAmountExchangeRate(new BigDecimal(0));
                receiptFormItem.setExchangeRateHkd(exchangeRateService.getRateByCurrency(receivedOc.getCurrency(), "HKD"));
                receiptFormItem.setAmountHkd(receivedamount.add(feesreceived).multiply(receiptFormItem.getExchangeRateHkd()));
                receiptFormItem.setExchangeRateRmb(exchangeRateService.getRateByCurrency(receivedOc.getCurrency(), "CNY"));
                receiptFormItem.setAmountRmb(receivedamount.add(feesreceived).multiply(receiptFormItem.getExchangeRateRmb()));
                receiptFormItem.setGmtCreate(receivedOc.getGmtreceivedcreatetime());
                receiptFormItem.setGmtCreateUser(CREATE_USER);
                receiptFormItemMapper.insert(receiptFormItem);
            }
        }
        return Result.data(fee);
    }

    private String getReceiptFormNum(Date time, Long id) {
        String code = String.valueOf(id);
        if (String.valueOf(id).length() < 9) {
            code = String.format("%08d", id);
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return "RCF" + formatter.format(time) + code;
    }

    @Resource
    private ISaleCenterClient iSaleCenterClient;

    @Override
    public void importPaymentNewRecord(OccVo occVo, Long payablePlanId, String currency, Long agentId, BigDecimal payableAmount, Long backId) {
        BigDecimal flag=BigDecimal.ZERO;
        BigDecimal feespaid = occVo.getFeespaid();
        if (feespaid == null) {
            feespaid = flag;
        }
        BigDecimal paidamount = occVo.getPaidamount();
        if (paidamount == null) {
            paidamount = flag;
        }
        PaymentForm paymentForm = new PaymentForm();
        paymentForm.setFkCompanyId(COMPANY_ID);
        paymentForm.setFkTypeKey("m_agent");
        paymentForm.setFkTypeTargetId(agentId);
        paymentForm.setFkBankAccountId(backId);
        paymentForm.setFkBankAccountIdCompany(1L);
        paymentForm.setNumBank("HKP20220425");
        String curCurrency = occVo.getCurrency();
        paymentForm.setFkCurrencyTypeNum(curCurrency);
        paymentForm.setAmount(paidamount);
        paymentForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(curCurrency, "HKD"));
        paymentForm.setAmountHkd(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateHkd()));
        paymentForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(curCurrency, "CNY"));
        paymentForm.setAmountRmb(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateRmb()));
        paymentForm.setServiceFee(feespaid);
        paymentForm.setStatus(1);
        paymentForm.setGmtCreate(occVo.getCreatetime());
        paymentForm.setGmtCreateUser(CREATE_USER);
        paymentFormMapper.insert(paymentForm);
        //设置系统编号
        String payFormNum = getPayFormNum(occVo.getCreatetime(), paymentForm.getId());
        paymentForm.setNumSystem(payFormNum);
        paymentFormMapper.updateById(paymentForm);

        PaymentFormItem payRecordItem = new PaymentFormItem();
        payRecordItem.setFkPaymentFormId(paymentForm.getId());
        payRecordItem.setFkPayablePlanId(payablePlanId);
        payRecordItem.setAmountPayment(paidamount);
        payRecordItem.setServiceFee(feespaid.compareTo(flag) < 0 ? flag : feespaid);
        payRecordItem.setAmountExchangeRate(flag);
        payRecordItem.setExchangeRatePayable(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(), currency));
        payRecordItem.setAmountPayable(paidamount.add(payRecordItem.getServiceFee()).multiply(payRecordItem.getExchangeRatePayable()));
        payRecordItem.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
        payRecordItem.setAmountHkd(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateHkd()));
        payRecordItem.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
        payRecordItem.setAmountRmb(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateRmb()));
        payRecordItem.setGmtCreate(occVo.getCreatetime());
        payRecordItem.setGmtCreateUser(CREATE_USER);
        paymentFormItemMapper.insert(payRecordItem);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFee(Set<Long> payItemIds) {
//        if (GeneralTool.isNotEmpty(payItemIds)) {
            BigDecimal fee = BigDecimal.valueOf(5);
        List<PaymentForm> forms = paymentFormMapper.selectList(Wrappers.<PaymentForm>lambdaQuery().eq(PaymentForm::getGmtModifiedUser, "admin-tp"));
        List<Long> longs = forms.stream().map(PaymentForm::getId).collect(Collectors.toList());
        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>lambdaQuery().in(PaymentFormItem::getFkPaymentFormId,longs));
//            for (PaymentFormItem formItem : paymentFormItems) {
//                formItem.setServiceFee(formItem.getServiceFee().add(fee));
//                formItem.setAmountPayable(formItem.getAmountPayable().add(fee));
//                BigDecimal amountHkd = formItem.getAmountPayable().multiply(formItem.getExchangeRateHkd());
//                formItem.setAmountHkd(amountHkd);
//                BigDecimal amountCny= formItem.getAmountPayable().multiply(formItem.getExchangeRateRmb());
//                formItem.setAmountRmb(amountCny);
//                formItem.setGmtModifiedUser(CREATE_USER);
//                formItem.setGmtModified(new Date());
//            }
            Map<Long, List<PaymentFormItem>> listMap = paymentFormItems.stream().collect(Collectors.groupingBy(PaymentFormItem::getFkPaymentFormId));
            Set<Long> ids = paymentFormItems.stream().map(PaymentFormItem::getFkPaymentFormId).collect(Collectors.toSet());
            List<PaymentForm> paymentForms = paymentFormMapper.selectBatchIds(ids);
            for (PaymentForm paymentForm : paymentForms) {
                List<PaymentFormItem> formItems = listMap.get(paymentForm.getId());
                BigDecimal finalFee = formItems.stream().map(PaymentFormItem::getServiceFee).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                paymentForm.setServiceFee(finalFee);
                BigDecimal amountHkd = paymentForm.getAmount().add(paymentForm.getServiceFee()).multiply(paymentForm.getExchangeRateHkd());
                paymentForm.setAmountHkd(amountHkd);
                BigDecimal amountCny = paymentForm.getAmount().add(paymentForm.getServiceFee()).multiply(paymentForm.getExchangeRateRmb());
                paymentForm.setAmountRmb(amountCny);
//                paymentForm.setGmtModifiedUser(CREATE_USER);
//                paymentForm.setGmtModified(new Date());
            }
        System.out.println(paymentForms.size());
//            paymentFormItemService.batchUpdate(paymentFormItems);
            paymentFormService.batchUpdate(paymentForms);
//        }
    }

    @Override
    public void importPayableData(List<OccVo> hiPaidInfoList, Long payablePlanId, String currency, Long agentId, BigDecimal payableAmount, Double receivableFee) {
        if (!hiPaidInfoList.isEmpty()) {
            BigDecimal flag = BigDecimal.valueOf(0);
            if (payableAmount == null) {
                payableAmount = BigDecimal.valueOf(0);
            }
            int size = hiPaidInfoList.size();
            BigDecimal reFee = BigDecimal.valueOf(receivableFee);
            PaymentFormItem payRecordItem = null;
            PaymentForm paymentForm = null;
            PaymentForm pf = null;
            PaymentFormItem pr = null;
            int index = 0;
            for (OccVo hiPaidInfo : hiPaidInfoList) {
                //获取实付对应记录
                BigDecimal feespaid = hiPaidInfo.getFeespaid();
                if (feespaid == null) {
                    feespaid = BigDecimal.valueOf(0);
                }
                BigDecimal paidamount = hiPaidInfo.getPaidamount();
                if (paidamount == null) {
                    paidamount = BigDecimal.valueOf(0);
                }
                paymentForm = new PaymentForm();
                paymentForm.setFkCompanyId(COMPANY_ID);
                paymentForm.setFkTypeKey("m_agent");
                paymentForm.setFkTypeTargetId(agentId);
                if (!GeneralTool.isEmpty(hiPaidInfo.getBankaccountnum())) {
                    Result<AgentContractAccount> account = iSaleCenterClient.getAccount(agentId, hiPaidInfo.getBankaccountnum());
                    if (account.isSuccess() && account.getData() != null) {
                        paymentForm.setFkBankAccountId(account.getData().getId());
                    }
                }
                /*  !!!!!!!! 等待给我*/
                paymentForm.setFkBankAccountIdCompany(1L);
                paymentForm.setNumBank("HKP20220425");
//        paymentForm.setFkPaymentFeeTypeId(1L);
                paymentForm.setFkCurrencyTypeNum(hiPaidInfo.getCurrency());
                paymentForm.setAmount(paidamount);
                paymentForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(hiPaidInfo.getCurrency(), "HKD"));
                paymentForm.setAmountHkd(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateHkd()));
                paymentForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(hiPaidInfo.getCurrency(), "CNY"));
                paymentForm.setAmountRmb(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateRmb()));
                paymentForm.setServiceFee(feespaid);
                paymentForm.setStatus(1);
                paymentForm.setGmtCreate(hiPaidInfo.getCreatetime());
                paymentForm.setGmtCreateUser(CREATE_USER);
                paymentFormMapper.insert(paymentForm);
                //设置系统编号
                String payFormNum = getPayFormNum(hiPaidInfo.getCreatetime(), paymentForm.getId());
                paymentForm.setNumSystem(payFormNum);
                paymentFormMapper.updateById(paymentForm);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (pf == null
                        && size > 1 && paidamount.compareTo(flag) > 0) {
                    pf = paymentForm;
                }
                if (pf == null && size > 1 && index + 1 == size) {
                    pf = paymentForm;
                }

                                    /*
                                        创建付款单记录
                                     */
                log.info("插入应付计划Id={}，对应courseId={},付款单id={}", payablePlanId, hiPaidInfo.getCourseid(), paymentForm.getId());
                payRecordItem = new PaymentFormItem();
                payRecordItem.setFkPaymentFormId(paymentForm.getId());
                payRecordItem.setFkPayablePlanId(payablePlanId);
                payRecordItem.setAmountPayment(paidamount);
                payRecordItem.setServiceFee(feespaid.compareTo(flag) < 0 ? flag : feespaid);
                payRecordItem.setAmountExchangeRate(BigDecimal.valueOf(0));
                payRecordItem.setExchangeRatePayable(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(), currency));
                payRecordItem.setAmountPayable(paidamount.add(payRecordItem.getServiceFee()).multiply(payRecordItem.getExchangeRatePayable()));
                payRecordItem.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
                payRecordItem.setAmountHkd(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateHkd()));
                payRecordItem.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
                payRecordItem.setAmountRmb(paidamount.add(feespaid).multiply(paymentForm.getExchangeRateRmb()));
                payRecordItem.setGmtCreate(hiPaidInfo.getCreatetime());
                payRecordItem.setGmtCreateUser(CREATE_USER);
                paymentFormItemMapper.insert(payRecordItem);
                if (pr == null
                        && size > 1 && paidamount.compareTo(flag) > 0) {
                    pr = payRecordItem;
                }
                if (pr == null && size > 1 && index + 1 == size) {
                    pr = payRecordItem;
                }
                index += 1;
                payableAmount = payableAmount.subtract(paidamount).subtract(feespaid);
            }
            BigDecimal finaFee;
            if (payableAmount.compareTo(flag) <= 0) {
                finaFee = flag;
            } else if (payableAmount.compareTo(BigDecimal.valueOf(Math.max(reFee.doubleValue(), 10))) <= 0) {
                finaFee = payableAmount;
            } else {
                finaFee = flag;
            }
            if (finaFee.compareTo(flag) >= 0) {
                if (size > 1 && pf != null && pr!=null) {
                    pf.setServiceFee(finaFee.add(pf.getServiceFee()));
                    paymentFormMapper.updateById(pf);
                    pr.setServiceFee(finaFee.add(pr.getServiceFee()));
                    pr.setAmountPayable(pr.getAmountPayment().add(pr.getServiceFee()).multiply(pr.getExchangeRatePayable()));
                    pr.setAmountHkd(pr.getAmountPayment().add(pr.getServiceFee()).multiply(pr.getExchangeRateHkd()));
                    pr.setAmountRmb(pr.getAmountPayment().add(pr.getServiceFee()).multiply(pr.getExchangeRateRmb()));
                    paymentFormItemMapper.updateById(pr);
                }
                if (size == 1 && paymentForm != null) {
                    paymentForm.setServiceFee(finaFee.add(paymentForm.getServiceFee()));
                    paymentFormMapper.updateById(paymentForm);
                    payRecordItem.setServiceFee(finaFee.add(payRecordItem.getServiceFee()));
                    payRecordItem.setAmountPayable(payRecordItem.getAmountPayment().add(payRecordItem.getServiceFee()).multiply(payRecordItem.getExchangeRatePayable()));
                    payRecordItem.setAmountHkd(payRecordItem.getAmountPayment().add(payRecordItem.getServiceFee()).multiply(payRecordItem.getExchangeRateHkd()));
                    payRecordItem.setAmountRmb(payRecordItem.getAmountPayment().add(payRecordItem.getServiceFee()).multiply(payRecordItem.getExchangeRateRmb()));
                    paymentFormItemMapper.updateById(payRecordItem);
                }
            }
//            if (firstId != null && firstItemId != null) {
//                if (finaFee.compareTo(flag) > 0) {
//                    if (size == 1) {
//                        paymentForm.setServiceFee(finaFee.add(paymentForm.getServiceFee()));
//                        paymentFormMapper.updateById(paymentForm);
//                    } else if (size > 1) {
//                        pf.setServiceFee(finaFee.add(pf.getServiceFee()));
//                        paymentFormMapper.updateById(paymentForm);
//                        pr.setServiceFee(finaFee.add(pr.getServiceFee()));
//                        pr.setAmountPayable(pr.getAmountPayment().add(pr.getServiceFee()).multiply(pr.getExchangeRatePayable()));
//                        payRecordItem.setAmountHkd(pr.getAmountPayment().add(pr.getServiceFee()).multiply(paymentForm.getExchangeRateHkd()));
//                        payRecordItem.setAmountRmb(pr.getAmountPayment().add(pr.getServiceFee()).multiply(paymentForm.getExchangeRateRmb()));
//                        paymentFormItemMapper.updateById(pr);
//                    }
//                }
////                paymentFormMapper.updateFee(finaFee, firstId);
////                paymentFormItemMapper.updateFee(finaFee, firstItemId, firstId);
//            }
        }
    }

    private String getPayFormNum(Date time, Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return "PMF" + formatter.format(time) + code;
    }
}
