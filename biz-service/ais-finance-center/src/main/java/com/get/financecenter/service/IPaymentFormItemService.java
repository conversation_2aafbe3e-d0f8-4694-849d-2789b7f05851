package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.*;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.PaymentFormItemVo;
import com.get.financecenter.entity.PaymentFormItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/21
 * @TIME: 16:49
 * @Description:
 **/
public interface IPaymentFormItemService extends BaseService<PaymentFormItem> {

    /**
     * @return java.util.List<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 列表数据
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    List<PaymentFormItemVo> datas(PaymentFormItemDto paymentFormItemDto, Page page);

    /**
     * 批量更新
     * @param paymentFormItems
     */
    void batchUpdate(List<PaymentFormItem> paymentFormItems);

    /**
     * 导出付款单子项列表
     *
     *
     * @param paymentFormDto
     */
    void exportPaymentFormItemExcel(PaymentFormDto paymentFormDto);

    /**
     * @return java.lang.Long
     * @Description: 新增多个应收计划
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    void batchAdd(List<PaymentFormItemDto> paymentFormItemDto);

    /**
     * @return com.get.financecenter.vo.PaymentFormItemDto
     * @Description: 修改
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    PaymentFormItemVo update(PaymentFormItemDto paymentFormItemDto);

    /**
     * @return com.get.financecenter.vo.PaymentFormItemDto
     * @Description: 详情
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    PaymentFormItemVo findPaymentFormItemById(Long id);


    /**
     * 获取之前付款信息
     * @param planIds
     * @return
     */
    List<AlreadyPayVo> getAlreadyPayByPlanIds(Set<Long> planIds);
    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [attachedVo, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page);

    /**
     * @return com.get.financecenter.vo.PaymentFormItemDto
     * @Description: 获取应付计划
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    PaymentFormItemVo getPayableFormItem(Long planId, Long formId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [tableName, agentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentOfferItemSelect(String tableName, String fkTypeKey, Long fkTypeTargetId);

    /**
     * @return java.util.List<com.get.financecenter.vo.PaymentFormDto>
     * @Description: 获取付款单列表
     * @Param [payFormId]
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormList(Long planId);

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 19:01 2021/11/22
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds);


    /**
     * 获取付款单个数
     * @param planIds
     * @return
     */
    Integer getPayFormItemCount(Set<Long> planIds);

    /**
     * 快速创建付款单
     * @param quickPaymentFormDto
     * @return
     */
    ResponseBo quickCreatePaymentForm(QuickPaymentFormDto quickPaymentFormDto);

    /**
     * feign 根据应付计划id获取应付折合金额
     *
     * @param payablePlanId
     * @Date 15:47 2022/6/9
     * <AUTHOR>
     */
    BigDecimal getAmountPaidByPayablePlanId(Long payablePlanId);

    /**
     * 应付批量找平
     * @param balancingPaymentFormDtos
     * @return
     */
    ResponseBo batchBalancingPaymentForm(List<BalancingPaymentFormDto> balancingPaymentFormDtos);

    /**
     * 根据keys和学生id集合获取付款单子项
     *
     * @param keys       m_student_offer_item（留学申请计划）
     *                   m_student_insurance（留学保险）
     *                   m_student_accommodation（留学住宿）
     *                   m_student_service_fee（留学服务费）
     * @param studentIds 学生id集合
     * @return
     */
    Map<Long, Integer> getPaymentFormItemList(List<String> keys, Set<Long> studentIds);
}
