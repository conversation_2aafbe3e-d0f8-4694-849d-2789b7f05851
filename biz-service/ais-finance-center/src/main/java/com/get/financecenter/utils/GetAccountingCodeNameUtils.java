package com.get.financecenter.utils;

import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.entity.AccountingItem;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 获取科目名称信息 工具类
 */
@Component
public class GetAccountingCodeNameUtils {

    @Resource
    private AccountingItemMapper accountingItemMapper;

    public String setAccountingCodeName(Long accountingItemId) {
        List<AccountingItem> allItems = accountingItemMapper.getAccountingItemDropdownMenu();
        if (GeneralTool.isNotEmpty(allItems)){

            Map<Long, AccountingItem> itemMap = allItems.stream()
                    .collect(Collectors.toMap(AccountingItem::getId, Function.identity()));

            LinkedList<String> path = new LinkedList<>();
            AccountingItem current = itemMap.get(accountingItemId);
            if (current == null) return "";

//            path.addFirst(current.getCode());// 添加code
            String code = current.getCode();
            path.addLast(current.getCodeName());   // 添加当前的名称

            while (current.getFkParentAccountingItemId() != 0) {
                current = itemMap.get(current.getFkParentAccountingItemId());
                if (current == null) break;
                path.addFirst(current.getCodeName());  // 只添加父节点的名称
            }
            return code + "-" + String.join("-", path);
//            return String.join("-", path);
        }
        return "";
    }

    public String setAccountingName(Long accountingItemId) {
        List<AccountingItem> allItems = accountingItemMapper.getAccountingItemDropdownMenu();
        if (GeneralTool.isNotEmpty(allItems)){

            Map<Long, AccountingItem> itemMap = allItems.stream()
                    .collect(Collectors.toMap(AccountingItem::getId, Function.identity()));

            LinkedList<String> path = new LinkedList<>();
            AccountingItem current = itemMap.get(accountingItemId);
            if (current == null) return "";

            path.addLast(current.getCodeName());   // 添加当前的名称

            while (current.getFkParentAccountingItemId() != 0) {
                current = itemMap.get(current.getFkParentAccountingItemId());
                if (current == null) break;
                path.addFirst(current.getCodeName());
            }
            return String.join("-", path);
        }
        return "";
    }

}
