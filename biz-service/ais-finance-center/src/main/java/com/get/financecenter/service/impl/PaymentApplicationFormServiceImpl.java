package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.MediaAndAttachedMapper;
import com.get.financecenter.dao.PaymentApplicationFormItemMapper;
import com.get.financecenter.dao.PaymentApplicationFormMapper;
import com.get.financecenter.dao.PaymentFeeTypeMapper;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.PaymentApplicationFormDto;
import com.get.financecenter.dto.PaymentApplicationFormItemDto;
import com.get.financecenter.dto.query.PaymentApplicationFormQueryDto;
import com.get.financecenter.entity.FMediaAndAttached;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentApplicationFormItem;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IPaymentApplicationFormService;
import com.get.financecenter.utils.MyStringUtils;
import com.get.financecenter.utils.RelationTargetProcessorUtils;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentApplicationFormItemVo;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.financecenter.vo.PaymentFeeTypeVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.DepartmentVo;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/30 11:47
 */
@Service
public class PaymentApplicationFormServiceImpl extends BaseServiceImpl<PaymentApplicationFormMapper, PaymentApplicationForm> implements IPaymentApplicationFormService {
    @Resource
    PaymentApplicationFormMapper paymentApplicationFormMapper;
    @Resource
    PaymentApplicationFormItemMapper paymentApplicationFormItemMapper;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private PaymentFeeTypeMapper paymentFeeTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient iFinanceCenterClient;
    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private RelationTargetProcessorUtils relationTargetProcessorUtils;

    @Override
    public Long save(PaymentApplicationFormDto mpay) {
        PaymentApplicationForm paymentApplicationForm = BeanCopyUtils.objClone(mpay, PaymentApplicationForm::new);
        utilService.updateUserInfoToEntity(paymentApplicationForm);
        //撤单有可能不为空
        if (GeneralTool.isEmpty(mpay.getFkStaffId())) {
            paymentApplicationForm.setFkStaffId(GetAuthInfo.getStaffId());
        }
        paymentApplicationForm.setStatus(0);
        paymentApplicationForm.setIsVouchCreated(false);
        paymentApplicationFormMapper.insert(paymentApplicationForm);
        paymentApplicationForm.setNum(MyStringUtils.getFormNum("PMF",paymentApplicationForm.getId()));
        paymentApplicationFormMapper.updateById(paymentApplicationForm);

        //附件
        if (mpay.getMediaAndAttachedVos() != null) {
            List<MediaAndAttachedDto> mediaAndAttachedDtos = mpay.getMediaAndAttachedVos();
            for (int j = 0; j < mediaAndAttachedDtos.size(); j++) {
                FMediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(mediaAndAttachedDtos.get(j), FMediaAndAttached::new);
                mediaAndAttached.setFkTableId(paymentApplicationForm.getId());
                utilService.updateUserInfoToEntity(mediaAndAttached);

                mediaAndAttachedMapper.insert(mediaAndAttached);
            }
        }

        List<PaymentApplicationFormItemDto> paymentApplicationFormItemDtos = mpay.getPaymentApplicationFormItemVos();
        for (int i = 0; i < paymentApplicationFormItemDtos.size(); i++) {
            PaymentApplicationFormItem mpayItem = BeanCopyUtils.objClone(paymentApplicationFormItemDtos.get(i), PaymentApplicationFormItem::new);
            mpayItem.setFkPaymentApplicationFormId(paymentApplicationForm.getId());
            utilService.updateUserInfoToEntity(mpayItem);
            mpayItem.setFkPaymentFeeTypeId(mpayItem.getFkPaymentFeeTypeId());
            paymentApplicationFormItemMapper.insert(mpayItem);
        }
        return paymentApplicationForm.getId();

    }

    @Override
    public Boolean startPayFlow(String businessKey, String procdefKey, String companyId) {

        //111111
        Result<Boolean> result = workflowCenterClient.startPayFlow(businessKey, procdefKey, companyId);

        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_error"));
        }

        return true;
    }


    @Override
    public Boolean updateMpay(PaymentApplicationForm payForm) {
        utilService.updateUserInfoToEntity(payForm);
        paymentApplicationFormMapper.updateById(payForm);
        return true;
    }

    @Override
    public PaymentApplicationFormVo getMpayById(Long id) {

        PaymentApplicationForm payForm = paymentApplicationFormMapper.selectById(id);
        PaymentApplicationFormVo mpayDto = BeanCopyUtils.objClone(payForm, PaymentApplicationFormVo::new);
        return mpayDto;
    }

    @Override
    public List<FMediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVo.get(0).getTypeKey().equals(FileTypeEnum.WORKFLOW_PAY_FILE.key)) {
//            Example example = new Example(MediaAndAttached.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("fkTableName", mediaAttachedVo.get(0).getFkTableName());
//            criteria.andEqualTo("fkTableId", mediaAttachedVo.get(0).getFkTableId());
//            criteria.andEqualTo("typeKey", TableEnum.WORKFLOW_MPAY.key);
//            mediaAndAttachedMapper.deleteByExample(example);
            mediaAndAttachedMapper.delete(Wrappers.<FMediaAndAttached>query().lambda()
                    .eq(FMediaAndAttached::getFkTableName, TableEnum.WORKFLOW_MPAY.key)
                    .eq(FMediaAndAttached::getFkTableId, mediaAttachedVo.get(0).getFkTableId())
                    .eq(FMediaAndAttached::getTypeKey, mediaAttachedVo.get(0).getFileKey()));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVos : mediaAttachedVo) {
            //设置插入的表
            mediaAttachedVos.setFkTableName(TableEnum.WORKFLOW_MPAY.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAttachedVos));
        }
        return mediaAndAttachedDtos;
    }

    @Override
    public List<PaymentApplicationFormVo> getMpayList(PaymentApplicationFormQueryDto paymentApplicationFormVo, Page page) {
        //查询条件-公司ids
        if (GeneralTool.isNotEmpty(paymentApplicationFormVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(paymentApplicationFormVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        String num = "";
        if (GeneralTool.isNotEmpty(paymentApplicationFormVo.getNum())) {
            num = "%" + paymentApplicationFormVo.getNum() + "%";
        }
//        StaffVo staff = StaffContext.getStaff();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        String username = String.valueOf(GetAuthInfo.getLoginId());
        //status 1全部  0个人
        List<PaymentApplicationFormVo> mpayDtoList = new ArrayList<>();

        Result<List<Long>> result = workflowCenterClient.getPersonalHistoryTasks(paymentApplicationFormVo.getProcdkey());
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<Long> personalHistoryTasks = result.getData();
        IPage<PaymentApplicationFormVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<PaymentApplicationFormVo> paymentApplicationFormVos = paymentApplicationFormMapper.getPayFormByData(iPage, paymentApplicationFormVo, personalHistoryTasks, num, username);
        page.setAll((int) iPage.getTotal());

        //公司ids
        Set<Long> companyIds = paymentApplicationFormVos.stream().map(PaymentApplicationFormVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result1.isSuccess()) {
                companyNamesByIds = result1.getData();
            }
        }


        //部门ids
        Set<Long> departmentIds = paymentApplicationFormVos.stream().map(PaymentApplicationFormVo::getFkDepartmentId).collect(Collectors.toSet());
        //根据部门ids获取名称map
        Map<Long, String> departmentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(departmentIds)) {
            Result<Map<Long, String>> result3 = permissionCenterClient.getDepartmentNamesByIds(departmentIds);
            if (result3.isSuccess()) {
                departmentNamesByIds = result3.getData();
            }
        }

        //支付申请单ids
        Set<Long> ids = paymentApplicationFormVos.stream().map(PaymentApplicationFormVo::getId).collect(Collectors.toSet());
        //根据支付申请单ids获取对象map
        Map<Long, List<PaymentApplicationFormItemVo>> paymentApplicationFormItemDtoByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(ids)) {
            paymentApplicationFormItemDtoByIds = getPaymentApplicationFormItemDtoByIds(ids);
        }

        //币种编号nums
        Set<String> currencyTypeNums = paymentApplicationFormVos.stream().map(PaymentApplicationFormVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }


        for (PaymentApplicationFormVo mpayDto : paymentApplicationFormVos) {
            PaymentApplicationFormVo returnMpayDto = mpayDto;
            returnMpayDto.setFkTableParentId(mpayDto.getFkPaymentApplicationFormIdRevoke());
            returnMpayDto.setShortName(companyNamesByIds.get(mpayDto.getFkCompanyId()));
            returnMpayDto.setDepartmentName(departmentNamesByIds.get(mpayDto.getFkDepartmentId()));
            List<PaymentApplicationFormItemVo> dataByFkMpayId = paymentApplicationFormItemDtoByIds.get(mpayDto.getId());
            List<PaymentApplicationFormItemVo> newPaymentApplicationFormItemVo = new ArrayList<>();
            if (GeneralTool.isNotEmpty(dataByFkMpayId)){
                for (PaymentApplicationFormItemVo paymentApplicationFormItemVo : dataByFkMpayId) {
                    PaymentFeeType payFormType = paymentFeeTypeMapper.selectById(paymentApplicationFormItemVo.getFkPaymentFeeTypeId());
                    if (payFormType != null) {
                        PaymentFeeTypeVo paymentFeeTypeVo = BeanCopyUtils.objClone(payFormType, PaymentFeeTypeVo::new);
                        paymentFeeTypeVo.setTargetType(RelationTargetKeyEnum.getNameByRelationTargetKey(payFormType.getRelationTargetKey()));
                        paymentApplicationFormItemVo.setPaymentFeeTypeDto(paymentFeeTypeVo);
                    }
                    newPaymentApplicationFormItemVo.add(paymentApplicationFormItemVo);
                }
            }
            returnMpayDto.setPaymentApplicationFormItemDtos(newPaymentApplicationFormItemVo);
            String currencyNameByNum = currencyNamesByNums.get(mpayDto.getFkCurrencyTypeNum());
            returnMpayDto.setFkCurrencyTypeNumName(currencyNameByNum);
            //查询task版本 111111
            Result<ActRuTaskVo> resulttaskVersionByBusinessKey =
                    workflowCenterClient.getTaskDataByBusinessKey(String.valueOf(mpayDto.getId()), "m_payment_application_form");
            if (!resulttaskVersionByBusinessKey.isSuccess()) {
                throw new GetServiceException(resulttaskVersionByBusinessKey.getMessage());
            }
            //查询task版本
            ActRuTaskVo taskVersionByBusinessKey = new ActRuTaskVo();//111111
            taskVersionByBusinessKey = resulttaskVersionByBusinessKey.getData();
            if (taskVersionByBusinessKey != null) {
                returnMpayDto.setTaskVersion(taskVersionByBusinessKey.getRev());
                returnMpayDto.setTaskId(taskVersionByBusinessKey.getId());
                returnMpayDto.setDeployId(taskVersionByBusinessKey.getDeployId());
                returnMpayDto.setProcInstId(taskVersionByBusinessKey.getProcInstId());
                int signOrGet = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(),
                        taskVersionByBusinessKey.getRev()).getData();
                returnMpayDto.setSignOrGetStatus(signOrGet);
            } else {
                returnMpayDto.setSignOrGetStatus(2);

            }
            mpayDtoList.add(returnMpayDto);

        }
        return mpayDtoList;
    }

    @Override
    public PaymentApplicationFormVo getMpayDetailData(Long businessKey) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        PaymentApplicationForm payForm = paymentApplicationFormMapper.selectById(businessKey);
        if (payForm == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        PaymentApplicationFormVo mpayDto = BeanCopyUtils.objClone(payForm, PaymentApplicationFormVo::new);
        List<PaymentApplicationFormItemVo> dataByFkMpayId = paymentApplicationFormItemMapper.getDataByFkMpayId(businessKey);
        List<PaymentApplicationFormItemVo> newMpayItem = new ArrayList<>();

        mpayDto.setAmountSum(dataByFkMpayId.stream().map(PaymentApplicationFormItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        mpayDto.setFkStaffIdVouchCreatedName(permissionCenterClient.getStaffName(mpayDto.getFkStaffIdVouchCreated()).getData());
        //获取币种List
        Set<String> currencyTypeNumList = dataByFkMpayId.stream().map(paymentApplicationFormItemVo -> paymentApplicationFormItemVo.getFkCurrencyTypeNum()).collect(Collectors.toSet());
        Result<Map<String, String>> result2 = iFinanceCenterClient.getCurrencyNamesByNums(currencyTypeNumList);
        if (!result2.isSuccess()) {
            throw new GetServiceException(result2.getMessage());
        }
        Map<String, String> currencyMap = result2.getData();
        for (PaymentApplicationFormItemVo mpayItemDto : dataByFkMpayId) {
            PaymentApplicationFormItemVo newMpayItemDto = mpayItemDto;
            PaymentFeeType payFormType = paymentFeeTypeMapper.selectById(mpayItemDto.getFkPaymentFeeTypeId());
            if (payFormType != null) {
                newMpayItemDto.setTypeName(payFormType.getTypeName());
                newMpayItemDto.setTargetType(RelationTargetKeyEnum.getNameByRelationTargetKey(payFormType.getRelationTargetKey()));
            }
            newMpayItemDto.setCurrencyTypeName(currencyMap.get(mpayItemDto.getFkCurrencyTypeNum()));
            StringBuilder relationTargetName = new StringBuilder();
//            if (RelationTargetKeyEnum.STAFF.relationTargetKey.equals(newMpayItemDto.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.STAFF.name).append(":");
//                StaffVo staffVo = permissionCenterClient.getStaffById(newMpayItemDto.getRelationTargetId()).getData();
//                relationTargetName.append(staffVo.getFullName());
//                newMpayItemDto.setRelationTargetCompanyId(staffVo.getFkCompanyId());
//            } else if (RelationTargetKeyEnum.PROVIDER.relationTargetKey.equals(newMpayItemDto.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.PROVIDER.name).append(":");
//                Provider provider = providerMapper.selectById(newMpayItemDto.getRelationTargetId());
//                relationTargetName.append(provider.getName());
//                newMpayItemDto.setRelationTargetCompanyId(provider.getFkCompanyId());
//
//            }
//            else if (RelationTargetKeyEnum.STUDENT.relationTargetKey.equals(newMpayItemDto.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.STUDENT.name).append(":");
//                Student student = saleCenterClient.getStudentById(newMpayItemDto.getRelationTargetId()).getData();
//                relationTargetName.append(student.getName());
//                newMpayItemDto.setRelationTargetCompanyId(student.getFkCompanyId());
//
//            }else if (RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey.equals(newMpayItemDto.getRelationTargetKey())){
//                relationTargetName.append(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.name).append(":");
//                StudentServiceFeeSummaryVo studentServiceFeeSummaryVo = saleCenterClient.getServiceFeeNumById(newMpayItemDto.getRelationTargetId()).getData();
//                relationTargetName.append(studentServiceFeeSummaryVo.getServiceFeeNum());
//                newMpayItemDto.setRelationTargetCompanyId(studentServiceFeeSummaryVo.getTargetCompanyNameId());
//            }
            relationTargetProcessorUtils.processRelationTarget(
                    newMpayItemDto.getRelationTargetKey(),
                    newMpayItemDto.getRelationTargetId(),
                    null,
                    newMpayItemDto::setRelationTargetCompanyId,
                    newMpayItemDto::setRelationTargetName
                    );

//            newMpayItemDto.setRelationTargetName(relationTargetName.toString());
            newMpayItem.add(newMpayItemDto);
        }
        //查询task数据111111
        Result<ActRuTaskVo> resulttaskVersionByBusinessKey =
                workflowCenterClient.getTaskDataByBusinessKey(String.valueOf(mpayDto.getId()), "m_payment_application_form");
        if (!resulttaskVersionByBusinessKey.isSuccess()) {
            throw new GetServiceException(resulttaskVersionByBusinessKey.getMessage());
        }
        ActRuTaskVo taskVersionByBusinessKey = new ActRuTaskVo();//111111
        taskVersionByBusinessKey = resulttaskVersionByBusinessKey.getData();
        if (taskVersionByBusinessKey != null) {
            mpayDto.setTaskVersion(taskVersionByBusinessKey.getRev());
            mpayDto.setTaskId(taskVersionByBusinessKey.getId());
            mpayDto.setProcInstId(taskVersionByBusinessKey.getProcInstId());
            mpayDto.setDeployId(taskVersionByBusinessKey.getDeployId());
            //待修改和上面
            if (GeneralTool.isNotEmpty(taskVersionByBusinessKey.getRev()) || GeneralTool.isNotEmpty(taskVersionByBusinessKey.getId())) {
                //111111
                int signOrGet = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(), taskVersionByBusinessKey.getRev()).getData();
                mpayDto.setSignOrGetStatus(signOrGet);
            } else {
                mpayDto.setSignOrGetStatus(2);
            }
        } else {
            mpayDto.setSignOrGetStatus(2);
        }

        Result<String> result = permissionCenterClient.getCompanyNameById(mpayDto.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            mpayDto.setShortName(result.getData());
        }
        String currencyNameByNum = currencyTypeService.getCurrencyNameByNum(mpayDto.getFkCurrencyTypeNum());
        Result<DepartmentVo> departmentDtoResult = permissionCenterClient.getDepartmentById(mpayDto.getFkDepartmentId());
        if (departmentDtoResult.isSuccess() && GeneralTool.isNotEmpty(departmentDtoResult.getData())) {
            mpayDto.setDepartmentName(departmentDtoResult.getData().getName());
        }
        mpayDto.setFkCurrencyTypeNumName(currencyNameByNum);
        mpayDto.setPaymentApplicationFormItemDtos(newMpayItem);
        Result<HiCommentFeignVo> result1 = workflowCenterClient.getHiComment(businessKey, TableEnum.WORKFLOW_MPAY.key);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        HiCommentFeignVo hiComment = result1.getData();
        if (GeneralTool.isNotEmpty(hiComment)) {
            mpayDto.setRefuseButtonType(hiComment.getRefuseButtonType());
        }
        return mpayDto;
    }


    @Override
    public void updataMpayData(PaymentApplicationFormDto paymentApplicationFormDto) {

        if (null != paymentApplicationFormDto.getPaymentApplicationFormItemVos()) {

            if (null != paymentApplicationFormDto.getMediaAndAttachedVos()) {
                List<MediaAndAttachedDto> mediaAndAttachedDtos = paymentApplicationFormDto.getMediaAndAttachedVos();
                for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                    //TODO 改过
                   // FMediaAndAttachedVo mediaAndAttachedDto = BeanCopyUtils.objClone(mediaAndAttachedDto, FMediaAndAttachedVo::new);
                    //utilService.updateUserInfoToEntity(mediaAndAttachedDto);
                    //mediaAndAttachedMapper.updateById(mediaAndAttachedDto);
                    FMediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(mediaAndAttachedDto, FMediaAndAttached::new);
                    utilService.updateUserInfoToEntity(mediaAndAttached);
                    mediaAndAttachedMapper.updateById(mediaAndAttached);

                }
            }
            List<PaymentApplicationFormItemVo> paymentApplicationFormItems = paymentApplicationFormItemMapper.getDataByFkMpayId(paymentApplicationFormDto.getId());
            //这里的更新事由 ，先删除 后添加 TODO 改过
            // for (PaymentApplicationFormItem paymentApplicationFormItem : paymentApplicationFormItems) {
            //                    paymentApplicationFormItemMapper.deleteById(paymentApplicationFormItem);
            //                }
            if (paymentApplicationFormItems.size() != 0) {
                for (PaymentApplicationFormItemVo paymentApplicationFormItem : paymentApplicationFormItems) {
                    paymentApplicationFormItemMapper.deleteById(paymentApplicationFormItem);
                }
            }
            List<PaymentApplicationFormItemDto> paymentApplicationFormItemDtos = paymentApplicationFormDto.getPaymentApplicationFormItemVos();
            for (PaymentApplicationFormItemDto savePaymentApplicationFormItemVos : paymentApplicationFormItemDtos) {
                PaymentApplicationFormItem paymentApplicationFormItem = BeanCopyUtils.objClone(savePaymentApplicationFormItemVos, PaymentApplicationFormItem::new);
                utilService.updateUserInfoToEntity(paymentApplicationFormItem);
                paymentApplicationFormItem.setFkPaymentApplicationFormId(paymentApplicationFormDto.getId());
                paymentApplicationFormItemMapper.insert(paymentApplicationFormItem);
            }

        }
        //TODO 改过
       // PaymentApplicationFormVo mpayDto = BeanCopyUtils.objClone(paymentApplicationFormDto, PaymentApplicationFormVo::new);
        PaymentApplicationForm mpayDto = BeanCopyUtils.objClone(paymentApplicationFormDto, PaymentApplicationForm::new);
        utilService.updateUserInfoToEntity(mpayDto);
        paymentApplicationFormMapper.updateById(mpayDto);


    }

    @Override
    public List<FMediaAndAttachedVo> getPayFileData(MediaAndAttachedDto mediaAndAttachedDto, Page page) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        mediaAndAttachedDto.setFkTableName(TableEnum.WORKFLOW_MPAY.key);

        return attachedService.getMediaAndAttachedDto(mediaAndAttachedDto, page);

    }


    @Override
    public void updateById(Long id) {
        PaymentApplicationForm paymentApplicationForm = paymentApplicationFormMapper.selectById(id);
        paymentApplicationForm.setStatus(5);
        utilService.updateUserInfoToEntity(paymentApplicationForm);
        paymentApplicationFormMapper.updateById(paymentApplicationForm);

    }

    @Override
    public void getUserSubmit(String taskId, String status) {
        //111111
        workflowCenterClient.getUserSubmit(taskId, status);
    }


    public Map<Long, List<PaymentApplicationFormItemVo>> getPaymentApplicationFormItemDtoByIds(Set<Long> ids) {
        Map<Long, List<PaymentApplicationFormItemVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(PaymentApplicationFormItem.class);
//        example.createCriteria().andIn("fkPaymentApplicationFormId", ids);
//        List<PaymentApplicationFormItem> paymentApplicationFormItems = paymentApplicationFormItemMapper.selectByExample(example);
        List<PaymentApplicationFormItem> paymentApplicationFormItems = paymentApplicationFormItemMapper.selectList(Wrappers.<PaymentApplicationFormItem>query().lambda()
                .in(PaymentApplicationFormItem::getFkPaymentApplicationFormId, ids));
        if (GeneralTool.isEmpty(paymentApplicationFormItems)) {
            return map;
        }
        for (PaymentApplicationFormItem paymentApplicationFormItem : paymentApplicationFormItems) {
            PaymentApplicationFormItemVo paymentApplicationFormItemVo = BeanCopyUtils.objClone(paymentApplicationFormItem, PaymentApplicationFormItemVo::new);
            List<PaymentApplicationFormItemVo> paymentApplicationFormItemVos = new ArrayList<>();
            //如果集合中包含支付申请单id，则往原来的对象上面添加数据
            if (map.containsKey(paymentApplicationFormItem.getFkPaymentApplicationFormId())) {
                paymentApplicationFormItemVos = map.get(paymentApplicationFormItem.getFkPaymentApplicationFormId());
                paymentApplicationFormItemVos.add(paymentApplicationFormItemVo);
                map.put(paymentApplicationFormItem.getFkPaymentApplicationFormId(), paymentApplicationFormItemVos);
                continue;
            }
            paymentApplicationFormItemVos.add(paymentApplicationFormItemVo);
            map.put(paymentApplicationFormItem.getFkPaymentApplicationFormId(), paymentApplicationFormItemVos);
        }
        return map;
    }

    @Override
    public void getRevokePaymentApplicationFrom(Long id, String summary) {

        PaymentApplicationForm paymentApplicationForm1 = paymentApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(paymentApplicationForm1)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (paymentApplicationFormMapper.getExistParentId(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        List<PaymentApplicationFormItemVo> dataByFkMpayId = paymentApplicationFormItemMapper.getDataByFkMpayId(paymentApplicationForm1.getId());
        if (GeneralTool.isEmpty(dataByFkMpayId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        PaymentApplicationForm paymentApplicationForm = new PaymentApplicationForm();
        paymentApplicationForm.setFkStaffId(paymentApplicationForm1.getFkStaffId());
        paymentApplicationForm.setFkCompanyId(paymentApplicationForm1.getFkCompanyId());
        paymentApplicationForm.setFkCurrencyTypeNum(paymentApplicationForm1.getFkCurrencyTypeNum());
        paymentApplicationForm.setFkDepartmentId(paymentApplicationForm1.getFkDepartmentId());
        paymentApplicationForm.setFkPaymentApplicationFormIdRevoke(id);
        dataByFkMpayId.stream().forEach(dd -> {
            dd.setId(null);
            dd.setSummary(summary);
            dd.setGmtModified(null);
            dd.setGmtModifiedUser(null);
        });
        PaymentApplicationFormDto paymentApplicationFormDto = BeanCopyUtils.objClone(paymentApplicationForm, PaymentApplicationFormDto::new);
        paymentApplicationFormDto.setPaymentApplicationFormItemVos(BeanCopyUtils.copyListProperties(dataByFkMpayId, PaymentApplicationFormItemDto::new));
        Long newId = this.save(paymentApplicationFormDto);
        if (GeneralTool.isNotEmpty(newId)) {
            List<FMediaAndAttachedVo> mediaAndAttachedList = mediaAndAttachedMapper.getMediaAndAttachedList(id);
            if (GeneralTool.isNotEmpty(mediaAndAttachedList)) {
                mediaAndAttachedList.stream().forEach(mediaAndAttachedDto -> {
                    mediaAndAttachedDto.setFkTableId(newId);
                    mediaAndAttachedDto.setId(null);
                    mediaAndAttachedDto.setGmtModifiedUser(null);
                    mediaAndAttachedDto.setGmtModified(null);
                });
                this.addInstitutionMedia(BeanCopyUtils.copyListProperties(mediaAndAttachedList, MediaAndAttachedDto::new));
            }
            paymentApplicationForm1.setStatus(ProjectExtraEnum.REVOKED.key);
            utilService.updateUserInfoToEntity(paymentApplicationForm1);
            paymentApplicationFormMapper.updateById(paymentApplicationForm1);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 根据id获取支付申请单
     * @param targetId
     * @return
     */
    @Override
    public PaymentApplicationForm getPaymentApplicationFormById(Long targetId) {
        return paymentApplicationFormMapper.selectById(targetId);
    }

    /**
     * 更新支付申请单状态
     * @param paymentApplicationForm
     * @return
     */
    @Override
    public Boolean updatePaymentApplicationFormStatus(PaymentApplicationForm paymentApplicationForm) {
        utilService.updateUserInfoToEntity(paymentApplicationForm);
        paymentApplicationFormMapper.updateById(paymentApplicationForm);
        return true;
    }

    /**
     * 获取支付申请单总金额
     * @param id
     * @return
     */
    @Override
    public BigDecimal getPaymentApplicationFormTotalAmount(Long id) {
        return paymentApplicationFormMapper.getPaymentApplicationFormTotalAmount(id);
    }

}
