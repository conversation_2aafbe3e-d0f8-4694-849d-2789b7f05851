package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.dto.PrepayApplicationFormDto;
import com.get.financecenter.dto.query.PrepayApplicationFormQueryDto;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:28
 */
public interface IPrepayApplicationFormService extends BaseService<PrepayApplicationForm> {

    Long save(PrepayApplicationFormDto prepayApplicationFormDto);

    Boolean startBorrowFlow(String businessKey, String procdefKey, String companyId);


    boolean updateBorrowMoneyStatus(PrepayApplicationForm borrowFrom);

    PrepayApplicationFormVo getBorrowMoneyById(Long id);

    /**
     * @ Description :附件上传
     * @ Param [mediaAttachedVo]
     * @ return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @ author LEO
     */
    List<FMediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVo);


    List<PrepayApplicationFormVo> getBorrowMoneyData(PrepayApplicationFormQueryDto prepayApplicationFormVo, Page page);

    PrepayApplicationFormVo getPrepayDetailData(Long businessKey);

    /**
     * 借款申请单作废
     * @param businessKey
     */
    void cancelPrepayApplicationForm(Long businessKey);

    void updataPrepayData(PrepayApplicationFormDto prepayApplicationFormDto);


    void getUserSubmit(String taskId, String status);


    List<FMediaAndAttachedVo> getPrepayFileData(MediaAndAttachedDto mediaAndAttachedDto, Page page);


    void getRevokePrepayApplication(Long id, String summary);

    /**
     * 根据id获取借款申请单
     * @param targetId
     * @return
     */
    PrepayApplicationForm getPrepayApplicationFormById(Long targetId);
}
