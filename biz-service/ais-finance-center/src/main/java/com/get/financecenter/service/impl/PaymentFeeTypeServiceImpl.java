package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.PaymentFeeTypeMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.dto.PaymentFeeTypeDto;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.service.IPaymentFeeTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.PaymentFeeTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/6 14:58
 */
@Service
public class PaymentFeeTypeServiceImpl extends BaseServiceImpl<PaymentFeeTypeMapper, PaymentFeeType> implements IPaymentFeeTypeService {

    @Resource
    private PaymentFeeTypeMapper paymentFeeTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PaymentFormMapper paymentFormMapper;
    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;

    @Override
    public PaymentFeeTypeVo findPayTypeById(Long id) {

        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentFeeType payFormType = paymentFeeTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(payFormType, PaymentFeeTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<PaymentFeeTypeDto> paymentFeeTypeDtos) {
        if (GeneralTool.isEmpty(paymentFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (PaymentFeeTypeDto paymentFeeTypeDto : paymentFeeTypeDtos) {
            if (validateAdd(paymentFeeTypeDto)) {
                //获取最大排序
                paymentFeeTypeDto.setViewOrder(paymentFeeTypeMapper.getMaxViewOrder());
                PaymentFeeType payFormType = BeanCopyUtils.objClone(paymentFeeTypeDto, PaymentFeeType::new);
                utilService.updateUserInfoToEntity(payFormType);
                int i = paymentFeeTypeMapper.insert(payFormType);
                if (i <= 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (paymentFeeTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //校验是否与支付单做关联。
        Integer count = paymentFormMapper.getPayMentFormCountByTypeId(id);
        if (count > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        } else {
            paymentFeeTypeMapper.deleteById(id);
        }
    }

    @Override
    public PaymentFeeTypeVo updatePayFlowType(PaymentFeeTypeDto paymentFeeTypeDto) {
        if (paymentFeeTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        PaymentFeeType result = paymentFeeTypeMapper.selectById(paymentFeeTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(paymentFeeTypeDto)) {
            PaymentFeeType workFlowType = BeanCopyUtils.objClone(paymentFeeTypeDto, PaymentFeeType::new);
            utilService.updateUserInfoToEntity(workFlowType);
            int i = paymentFeeTypeMapper.updateById(workFlowType);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        return findPayTypeById(paymentFeeTypeDto.getId());
    }

    @Override
    public List<PaymentFeeTypeVo> getPayTypes(PaymentFeeTypeDto paymentFeeTypeDto, Page page) {
//        Example example = new Example(PaymentFeeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(paymentFeeTypeVo)) {
//            if (GeneralTool.isNotEmpty(paymentFeeTypeVo.getTypeName())) {
//                criteria.andLike("typeName", "%" + paymentFeeTypeVo.getTypeName() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<PaymentFeeType> workFlowTypes = paymentFeeTypeMapper.selectByExample(example);
//        page.restPage(workFlowTypes);

        LambdaQueryWrapper<PaymentFeeType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(paymentFeeTypeDto)) {
            if (GeneralTool.isNotEmpty(paymentFeeTypeDto.getTypeName())) {
                wrapper.like(PaymentFeeType::getTypeName, paymentFeeTypeDto.getTypeName());
            }
            if(GeneralTool.isNotEmpty(paymentFeeTypeDto.getKeyWord())){
                wrapper.like(PaymentFeeType::getTypeName, paymentFeeTypeDto.getKeyWord());
            }
            if (GeneralTool.isNotEmpty(paymentFeeTypeDto.getRelationTargetKey())) {
                wrapper.eq(PaymentFeeType::getRelationTargetKey, paymentFeeTypeDto.getRelationTargetKey());
            }
            if (GeneralTool.isNotEmpty(paymentFeeTypeDto.getFkAccountingItemId())) {
                wrapper.eq(PaymentFeeType::getFkAccountingItemId, paymentFeeTypeDto.getFkAccountingItemId());
            }
        }
        wrapper.orderByDesc(PaymentFeeType::getViewOrder);
        IPage<PaymentFeeType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<PaymentFeeType> workFlowTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<PaymentFeeTypeVo> newpaymentFeeTypeVos = new ArrayList<>();
        for (PaymentFeeType paymentFeeType : workFlowTypes) {
            PaymentFeeTypeVo paymentFeeTypeVo = BeanCopyUtils.objClone(paymentFeeType, PaymentFeeTypeVo::new);
            paymentFeeTypeVo.setTypeName(paymentFeeType.getTypeName());
            if (GeneralTool.isNotEmpty(paymentFeeType.getRelationTargetKey())){
                paymentFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(paymentFeeType.getRelationTargetKey()));
            }
            paymentFeeTypeVo.setTargetType(RelationTargetKeyEnum.getNameByRelationTargetKey(paymentFeeType.getRelationTargetKey()));
            paymentFeeTypeVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(paymentFeeType.getFkAccountingItemId()));
            newpaymentFeeTypeVos.add(paymentFeeTypeVo);
        }
        return newpaymentFeeTypeVos;
    }

    @Override
    public void sort(List<PaymentFeeTypeDto> paymentFeeTypeDtos) {
        if (GeneralTool.isEmpty(paymentFeeTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        PaymentFeeType ro = BeanCopyUtils.objClone(paymentFeeTypeDtos.get(0), PaymentFeeType::new);
        PaymentFeeType paymentFeeTypeOne = paymentFeeTypeMapper.selectById(ro.getId());
        if (GeneralTool.isEmpty(paymentFeeTypeOne)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer oneorder = paymentFeeTypeOne.getViewOrder();
        PaymentFeeType rt = BeanCopyUtils.objClone(paymentFeeTypeDtos.get(1), PaymentFeeType::new);
        PaymentFeeType paymentFeeTypeTwo = paymentFeeTypeMapper.selectById(rt.getId());
        if (GeneralTool.isEmpty(paymentFeeTypeTwo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer twoorder = paymentFeeTypeTwo.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        paymentFeeTypeMapper.updateById(ro);
        paymentFeeTypeMapper.updateById(rt);
    }


    @Override
    public List<PaymentFeeTypeVo> getPayTypeList() {
//        Example example = new Example(PaymentFeeType.class);
//        example.orderBy("viewOrder").desc();
//        List<PaymentFeeType> workFlowTypes = paymentFeeTypeMapper.selectByExample(example);
        List<PaymentFeeType> workFlowTypes = paymentFeeTypeMapper.selectList(Wrappers.<PaymentFeeType>query().lambda()
                .orderByDesc(PaymentFeeType::getViewOrder));
        List<PaymentFeeTypeVo> paymentFeeTypeVoList = workFlowTypes.stream().map(workFlowType -> BeanCopyUtils.objClone(workFlowType, PaymentFeeTypeVo::new)).collect(Collectors.toList());
        for (PaymentFeeTypeVo paymentFeeTypeVo : paymentFeeTypeVoList) {
            paymentFeeTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(paymentFeeTypeVo.getRelationTargetKey()));
        }
        return paymentFeeTypeVoList;
    }

    @Override
    public List<Map<String, Object>> findTargetType() {

        return TableEnum.enumsTranslation2Arrays(TableEnum.PAY_OBJECT_TYPE);

    }

    @Override
    public String getTypeNameById(Long payTypeId) {
        if (GeneralTool.isEmpty(payTypeId)) {
            return null;
        }
        PaymentFeeType paymentFeeType = paymentFeeTypeMapper.selectById(payTypeId);
        if (GeneralTool.isEmpty(paymentFeeType)) {
            return null;
        }
        return paymentFeeType.getTypeName();
    }

    @Override
    public Map<Long, String> getTypeNameByIds(Set<Long> payTypeIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(payTypeIds)) {
            return map;
        }
//        Example example = new Example(PaymentFeeType.class);
//        example.createCriteria().andIn("id",payTypeIds);
//        List<PaymentFeeType> paymentFeeTypes = paymentFeeTypeMapper.selectByExample(example);
        List<PaymentFeeType> paymentFeeTypes = paymentFeeTypeMapper.selectBatchIds(payTypeIds);
        if (GeneralTool.isEmpty(paymentFeeTypes)) {
            return map;
        }
        for (PaymentFeeType paymentFeeType : paymentFeeTypes) {
            map.put(paymentFeeType.getId(), paymentFeeType.getTypeName());
        }
        return map;
    }

    @Override
    public List<PaymentFeeTypeVo> getRelationTargetKey() {
        List<PaymentFeeTypeVo> options = RelationTargetKeyEnum.getOptions();
        return options;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<PaymentFeeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(PaymentFeeType::getViewOrder,start,end).orderByDesc(PaymentFeeType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(PaymentFeeType::getViewOrder,end,start).orderByDesc(PaymentFeeType::getViewOrder);

        }
        List<PaymentFeeType> paymentFeeTypes = list(lambdaQueryWrapper);
        List<PaymentFeeType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<PaymentFeeType> sortedList = Lists.newArrayList();
            PaymentFeeType paymentFeeTypeLast = paymentFeeTypes.get(paymentFeeTypes.size() - 1);
            sortedList.add(paymentFeeTypeLast);
            paymentFeeTypes.remove(paymentFeeTypes.size() - 1);
            sortedList.addAll(paymentFeeTypes);
            for (PaymentFeeType paymentFeeType : sortedList) {
                paymentFeeType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<PaymentFeeType> sortedList = Lists.newArrayList();
            PaymentFeeType paymentFeeTypeFirst = paymentFeeTypes.get(0);
            paymentFeeTypes.remove(0);
            sortedList.addAll(paymentFeeTypes);
            sortedList.add(paymentFeeTypeFirst);
            for (PaymentFeeType paymentFeeType : sortedList) {
                paymentFeeType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    private boolean validateAdd(PaymentFeeTypeDto paymentFeeTypeDto) {
//        Example example = new Example(PaymentFeeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", paymentFeeTypeVo.getTypeName());
//        List<PaymentFeeType> list = this.paymentFeeTypeMapper.selectByExample(example);
        List<PaymentFeeType> list = paymentFeeTypeMapper.selectList(Wrappers.<PaymentFeeType>query().lambda()
                .eq(PaymentFeeType::getTypeName, paymentFeeTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(PaymentFeeTypeDto paymentFeeTypeDto) {
//        Example example = new Example(PaymentFeeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", paymentFeeTypeVo.getTypeName());
//        List<PaymentFeeType> list = this.paymentFeeTypeMapper.selectByExample(example);

        List<PaymentFeeType> list = paymentFeeTypeMapper.selectList(Wrappers.<PaymentFeeType>query().lambda()
                .eq(PaymentFeeType::getTypeName, paymentFeeTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(paymentFeeTypeDto.getId());
    }
}
