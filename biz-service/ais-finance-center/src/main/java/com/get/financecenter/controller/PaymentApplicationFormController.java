package com.get.financecenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.service.IPaymentApplicationFormService;
import com.get.financecenter.dto.PaymentApplicationFormDto;
import com.get.financecenter.dto.query.PaymentApplicationFormQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/30 11:45
 */
@Api(tags = "支付流程")
@RestController
@RequestMapping("finance/feginMpay")
public class PaymentApplicationFormController {

    @Resource
    IPaymentApplicationFormService IPaymentApplicationFormServiceice;

    @ApiOperation("支付流程保存")
    @PostMapping("/mPaySave")
    public ResponseBo mPaySave(@RequestBody PaymentApplicationFormDto mpay) {
        if (mpay == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        IPaymentApplicationFormServiceice.save(mpay);
        return ResponseBo.ok();
    }

    @ApiOperation("支付流程开始")
    @GetMapping("/startPayFlow")
    public ResponseBo startPayFlow(@RequestParam("businessKey") String businessKey,
                                   @RequestParam("procdefKey") String procdefKey,
                                   @RequestParam("companyId") String companyId) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        IPaymentApplicationFormServiceice.startPayFlow(businessKey, procdefKey, companyId);
        return ResponseBo.ok();
    }


    @ApiIgnore
    @PostMapping("/updateMpay")
    public Boolean updateMpay(@RequestBody PaymentApplicationForm payForm) {
        IPaymentApplicationFormServiceice.updateMpay(payForm);
        return true;
    }

    @ApiIgnore
    @GetMapping("/getMpayById")
    public PaymentApplicationFormVo getMpayById(@RequestParam("id") Long id) throws GetServiceException {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        PaymentApplicationFormVo mpayById = IPaymentApplicationFormServiceice.getMpayById(id);
        return mpayById;
    }

    @ApiOperation("支付表单列表，selectStatus属性:0查登当前录人，1查全部，2我的审批")
    @PostMapping("/getMpayData")
    public ResponseBo<PaymentApplicationFormVo> getMpayData(@RequestBody SearchBean<PaymentApplicationFormQueryDto> page) {
        List<PaymentApplicationFormVo> mpayList = IPaymentApplicationFormServiceice.getMpayList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<PaymentApplicationFormVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayList, p);
        return mpayDtoListResponseBo;
    }

    @ApiOperation("支付表详细数据")
    @GetMapping("/getMpayDetailData")
    public ResponseBo getMpayDetailData(@RequestParam("businessKey") Long businessKey) {
        PaymentApplicationFormVo mpayList = IPaymentApplicationFormServiceice.getMpayDetailData(businessKey);
        return new ResponseBo<>(mpayList);
    }


    @ApiOperation(value = "保存支付单附件接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  List<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(IPaymentApplicationFormServiceice.addInstitutionMedia(mediaAttachedVo));
    }


    @ApiOperation("重新修改表单")
    @PostMapping("updataMpayData")
    public ResponseBo updataMpayData(@RequestBody PaymentApplicationFormDto paymentApplicationFormDto) {
        IPaymentApplicationFormServiceice.updataMpayData(paymentApplicationFormDto);
        return ResponseBo.ok();

    }


    /**
     * @ Description :
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("文件回显")
    @PostMapping("getPayFileData")
    public ResponseBo getPayFileData(@RequestBody SearchBean<MediaAndAttachedDto> page) {
        List<FMediaAndAttachedVo> payFileData = IPaymentApplicationFormServiceice.getPayFileData(page.getData(), page);
        return new ListResponseBo<>(payFileData);

    }

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("作废")
    @PostMapping("updateCancellationBusiness")
    public ResponseBo updateCancellationBusiness(@RequestParam("id") Long id) {

        IPaymentApplicationFormServiceice.updateById(id);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("重新提交或放弃")
    @GetMapping("getUserSubmit")
    public ResponseBo getUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        IPaymentApplicationFormServiceice.getUserSubmit(taskId, status);
        return ResponseBo.ok();
    }

    /**
     * @ Description :
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("撤单保存内容")
    @GetMapping("getRevokePaymentApplicationFrom")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getRevokePaymentApplicationFrom(@RequestParam("id") Long id,
                                                      @RequestParam("summary") String summary) {
        IPaymentApplicationFormServiceice.getRevokePaymentApplicationFrom(id, summary);
        return ResponseBo.ok();
    }


}


