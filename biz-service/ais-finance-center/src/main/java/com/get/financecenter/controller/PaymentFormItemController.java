package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PaymentFormItemVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.service.IPaymentFormItemService;
import com.get.financecenter.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 17:34
 * @Description:
 **/
@Api(tags = "付款单子项管理")
@RestController
@RequestMapping("finance/paymentFormItem")
public class PaymentFormItemController {

    @Resource
    private IPaymentFormItemService paymentFormItemService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单子项管理/查询")
    @PostMapping("datas")
    public ResponseBo<PaymentFormItemVo> datas(@RequestBody SearchBean<PaymentFormItemDto> page) {
        List<PaymentFormItemVo> datas = paymentFormItemService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 导出付款单子项列表
     *
     * @param response
     * @param paymentFormDto
     */
    @ApiOperation(value = "导出付款单子项列表", notes = "")
    @PostMapping("/exportPaymentFormItemExcel")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单子项管理/导出付款单子项列表")
    @ResponseBody
    public void exportPaymentFormItemExcel(HttpServletResponse response, @RequestBody PaymentFormDto paymentFormDto) {
        CommonUtil.ok(response);
        paymentFormItemService.exportPaymentFormItemExcel(paymentFormDto);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量新增信息
     * @Param [paymentFormItemVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单子项管理/新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(PaymentFormItemDto.Add.class)  ValidList<PaymentFormItemDto> paymentFormItemDtos) {
        paymentFormItemService.batchAdd(paymentFormItemDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 修改信息
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/付款单子项管理/更新")
    @PostMapping("update")
    public ResponseBo<PaymentFormItemVo> update(@RequestBody  @Validated(PaymentFormItemDto.Update.class)  PaymentFormItemDto paymentFormItemDto) {
        return UpdateResponseBo.ok(paymentFormItemService.update(paymentFormItemDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/付款单子项管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<PaymentFormItemVo> detail(@PathVariable("id") Long id) {
        PaymentFormItemVo data = paymentFormItemService.findPaymentFormItemById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/付款单子项管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        paymentFormItemService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件列表", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单子项管理/查询附件")
    @PostMapping("getMedia")
    public ResponseBo<FMediaAndAttachedVo> getMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = paymentFormItemService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件保存
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件保存")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/付款单子项管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(paymentFormItemService.addMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 获取付款单列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取付款单列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单子项管理/获取付款单列表")
    @PostMapping("getPayFormList")
    public ResponseBo<PaymentFormVo> getPayFormList(@RequestParam("planId") Long planId) {
        List<PaymentFormVo> datas = paymentFormItemService.getPayFormList(planId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @ApiOperation(value = "付款-学生申请方案项目下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/付款单子项管理/付款-学生申请方案项目下拉")
    @PostMapping("getStudentOfferItemSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getStudentOfferItemSelect(@RequestParam(value = "tableName") String tableName,
                                                                  @RequestParam(value = "fkTypeKey") String fkTypeKey,
                                                                  @RequestParam(value = "fkTypeTargetId") Long fkTypeTargetId) {
        return new ListResponseBo<>(paymentFormItemService.getStudentOfferItemSelect(tableName, fkTypeKey, fkTypeTargetId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 获取应付计划数据
     * @Param [typeKey, targetId, formId]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取应付计划数据", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/付款单子项管理/详情")
    @PostMapping("getPayableFormItem")
    public ResponseBo<PaymentFormItemVo> getPayableFormItem(@RequestParam("planId") Long planId,
                                                            @RequestParam("formId") Long formId) {
        PaymentFormItemVo data = paymentFormItemService.getPayableFormItem(planId, formId);
        return new ResponseBo<>(data);
    }

    /**
     * 获取付款单子项
     *
     * @Date 19:01 2021/11/22
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getPayFormListFeign")
    public List<PaymentFormVo> getPayFormListFeign(@RequestParam("planId") Long planId) {
        List<PaymentFormVo> datas = paymentFormItemService.getPayFormList(planId);
        return datas.stream().map(paymentFormDto -> BeanCopyUtils.objClone(paymentFormDto, PaymentFormVo::new)).collect(Collectors.toList());
    }

    /**
     * Author Cream
     * Description : 创建付款单
     * Date 2022/5/7 15:13
     * Params: QuickPaymentFormVo quickPaymentFormVo
     * Return ResponseBo
     */
    @ApiOperation(value = "快速创建付款单")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/应付计划管理/快速创建付款单")
    @PostMapping("/quickCreatePaymentForm")
    public ResponseBo quickCreatePaymentForm(@RequestBody @Validated QuickPaymentFormDto quickPaymentFormDto){
        return paymentFormItemService.quickCreatePaymentForm(quickPaymentFormDto);
    }

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 19:01 2021/11/22
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getPayFormListFeignByPlanIds")
    public List<PaymentFormVo> getPayFormListFeignByPlanIds(@RequestParam("planIds") Set<Long> planIds) {
        return paymentFormItemService.getPayFormListFeignByPlanIds(planIds);
    }


    @ApiOperation(value = "批量找平")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/应付计划管理/批量找平")
    @PostMapping("batchBalancingPaymentForm")
    public ResponseBo batchBalancingPaymentForm(@RequestBody @Validated ValidList<BalancingPaymentFormDto> balancingPaymentFormDtos){
        return paymentFormItemService.batchBalancingPaymentForm(balancingPaymentFormDtos);
    }


}