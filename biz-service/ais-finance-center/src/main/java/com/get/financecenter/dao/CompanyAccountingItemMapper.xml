<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.CompanyAccountingItemMapper">

    <select id="getCompanyAccountingItem" resultType="com.get.financecenter.vo.CompanyAccountingItemVo">
        SELECT
            mcai.id,
            mai.type AS fkBalanceSheetTypeId,
            mcai.fk_company_id AS fkCompanyId,
            mai.code AS accountingItemCode,
            mcai.fk_accounting_item_id AS fkAccountingItemId,
            mcai.amount_dr_opening_balance AS amountDrOpeningBalance,
            mcai.amount_cr_opening_balance AS amountCrOpeningBalance,
            mai.direction AS direction,
            mcai.gmt_create AS gmtCreate,
            mcai.gmt_create_user AS gmtCreateUser,
            mcai.gmt_modified AS gmtModified,
            mcai.gmt_modified_user AS gmtModifiedUser
        FROM m_company_accounting_item mcai
        LEFT JOIN m_accounting_item mai ON mcai.fk_accounting_item_id = mai.id
        WHERE 1=1
        <if test="companyAccountingItemDto.fkCompanyId != null and companyAccountingItemDto.fkCompanyId !=''">
            AND mcai.fk_company_id = #{companyAccountingItemDto.fkCompanyId}
        </if>
        <if test="companyAccountingItemDto.fkBalanceSheetTypeId != null and companyAccountingItemDto.fkBalanceSheetTypeId !=''">
            AND mai.type = #{companyAccountingItemDto.fkBalanceSheetTypeId}
        </if>
        <if test="companyAccountingItemDto.direction != null and companyAccountingItemDto.direction !=''">
            AND mai.direction = #{companyAccountingItemDto.direction}
        </if>
        <if test="companyAccountingItemDto.fkAccountingItemId != null and companyAccountingItemDto.fkAccountingItemId !=''">
            AND mcai.fk_accounting_item_id = #{companyAccountingItemDto.fkAccountingItemId}
        </if>
    </select>

    <select id="getCompanyAccountingDatas" resultType="com.get.financecenter.vo.CompanyAccountingVo">
        SELECT
            mc.id AS fkCompanyId,
            mc.short_name AS companyName,
            COUNT(mcai.id) AS fkAccountingItemCount,
        COALESCE(last_mcai.gmt_modified_user, last_mcai.gmt_create_user) AS gmtModifiedUser,
        COALESCE(last_mcai.gmt_modified, last_mcai.gmt_create) AS gmtModified
        FROM
            ais_permission_center.m_company mc
        INNER JOIN (
             SELECT id FROM ais_permission_center.m_company
                WHERE
                id in
                <foreach item="companyId" index="index" collection="companyIds" separator="," open="(" close=")">
                    #{companyId}
                </foreach>
        ) AS mca ON mca.id = mc.id
        LEFT JOIN m_company_accounting_item mcai ON mc.id = mcai.fk_company_id
        LEFT JOIN (
            SELECT a.*
            FROM m_company_accounting_item a
            INNER JOIN (
                SELECT
                fk_company_id,
                MAX(COALESCE(gmt_modified, gmt_create)) AS max_time
                FROM m_company_accounting_item
                GROUP BY fk_company_id
            ) b ON a.fk_company_id = b.fk_company_id
            AND COALESCE(a.gmt_modified, a.gmt_create) = b.max_time
            INNER JOIN (
                SELECT
                fk_company_id,
                MIN(id) AS min_id
                FROM m_company_accounting_item
                GROUP BY fk_company_id, COALESCE(gmt_modified, gmt_create)
            ) c ON a.fk_company_id = c.fk_company_id AND a.id = c.min_id
        ) last_mcai ON last_mcai.fk_company_id = mc.id
        WHERE 1=1
        <if test="companyAccountingDto.fkCompanyId != null and companyAccountingDto.fkCompanyId !=''">
            AND mc.id = #{companyAccountingDto.fkCompanyId}
        </if>
        GROUP BY
            mc.id,
            mc.short_name,
            gmtModifiedUser,
            gmtModified
        ORDER BY mc.view_order DESC
    </select>

    <select id="selectBatchFkAccountingItemIds" resultType="java.lang.Long">
        SELECT mcai.id
        FROM m_company_accounting_item mcai
        WHERE mcai.fk_company_id = #{fkCompanyId}
        <if test="deleteFkAccountingItemIds != null and deleteFkAccountingItemIds.size() > 0">
            AND mcai.fk_accounting_item_id IN
            <foreach item="fkAccountingItemId" index="index" collection="deleteFkAccountingItemIds" separator="," open="(" close=")">
                #{fkAccountingItemId}
            </foreach>
        </if>
    </select>

    <select id="getCompanyProfitAndLossItemBind" resultType="java.lang.Long">
        SELECT mcai.fk_accounting_item_id
        FROM m_company_accounting_item mcai
        WHERE mcai.fk_company_id = #{fkCompanyId}
    </select>
</mapper>