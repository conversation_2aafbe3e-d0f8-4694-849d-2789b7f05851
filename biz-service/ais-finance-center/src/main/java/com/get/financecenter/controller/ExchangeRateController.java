package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.ExchangeRateVo;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.dto.ExchangeRateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/21 16:52
 * @verison: 1.0
 * @description:
 */
@Api(tags = "汇率管理")
@RestController
@RequestMapping("finance/exchangeRate")
public class ExchangeRateController {
    @Resource
    private IExchangeRateService exchangeRateService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ExchangeRateDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/汇率管理/汇率详情")
    @GetMapping("/{id}")
    public ResponseBo<ExchangeRateVo> detail(@PathVariable("id") Long id) {
        ExchangeRateVo data = exchangeRateService.findExchangeRateById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [exchangeRateVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/汇率管理/新增汇率")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ExchangeRateDto.Add.class)  ValidList<ExchangeRateDto> exchangeRateDtos) {
        exchangeRateService.batchAdd(exchangeRateDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/汇率管理/删除汇率")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        exchangeRateService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ExchangeRateDto>
     * @Description :修改信息
     * @Param [exchangeRateVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/汇率管理/更新汇率")
    @PostMapping("update")
    public ResponseBo<ExchangeRateVo> update(@RequestBody @Validated(ExchangeRateDto.Update.class)  ExchangeRateDto exchangeRateDto) {
        return UpdateResponseBo.ok(exchangeRateService.updateExchangeRate(exchangeRateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ExchangeRateDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/汇率管理/查询汇率")
    @PostMapping("datas")
    public ResponseBo<ExchangeRateVo> datas(@RequestBody SearchBean<ExchangeRateDto> page) {
        List<ExchangeRateVo> datas = exchangeRateService.getExchangeRates(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return void
     * @Description :feign调用 汇率自动批量新增
     * @Param [exchangeRateVos]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("batchAddAuTo")
    public void batchAddAuTo(@RequestBody List<ExchangeRateDto> exchangeRateDtos) {
        exchangeRateService.batchAddAuTo(exchangeRateDtos);
    }

    /**
     * @return
     * @Description :获取汇率
     * @Param
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @PostMapping("getExchangeRate")
    public BigDecimal getExchangeRate(@RequestParam("fromCurrency") String fromCurrency, @RequestParam("toCurrency") String toCurrency) {
        return exchangeRateService.getRateByCurrency(fromCurrency, toCurrency);
    }


    /**
     * 保存汇率
     *
     * @param exchangeRateDto
     * @return
     */
    @ApiIgnore
    @PostMapping("add")
    public ResponseBo addExchangeRate(@RequestBody  @Validated(ExchangeRateDto.Add.class) ExchangeRateDto exchangeRateDto) {
        return SaveResponseBo.ok(exchangeRateService.addExchangeRate(exchangeRateDto));
    }

    /**
     * 获取最新汇率
     *
     * @param
     * @return
     */
    @ApiIgnore
    @PostMapping("getLastExchangeRate")
    public BigDecimal getLastExchangeRate(@RequestParam("last") Boolean last, @RequestParam("fromCurrency") String fromCurrency, @RequestParam("toCurrency") String toCurrency) {
        ExchangeRateVo data = exchangeRateService.getLastExchangeRate(last, fromCurrency, toCurrency);
        return data.getExchangeRate();
    }

    /**
     * 获取最新汇率
     *
     * @param
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取当天汇率", notes = "获取当天汇率")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/汇率管理/获取当天汇率")
    @PostMapping("getCurrentExchangeRate")
    public ResponseBo getCurrentExchangeRate(@RequestParam("fromCurrency") String fromCurrency, @RequestParam("toCurrency") String toCurrency) {
        ExchangeRateVo data = exchangeRateService.getLastExchangeRate(false, fromCurrency, toCurrency);
        return new ResponseBo<>(data);
    }


}
