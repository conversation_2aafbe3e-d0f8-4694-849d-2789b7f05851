package com.get.financecenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公司损益表项模板控制层
 */
@Api(tags = "公司损益表项模板管理")
@RestController
@RequestMapping("finance/CompanyProfitAndLossTemplate")
public class CompanyProfitAndLossTemplateController extends ApiController {

}

