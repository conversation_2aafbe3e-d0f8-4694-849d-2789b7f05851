package com.get.financecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.financecenter.service.AsyncReminderService;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

@Service
@Slf4j
public class AsyncReminderServiceImpl implements AsyncReminderService {

    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;

    /**
     * 发送发票下学习计划类型应收的邮件通知
     * @param offerItem
     * @param commissionNotice
     */
    @Async
    @Override
    public void sendInvoiceCommissionEmail(StudentOfferItem offerItem, String commissionNotice,Map<String, String> headerMap) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        String countryName = institutionCenterClient.getCountryNameById(offerItem.getFkAreaCountryId()).getData();
        String courseName = "";
        if (-1 == offerItem.getFkInstitutionCourseId()) {
            courseName = offerItem.getOldCourseCustomName();
        }else {
            courseName = institutionCenterClient.getCourseNameById(offerItem.getFkInstitutionCourseId()).getData();
        }
        Result<List<StudentOfferItem>> studentOfferItemByParentId = saleCenterClient.getStudentOfferItemByParentId(offerItem.getId());
        if (GeneralTool.isNotEmpty(studentOfferItemByParentId.getData())){
            for (StudentOfferItem studentOfferItem : studentOfferItemByParentId.getData()) {
                if (!studentOfferItem.getIsFollowHidden()){
                    Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
                    if (courseById.getData().getName()!=null){
                        courseName=courseName+" + "+courseById.getData().getName();
                    }
                }
            }
        }
        String institutionName = institutionCenterClient.getInstitutionName(offerItem.getFkInstitutionId()).getData();
        String studentName = saleCenterClient.getStudentNameById(offerItem.getFkStudentId()).getData();
        StudentOfferVo studentOfferVo = saleCenterClient.getStudentOfferDetail(offerItem.getFkStudentOfferId()).getData();
        List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentOfferVo.getStudentProjectRoleStaffDtos();
        Set<Long> staffIds = projectRoleStaffDtos.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        staffIds.add(studentOfferVo.getFkStaffId());
        List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
        List<Map<String,String>> list = new ArrayList<>();
//        for (StaffVo staffById : staffByIds) {
//            Map<String,String> map = new HashMap<>();
//            map.put("email",staffById.getEmail());
//            map.put("commissionNotice",commissionNotice);
//            map.put("studentName",studentName);
//            map.put("countryName",countryName);
//            map.put("fkInstitutionName",institutionName);
//            map.put("fkCourseName",courseName);
//            //TODO REMINDER_COMMISSION_NOTICE 邮件
//            map.put("title","申请计划佣金通知");
//            map.put("openTime",new SimpleDateFormat("yyyy-MM-dd").format(offerItem.getDeferOpeningTime()));
//            list.add(map);
//        }

            Map<String,String> param = new HashMap<>();
        param.put("commissionNotice",commissionNotice);
        param.put("studentName",studentName);
        param.put("countryName",countryName);
        param.put("fkInstitutionName",institutionName);
        param.put("fkCourseName",courseName);
        param.put("openTime",new SimpleDateFormat("yyyy-MM-dd").format(offerItem.getDeferOpeningTime()));
        param.put("staffIdList",staffIds.toString());
            String map = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
            map = mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.REMINDER_COMMISSION_NOTICE.getEmailTemplateKey());
        emailSenderQueue.setOperationTime(now());
        emailSenderQueue.setFkTableId(offerItem.getId());
        emailSenderQueue.setEmailParameter(map);
        emailSenderQueueList.add(emailSenderQueue);
        Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (!bolleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
        }
        //reminderCenterClient.batchSendEmail(list, ProjectKeyEnum.REMINDER_COMMISSION_NOTICE.key);
    }

}
