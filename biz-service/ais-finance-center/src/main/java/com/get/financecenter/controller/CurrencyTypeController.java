package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.dto.CurrencyTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/9/17 11:46
 * @verison: 1.0
 * @description:
 */
@Api(tags = "货币类型管理")
@RestController
@RequestMapping("finance/currencyType")
public class CurrencyTypeController {

    @Resource
    private ICurrencyTypeService currencyTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.CurrencyTypeDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/货币类型管理/货币类型详情")
    @GetMapping("/{id}")
    public ResponseBo<CurrencyTypeVo> detail(@PathVariable("id") Long id) {
        CurrencyTypeVo data = currencyTypeService.findCurrencyTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [currencyTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/货币类型管理/新增货币类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(CurrencyTypeDto.Add.class)   ValidList<CurrencyTypeDto> currencyTypeDtos) {
        currencyTypeService.batchAdd(currencyTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/货币类型管理/删除货币类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        currencyTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.CurrencyTypeDto>
     * @Description :修改信息
     * @Param [currencyTypeVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/货币类型管理/更新货币类型")
    @PostMapping("update")
    public ResponseBo<CurrencyTypeVo> update(@RequestBody @Validated(CurrencyTypeDto.Update.class)  CurrencyTypeDto currencyTypeDto) {
        return UpdateResponseBo.ok(currencyTypeService.updateCurrencyType(currencyTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.CurrencyTypeDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord 编号/币种名称 搜索关键字")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/货币类型管理/查询货币类型")
    @PostMapping("datas")
    public ResponseBo<CurrencyTypeVo> datas(@RequestBody SearchBean<CurrencyTypeDto> page) {
        List<CurrencyTypeVo> datas = currencyTypeService.getCurrencyTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [currencyTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/货币类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<CurrencyTypeDto> currencyTypeDtos) {
        currencyTypeService.movingOrder(currencyTypeDtos);
        return ResponseBo.ok();
    }


    /**
     * 货币类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "货币类型下拉框数据", notes = "id货币类型id  num货币类型编号 typeName货币类型名称")
    @GetMapping("getCurrencyTypeList")
    public ResponseBo<CurrencyTypeVo> getCurrencyTypeList() {
        List<CurrencyTypeVo> datas = currencyTypeService.getCurrencyTypeList();
        return new ListResponseBo<>(datas);
    }

    /**
     * 货币类型下拉框数据（不包含外币）
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "货币类型下拉框数据（不包含外币）", notes = "id货币类型id  num货币类型编号 typeName货币类型名称")
    @GetMapping("getCurrencyTypeSelect")
    public ResponseBo<CurrencyTypeVo> getCurrencyTypeSelect() {
        List<CurrencyTypeVo> datas = currencyTypeService.getCurrencyTypeSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return java.lang.String
     * @Description :feign调用 通过币种编号 查找对应的币种名称
     * @Param [num]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping(value = "getCurrencyNameByNum")
    public String getCurrencyNameByNum(@RequestParam(required = false) String num) {
        return currencyTypeService.getCurrencyNameByNum(num);
    }

    /**
     * @Description :feign调用 根据币种编号nums查找币种名称map
     * @Param [nums]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping(value = "getCurrencyNamesByNums")
    public Map<String, String> getCurrencyNamesByNums(@RequestBody Set<String> nums) {
        return currencyTypeService.getCurrencyNamesByNums(nums);
    }

    /**
     * @Description: feign调用 根据币种编号nums查找币种名称map（只返回名称）
     * @Author: Jerry
     * @Date:11:47 2021/11/20
     */
    @ApiIgnore
    @PostMapping(value = "getNewCurrencyNamesByNums")
    public Map<String, String> getNewCurrencyNamesByNums(@RequestBody Set<String> nums) {
        return currencyTypeService.getNewCurrencyNamesByNums(nums);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "常用币种下拉", notes = "ProjectExtraEnum.PUBLIC_CURRENCY_COMMON")
    @GetMapping("getCommonCurrencyTypeSelect")
    public ResponseBo<BaseSelectEntity> getCommonCurrencyTypeSelect() {
        List<CurrencyTypeVo> result = currencyTypeService.getCurrencyByPublicLevel(ProjectExtraEnum.PUBLIC_CURRENCY_COMMON.key);
        return new ListResponseBo<>(
                result.stream().map(c -> {
                    BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                    baseSelectEntity.setId(c.getId());
                    baseSelectEntity.setNum(c.getNum());
                    baseSelectEntity.setName(c.getTypeName());
                    baseSelectEntity.setFullName(c.getTypeName() + "（" + c.getNum() + "）");
                    return baseSelectEntity;
                }).collect(Collectors.toList()));
    }
}
