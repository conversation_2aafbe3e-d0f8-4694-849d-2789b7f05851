package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.CompanyAccountingDto;
import com.get.financecenter.dto.CompanyAccountingItemDto;
import com.get.financecenter.dto.CompanyAccountingItemOperateDto;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import com.get.financecenter.vo.CompanyAccountingVo;
import java.util.List;
import javax.validation.Valid;

/**
 * 科目账套
 */
public interface ICompanyAccountingItemService {

    void delete(Long id);

    Integer updateById(CompanyAccountingItemDto companyAccountingItemDto);

    Integer save(CompanyAccountingItemDto companyAccountingItemDto);

    List<CompanyAccountingItemVo> getCompanyAccountingItem(@Valid CompanyAccountingItemDto companyAccountingItemDto, Page page);

    List<CompanyAccountingVo> getCompanyAccountingDatas(@Valid CompanyAccountingDto companyAccountingDto, Page page);

    void batchOperation(CompanyAccountingItemOperateDto companyAccountingItemOperateDto);

    List<Long> getCompanyProfitAndLossItemBind(java.lang.Long fkCompanyId);
}

