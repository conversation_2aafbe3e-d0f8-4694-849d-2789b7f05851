package com.get.financecenter.service;

import com.get.common.result.ListResponseBo;
import com.get.common.result.SearchBean;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.financecenter.dto.SettlementInstallmentBatchUpdateDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.salecenter.dto.SettlementCommissionNoticeDto;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.vo.SaleReceiptFormItemVo;
import com.get.salecenter.vo.SettlementCommissionNoticeVo;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/2/27 11:10
 */
public interface HtiAgencyCommissionSettlementService {

    /**
     * 财务佣金汇总列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    ListResponseBo<CommissionSummaryVo> commissionSummary(SearchBean<CommissionSummaryDto> page);


    /**
     * 佣金结算第四步导出
     * @param commissionSummaryDto
     * @param response
     */
    void agentSettlementListFourthStepExport(CommissionSummaryDto commissionSummaryDto, HttpServletResponse response);

    /**
     * 根据应付计划ids 获取结算标记
     *
     * @Date 12:07 2022/1/11
     * <AUTHOR>
     */
    Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(List<Long> payablePlanIds);

    /**
     * 检查银行账户是否存在佣金数据
     * @param agentAccountId
     * @return
     */
    Boolean getCommissionSettlementAccountInfo(Long agentAccountId);

    /**
     * 佣金结算更新
     * @param settlementInstallmentUpdateDto
     * @return
     */
    Boolean settlementInstallmentUpdate(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto);

    /**
     * 检查应付状态，没有结算就返回true
     * @param payablePlanIds
     * @return
     */
    Boolean checkPayableInfo(Set<Long> payablePlanIds);

    /**
     * 插入应付计划结算分期表
     *
     * @Date 14:52 2022/3/30
     * <AUTHOR>
     */
    Boolean insertSettlementInstallment(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto, ReceiptFormItemVo receiptFormItemVo);

    /**
     * 批量更新、插入 分期表数据
     * @param settlementInstallmentBatchUpdateDtos
     * @return
     */
    Boolean insertSettlementInstallmentBatch(List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos);

    /**
     * 作废应付时，删除佣金结算数据
     * @param payablePlanId
     * @return
     */
    Boolean deleteSettlementCommissionByPayablePlanId(Long payablePlanId);

    /**
     * 应付计划一键结算按钮
     * @param oneClickSettlementDto
     * @return
     */
    Boolean oneClickSettlement(OneClickSettlementDto oneClickSettlementDto);

    /**
     * 获取待结算标记代理ids
     * @return
     */
    List<Long> getSettlementFlagAgentIds();

    /**
     * 更新结算通知信息
     * @param settlementCommissionNoticeDto
     * @return
     */
    void updateSettlementNotificationInformation(SettlementCommissionNoticeDto settlementCommissionNoticeDto);

    /**
     * 结算通知信息详情
     * @param id
     * @return
     */
    SettlementCommissionNoticeVo settlementNotificationInformationDetails(Long id);

    /**
     * 入学失败，需要删除对应的未结算完的佣金数据
     * @return
     */
    Boolean deleteUnsettledCommissionByAdmissionFailure(List<Long> payablePlanIdSet);
}
