package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.CreateVouchDto;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.service.VouchService;
import com.get.financecenter.vo.VouchTypeVo;
import com.get.financecenter.vo.VouchVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "凭证控制类")
@RestController
@RequestMapping("finance/vouch")
public class VouchController {

    @Resource
    private VouchService vouchService;

    @ApiOperation("生成凭证")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/申请表单管理/生成凭证")
    @PostMapping("/createVouch")
    @RedisLock(value = "fzh:createVouch", waitTime = 10L)
    public ResponseBo createVouch(@RequestBody @Validated CreateVouchDto createVouchDto) {
        vouchService.createVouch(createVouchDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "凭证类型下拉",  notes = "凭证类型：转/现收/现付/银收/银付")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/凭证管理/凭证类型下拉")
    @PostMapping("/vouchType")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<VouchTypeVo>  getVouchType() {
        return new ListResponseBo<>(vouchService.getVouchType());
    }


    @ApiOperation(value = "获取列表数据",  notes = "凭证列表数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/凭证管理/获取列表数据")
    @PostMapping("datas")
    public ResponseBo<VouchVo> selectVouchs(@RequestBody SearchBean<VouchDto> page) {
        List<VouchVo> datas = vouchService.getVouchs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "根据id获取凭证和凭证详细")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/凭证管理/根据id获取凭证和凭证详细")
    @GetMapping("getVouchById")
    public ResponseBo<VouchVo> getVouchById(@RequestParam("id") Long id) {
        return new ResponseBo<>(vouchService.getVouchsById(id));
    }

    @ApiOperation(value = "新增接口", notes = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/凭证管理/新增接口")
    @PostMapping("add")
    @RedisLock(value = "fzh:createVouch", waitTime = 10L)
    public ResponseBo<Long> add(@RequestBody @Validated VouchDto vouchDto) {
        return new ResponseBo<>(vouchService.add(vouchDto));
    }

//    @ApiOperation(value = "删除接口", notes = "删除接口")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/凭证管理/删除接口")
//    @GetMapping("delete")
//    public ResponseBo<Long> delete(@RequestParam("id")Long id) {
//        vouchService.delete(id);
//        return new ResponseBo<>(id);
//    }

    @ApiOperation(value = "修改接口", notes = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/凭证管理/修改接口")
    @GetMapping("cancel")
    public ResponseBo<VouchVo> cancel(@RequestParam("id")Long id) {
        return new ResponseBo<>(vouchService.updateById(id));
    }

}
