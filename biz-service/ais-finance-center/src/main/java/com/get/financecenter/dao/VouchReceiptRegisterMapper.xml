<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.VouchReceiptRegisterMapper">

    <select id="getVouchReceiptRegister" resultType="com.get.financecenter.vo.VouchReceiptRegisterVo">
        SELECT mvrr.id,
               mvrr.fk_vouch_id,
               mvrr.fk_receipt_method_type_id,
               urmt.type_name AS receiptMethodTypeName,
               mvrr.fk_accounting_item_id_receipt_method,
               mvrr.relation_target_key_receipt_method,
               mvrr.relation_target_id_receipt_method,
               mvrr.fk_receipt_fee_type_id,
               urft.type_name AS receiptFeeTypeName,
               mvrr.summary,
               mvrr.fk_company_id,
               mvrr.fk_department_id,
               mvrr.relation_target_key_receipt_fee,
               mvrr.relation_target_id_receipt_fee,
               mvrr.fk_currency_type_num,
               mvrr.receipt_amount,
               mvrr.receipt_date,
               mvrr.status,
               mvrr.gmt_create,
               mvrr.gmt_create_user,
               mvrr.gmt_modified,
               mvrr.gmt_modified_user,
               mv.vouch_num AS vouchNumber
        FROM m_vouch_receipt_register mvrr
        LEFT JOIN m_vouch mv ON mv.id = mvrr.fk_vouch_id
        LEFT JOIN u_receipt_method_type urmt ON mvrr.fk_receipt_method_type_id = urmt.id
        LEFT JOIN u_receipt_fee_type urft ON mvrr.fk_receipt_fee_type_id = urft.id
        WHERE 1 = 1
        <if test="vouchReceiptRegisterDto.fkReceiptMethodTypeId != null and vouchReceiptRegisterDto.fkReceiptMethodTypeId != ''">
            AND mvrr.fk_receipt_method_type_id = #{vouchReceiptRegisterDto.fkReceiptMethodTypeId}
        </if>
        <if test="vouchReceiptRegisterDto.fkReceiptFeeTypeId != null and vouchReceiptRegisterDto.fkReceiptFeeTypeId != ''">
            AND mvrr.fk_receipt_fee_type_id = #{vouchReceiptRegisterDto.fkReceiptFeeTypeId}
        </if>
        <if test="vouchReceiptRegisterDto.relationTargetIdReceiptMethod != null and vouchReceiptRegisterDto.relationTargetIdReceiptMethod != ''">
            AND mvrr.fk_receipt_fee_type_id = #{vouchReceiptRegisterDto.fkReceiptFeeTypeId}
        </if>
        <if test="vouchReceiptRegisterDto.vouchNumber != null and vouchReceiptRegisterDto.vouchNumber != ''">
            AND mv.vouch_num LIKE CONCAT(CONCAT('%', #{vouchReceiptRegisterDto.vouchNumber}), '%')
        </if>
        <if test="vouchReceiptRegisterDto.fkCompanyId != null and vouchReceiptRegisterDto.fkCompanyId != ''">
            AND mvrr.fk_company_id = #{vouchReceiptRegisterDto.fkCompanyId}
        </if>
        <if test="vouchReceiptRegisterDto.fkDepartmentId != null and vouchReceiptRegisterDto.fkDepartmentId != ''">
            AND mvrr.fk_department_id = #{vouchReceiptRegisterDto.fkDepartmentId}
        </if>
        <if test="vouchReceiptRegisterDto.status != null">
            AND mvrr.status = #{vouchReceiptRegisterDto.status}
        </if>
        <if test="vouchReceiptRegisterDto.receiptDate != null">
            AND DATE_FORMAT(mvrr.receipt_date, '%Y-%m-%d') = DATE_FORMAT(#{vouchReceiptRegisterDto.receiptDate}, '%Y-%m-%d')
        </if>

        ORDER BY mvrr.status DESC,mvrr.gmt_create DESC
    </select>
</mapper>