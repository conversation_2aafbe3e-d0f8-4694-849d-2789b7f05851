<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ExpenseClaimFormItemMapper">

  <select id="getFormItemByExpenseClaimFormId" resultType="com.get.financecenter.entity.ExpenseClaimFormItem">
    select * from m_expense_claim_form_item where fk_expense_claim_form_id=#{id}
  </select>
    <select id="getExpenseClaimFormTotalAmount" resultType="java.math.BigDecimal">
      select IFNULL(sum(amount),0) from m_expense_claim_form_item where fk_expense_claim_form_id=#{id}
    </select>
</mapper>