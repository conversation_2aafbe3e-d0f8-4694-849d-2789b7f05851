package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.ReceiptFeeTypeDto;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptFeeTypeVo;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/12/6
 * @TIME: 15:58
 * @Description:
 **/
public interface IReceiptFeeTypeService {
    /**
     * 收款费用类型列表
     *
     * @param receiptFeeTypeDto
     * @param page
     * @return
     */
    List<ReceiptFeeTypeVo> getReceiptFeeTypes(ReceiptFeeTypeDto receiptFeeTypeDto, Page page);

    /**
     * 收款费用类型详情
     *
     * @param id
     * @return
     */
    ReceiptFeeTypeVo findReceiptFeeTypeById(Long id);


    /**
     * 批量新增
     *
     * @param receiptFeeTypeDtos
     */
    void batchAdd(List<ReceiptFeeTypeDto> receiptFeeTypeDtos);

    /**
     * 新增收款费用类型下拉
     *
     * @param receiptFeeTypeDto
     * @return
     */
    Long addReceiptFeeType(ReceiptFeeTypeDto receiptFeeTypeDto);

    /**
     * 删除收款费用类型
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改收款费用类型
     *
     * @param receiptFeeTypeDto
     * @return
     */
    ReceiptFeeTypeVo updateReceiptFeeType(ReceiptFeeTypeDto receiptFeeTypeDto);

    /**
     * 上移下移
     *
     * @param receiptFeeTypeDtos
     */
    void sort(List<ReceiptFeeTypeDto> receiptFeeTypeDtos);

    /**
     * 收款费用类型下拉
     *
     * @return
     */
    List<BaseSelectVo> getReceiptFeeTypeSelect();


    List<ReceiptFeeTypeVo> getReceiptFeeTypeGroup();

    void movingOrder(Integer start, Integer end);
}
