package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderAccountMapper;
import com.get.financecenter.dto.ProviderAccountDto;
import com.get.financecenter.entity.ProviderAccount;
import com.get.financecenter.service.IProviderAccountService;
import com.get.financecenter.vo.ProviderAccountVo;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @author: Sea
 * @create: 2020/12/28 10:53
 * @verison: 1.0
 * @description:
 */
@Service
public class ProviderAccountServiceImpl extends BaseServiceImpl<ProviderAccountMapper, ProviderAccount> implements IProviderAccountService {
    @Resource
    private ProviderAccountMapper providerAccountMapper;
    @Resource
    private UtilService utilService;

    @Override
    public ProviderAccountVo findProviderAccountById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ProviderAccount providerAccount = providerAccountMapper.selectById(id);
        if (GeneralTool.isEmpty(providerAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(providerAccount, ProviderAccountVo::new);
    }

    @Override
    public Long addProviderAccount(ProviderAccountDto providerAccountDto) {
        if (GeneralTool.isEmpty(providerAccountDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ProviderAccount providerAccount = BeanCopyUtils.objClone(providerAccountDto, ProviderAccount::new);
        if (validateAdd(providerAccountDto)) {
            utilService.updateUserInfoToEntity(providerAccount);
            providerAccountMapper.insert(providerAccount);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return providerAccount.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (providerAccountMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        providerAccountMapper.deleteById(id);
    }

    @Override
    public ProviderAccountVo updateProviderAccount(ProviderAccountDto providerAccountDto) {
        if (providerAccountDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ProviderAccount result = providerAccountMapper.selectById(providerAccountDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ProviderAccount providerAccount = BeanCopyUtils.objClone(providerAccountDto, ProviderAccount::new);
        LambdaUpdateWrapper<ProviderAccount> updateWrapper = new LambdaUpdateWrapper<>();
        if (validateUpdate(providerAccountDto)) {
            utilService.updateUserInfoToEntity(providerAccount);
            if (GeneralTool.isEmpty(providerAccount.getRemark())){
                updateWrapper.set(ProviderAccount::getRemark, null);
            }

            updateWrapper.eq(ProviderAccount::getId, providerAccount.getId());
            providerAccountMapper.update(providerAccount,updateWrapper);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findProviderAccountById(providerAccountDto.getId());
    }

    @Override
    public List<ProviderAccountVo> getProviderAccounts(ProviderAccountDto providerAccountDto, Page page) {
//        Example example = new Example(ProviderAccount::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerAccountVo.getFkProviderId());
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ProviderAccount> providerAccounts = providerAccountMapper.selectByExample(example);
//        page.restPage(providerAccounts);
        LambdaQueryWrapper<ProviderAccount> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(providerAccountDto.getFkProviderId())) {
            wrapper.eq(ProviderAccount::getFkProviderId, providerAccountDto.getFkProviderId());
        }
        IPage<ProviderAccount> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ProviderAccount> providerAccounts = pages.getRecords();
        return providerAccounts.stream().map(providerAccount -> BeanCopyUtils.objClone(providerAccount, ProviderAccountVo::new)).collect(Collectors.toList());
    }

    @Override
    public void isActive(Long providerAccountId, Boolean isActive) {
        if (GeneralTool.isEmpty(providerAccountId) || GeneralTool.isEmpty(providerAccountMapper.selectById(providerAccountId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ProviderAccount providerAccount = new ProviderAccount();
        providerAccount.setId(providerAccountId);
        providerAccount.setIsActive(isActive);
        providerAccountMapper.updateById(providerAccount);
    }

    private boolean validateAdd(ProviderAccountDto providerAccountDto) {
//        Example example = new Example(ProviderAccount::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerAccountVo.getFkProviderId());
//        criteria.andEqualTo("fkCurrencyTypeNum", providerAccountVo.getFkCurrencyTypeNum());
//        List<ProviderAccount> list = this.providerAccountMapper.selectByExample(example);
        List<ProviderAccount> list = this.providerAccountMapper.selectList(Wrappers.<ProviderAccount>query().lambda()
                .eq(ProviderAccount::getFkProviderId, providerAccountDto.getFkProviderId())
                .eq(ProviderAccount::getFkCurrencyTypeNum, providerAccountDto.getFkCurrencyTypeNum()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ProviderAccountDto providerAccountDto) {
//        Example example = new Example(ProviderAccount::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerAccountVo.getFkProviderId());
//        criteria.andEqualTo("fkCurrencyTypeNum", providerAccountVo.getFkCurrencyTypeNum());
//        List<ProviderAccount> list = this.providerAccountMapper.selectByExample(example);

        List<ProviderAccount> list = this.providerAccountMapper.selectList(Wrappers.<ProviderAccount>query().lambda()
                .eq(ProviderAccount::getFkProviderId, providerAccountDto.getFkProviderId())
                .eq(ProviderAccount::getFkCurrencyTypeNum, providerAccountDto.getFkCurrencyTypeNum()));
        return list.size() <= 0 || list.get(0).getId().equals(providerAccountDto.getId());
    }
}
