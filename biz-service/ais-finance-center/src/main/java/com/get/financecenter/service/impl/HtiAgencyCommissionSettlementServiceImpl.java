package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dao.PayablePlanSettlementAgentAccountMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.PayablePlanSettlementStatusMapper;
import com.get.financecenter.dao.PayableSettlementMessageMapper;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.RPayablePlanSettlementFlagMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.financecenter.dto.SettlementInstallmentBatchUpdateDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.PayablePlanSettlementAgentAccount;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.entity.PayablePlanSettlementStatus;
import com.get.financecenter.entity.RPayablePlanSettlementFlag;
import com.get.financecenter.entity.SettlementCommissionNotice;
import com.get.financecenter.service.AgencyCommissionSettlementService;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IInvoiceReceivablePlanService;
import com.get.financecenter.service.IPaymentFormItemService;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.SettlementCommissionNoticeDto;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.HtiCommissionSummaryExportVo;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.SaleReceiptFormItemVo;
import com.get.salecenter.vo.SettlementCommissionNoticeVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/27 11:10
 */
@Service
public class HtiAgencyCommissionSettlementServiceImpl implements HtiAgencyCommissionSettlementService {
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private PayablePlanSettlementAgentAccountMapper payablePlanSettlementAgentAccountMapper;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private ReceiptFormItemMapper receiptFormItemMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IInvoiceReceivablePlanService iInvoiceReceivablePlanService;
    @Resource
    private IPaymentFormItemService paymentFormItemService;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;
    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;
    @Resource
    private RPayablePlanSettlementFlagMapper payablePlanSettlementFlagMapper;
    @Resource
    private PayableSettlementMessageMapper payableSettlementMessageMapper;

    /**
     * 财务佣金汇总列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    @Override
    public ListResponseBo<CommissionSummaryVo> commissionSummary(SearchBean<CommissionSummaryDto> page) {
        Result<CommissionSummaryPageVo> result = saleCenterClient.commissionSummary(page);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        Page p = new Page();
        if (GeneralTool.isEmpty(result.getData())) {
            return new ListResponseBo<>(null, p);
        }
        CommissionSummaryPageVo commissionSummaryPageVo = result.getData();
        List<CommissionSummaryVo> commissionSummaryVos = commissionSummaryPageVo.getCommissionSummaryDtoList();
        p = commissionSummaryPageVo.getPage();
        for (CommissionSummaryVo commissionSummaryVo : commissionSummaryVos) {
            if (GeneralTool.isNotEmpty(commissionSummaryVo.getPayableAmount())) {
                commissionSummaryVo.setPayableAmount(commissionSummaryVo.getPayableAmount().setScale(2, RoundingMode.HALF_UP));
            }
            commissionSummaryVo.setRmbRate(exchangeRateService.getLastExchangeRate(false, commissionSummaryVo.getPlanCurrencyNum(),
                    "CNY").getExchangeRate().setScale(4, BigDecimal.ROUND_HALF_UP));
            commissionSummaryVo.setRmbAmount(commissionSummaryVo.getAmountActual().multiply(commissionSummaryVo.getRmbRate()).setScale(2, RoundingMode.HALF_UP));
            commissionSummaryVo.setTransferPurpose("往来款");
            commissionSummaryVo.setFkTypeKeyName(TableEnum.getValueByKey(commissionSummaryVo.getFkTypeKey(), TableEnum.COMMISSION_SETTLEMENT_TYPE));
        }

        return new ListResponseBo<>(commissionSummaryVos, p);
    }

    /**
     * Author Cream
     * Description : //佣金结算第四步导出
     * Date 2023/9/6 14:08
     * Params:
     * Return
     * @param commissionSummaryDto1
     * @param response
     */
    @Override
    public void agentSettlementListFourthStepExport(CommissionSummaryDto commissionSummaryDto1, HttpServletResponse response) {
        SearchBean<CommissionSummaryDto> searchBean = new SearchBean<>();
        searchBean.setData(commissionSummaryDto1);
        searchBean.setCurrentPage(1);
        searchBean.setShowCount(50000);
        List<HtiCommissionSummaryExportVo> commissionSummaryExportDtoList = new ArrayList<>();
        //查询数据
        List<CommissionSummaryVo> commissionSummaryVoList = commissionSummary(searchBean).getDatas();
        if (GeneralTool.isNotEmpty(commissionSummaryVoList)){
            //导出数据封装
            commissionSummaryVoList.forEach(commissionSummaryDto -> {
                HtiCommissionSummaryExportVo commissionSummaryExportDto = new HtiCommissionSummaryExportVo();
                BeanUtils.copyProperties(commissionSummaryDto, commissionSummaryExportDto);
                Boolean lockFlag = commissionSummaryDto.getLockFlag();
                if (GeneralTool.isNotEmpty(lockFlag)) {
                    commissionSummaryExportDto.setLock(lockFlag ? "是" : "否");
                }
                if (GeneralTool.isNotEmpty(commissionSummaryDto.getBankBranchName())) {
                    commissionSummaryExportDto.setBankName(commissionSummaryExportDto.getBankName() + "(" + commissionSummaryDto.getBankBranchName() + ")");
                }
                commissionSummaryExportDtoList.add(commissionSummaryExportDto);
            });
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("export_empty"));
        }
        FileUtils.exportExcel(response,commissionSummaryExportDtoList,"agentSettlementListFourthStepExport", HtiCommissionSummaryExportVo.class);
    }

    @Override
    public Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(List<Long> payablePlanIds) {
        LambdaQueryWrapper<PayablePlanSettlementAgentAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlanIds);
        lambdaQueryWrapper.isNull(PayablePlanSettlementAgentAccount::getNumSettlementBatch);
        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(lambdaQueryWrapper);

        Map<Long, List<PayablePlanSettlementAgentAccountVo>> map = new HashMap<>();
        Set<String> currencyTypeNum = payablePlanSettlementAgentAccounts.stream().map(PayablePlanSettlementAgentAccount::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNum);
//        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNum);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            currencyNamesByNums = result.getData();
//        }
        for (PayablePlanSettlementAgentAccount payablePlanSettlementAgentAccount : payablePlanSettlementAgentAccounts) {
            List<PayablePlanSettlementAgentAccountVo> payablePlanSettlementAgentAccountVos = map.get(payablePlanSettlementAgentAccount.getFkPayablePlanId());
            if (GeneralTool.isEmpty(payablePlanSettlementAgentAccountVos)) {
                payablePlanSettlementAgentAccountVos = new ArrayList<>();
            }
            PayablePlanSettlementAgentAccountVo studentOfferItemSettlementAgentAccountDto = new PayablePlanSettlementAgentAccountVo();
            BeanCopyUtils.copyProperties(payablePlanSettlementAgentAccount, studentOfferItemSettlementAgentAccountDto);
            studentOfferItemSettlementAgentAccountDto.setCurrencyTypeName(currencyNamesByNums.get(studentOfferItemSettlementAgentAccountDto.getFkCurrencyTypeNum()));
            payablePlanSettlementAgentAccountVos.add(studentOfferItemSettlementAgentAccountDto);
            map.put(payablePlanSettlementAgentAccount.getFkPayablePlanId(), payablePlanSettlementAgentAccountVos);
        }
        return map;
    }

    /**
     * 检查银行账户是否存在佣金数据
     * @param agentAccountId
     * @return
     */
    @Override
    public Boolean getCommissionSettlementAccountInfo(Long agentAccountId) {
        List<PayablePlanSettlementAgentAccount> payablePlanSettlementAgentAccounts = payablePlanSettlementAgentAccountMapper.selectList(Wrappers.<PayablePlanSettlementAgentAccount>lambdaQuery().eq(PayablePlanSettlementAgentAccount::getFkAgentContractAccountId, agentAccountId));
        return GeneralTool.isNotEmpty(payablePlanSettlementAgentAccounts);
    }

    /**
     * 佣金结算更新
     * @param settlementInstallmentUpdateDto
     * @return
     */
    @Override
    public Boolean settlementInstallmentUpdate(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto) {
        //查找对应的应收计划未结算过的收款单子单
        List<ReceiptFormItemVo> receiptFormItemList = receiptFormItemMapper.getSettlementReceiptFormItemListByPlanId(settlementInstallmentUpdateDto.getFkReceivablePlanId());
        if (GeneralTool.isNotEmpty(receiptFormItemList)) {
            List<Long> receiptFormItemId = receiptFormItemList.stream().map(ReceiptFormItemVo::getId).collect(Collectors.toList());
            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementInstallmentUpdateDto.getFkPayablePlanId())
                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                    .in(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemId));
            //插入应付计划结算分期表
            for (ReceiptFormItemVo receiptFormItemVo : receiptFormItemList) {
//                //旧数据不进入结算
//                if ("admin-1".equals(receiptFormItemVo.getGmtCreateUser())) {
//                    continue;
//                }
                insertSettlementInstallment(settlementInstallmentUpdateDto, receiptFormItemVo);
            }
        }

        //预付重新生成
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
                .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementInstallmentUpdateDto.getFkPayablePlanId())
                .isNull(PayablePlanSettlementInstallment::getFkReceiptFormItemId));
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
            List<Long> ids = payablePlanSettlementInstallments.stream().map(PayablePlanSettlementInstallment::getId).collect(Collectors.toList());
            payablePlanSettlementInstallmentMapper.deleteBatchIds(ids);

            BigDecimal serviceFee = getPrepaidHandlingFees(settlementInstallmentUpdateDto.getFkCompanyId());

            List<PayablePlanSettlementInstallment> collect = payablePlanSettlementInstallments.stream().filter(payablePlanSettlementInstallment -> payablePlanSettlementInstallment.getFkInvoiceReceivablePlanId() != null).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(collect)) {
                List<Long> fkInvoiceReceivablePlanIds = collect.stream().map(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId).collect(Collectors.toList());
                Map<Long, InvoiceReceivablePlan> data = iInvoiceReceivablePlanService.getInvoiceAmountByIds(fkInvoiceReceivablePlanIds);
                //应收金额
                BigDecimal receivableAmount = settlementInstallmentUpdateDto.getReceivableAmount();
                //应收币种
                String fkCurrencyTypeNum = settlementInstallmentUpdateDto.getReceivableCurrencyTypeNum();
                //应收金额折合成应付币种的应收金额
                BigDecimal receivableExchangeAmount;
                //统一将全部金额转成应付计划币种进行公式计算
                if (fkCurrencyTypeNum.equals(settlementInstallmentUpdateDto.getPayableCurrencyTypeNum())) {
                    receivableExchangeAmount = receivableAmount;
                } else {
                    BigDecimal lastExchangeRate = exchangeRateService.getLastExchangeRate(false, fkCurrencyTypeNum, settlementInstallmentUpdateDto.getPayableCurrencyTypeNum()).getExchangeRate();
                    receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
                }
                BigDecimal proportion = settlementInstallmentUpdateDto.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
                Map<Long, BigDecimal> map = new HashMap<>();
                for (PayablePlanSettlementInstallment settlementInstallment : collect) {
                    InvoiceReceivablePlan invoiceReceivablePlan = data.get(settlementInstallment.getFkInvoiceReceivablePlanId());
                    //本次预付金额
                    BigDecimal amountActual = invoiceReceivablePlan.getAmount().multiply(proportion).multiply(invoiceReceivablePlan.getPayInAdvancePercent().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);

                    amountActual = amountActual.subtract(serviceFee);
                    PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                    payablePlanSettlementInstallment.setFkPayablePlanId(settlementInstallmentUpdateDto.getFkPayablePlanId());
                    payablePlanSettlementInstallment.setAmountExpect(amountActual);
                    payablePlanSettlementInstallment.setAmountActual(amountActual);
                    payablePlanSettlementInstallment.setAmountActualInit(amountActual);
                    payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
                    payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
                    payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
                    payablePlanSettlementInstallment.setRollBack(false);
                    payablePlanSettlementInstallment.setFkInvoiceReceivablePlanId(settlementInstallment.getFkInvoiceReceivablePlanId());
                    payablePlanSettlementInstallment.setFkInvoiceId(invoiceReceivablePlan.getFkInvoiceId());
                    payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
                    utilService.setCreateInfo(payablePlanSettlementInstallment);
                    payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
                    map.put(settlementInstallment.getFkInvoiceReceivablePlanId(), amountActual);
                }
                iInvoiceReceivablePlanService.updateInvoiceReceivablePlanAmount(map);
            }


        }
        return true;
    }

    /**
     * 检查应付状态，没有结算就返回true
     * @param payablePlanIds
     * @return
     */
    @Override
    public Boolean checkPayableInfo(Set<Long> payablePlanIds) {
        Integer payFormItemCount = paymentFormItemMapper.getPayFormItemCount(payablePlanIds);
        List<PayablePlanSettlementInstallment> installments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIds));
        return payFormItemCount == 0 && GeneralTool.isEmpty(installments);
    }


    /**
     * 插入应付计划结算分期表
     *
     * @Date 14:52 2022/3/30
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertSettlementInstallment(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto, ReceiptFormItemVo receiptFormItemVo) {
        //todo 看看整体是否影响，有些接口是先删再增   先删除改收款单子单id的分期表
        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementInstallmentUpdateDto.getFkPayablePlanId())
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key).eq(PayablePlanSettlementInstallment::getFkReceiptFormItemId, receiptFormItemVo.getId()));

        //获取应收计划信息 计算预计支付金额
//        ReceivablePlan receivablePlan = receivablePlanMapper.selectById(saleReceiptFormItemVo.getFkReceivablePlanId());
        //应收金额
        BigDecimal receivableAmount = settlementInstallmentUpdateDto.getReceivableAmount();
        //应收币种
        String fkCurrencyTypeNum = settlementInstallmentUpdateDto.getReceivableCurrencyTypeNum();
        //应收金额折合成应付币种的应收金额
        BigDecimal receivableExchangeAmount;
        //统一将全部金额转成应付计划币种进行公式计算
        if (fkCurrencyTypeNum.equals(settlementInstallmentUpdateDto.getPayableCurrencyTypeNum())) {
            receivableExchangeAmount = receivableAmount;
        } else {
            BigDecimal lastExchangeRate = exchangeRateService.getLastExchangeRate(false, fkCurrencyTypeNum, settlementInstallmentUpdateDto.getPayableCurrencyTypeNum()).getExchangeRate();
            receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
        }
        //收款单付款金额折合成 应付币种的金额
        BigDecimal receiptFormItemAmount;
        //收款单手续费金额折合成 应付币种手续费金额
        BigDecimal receiptFormItemServiceFee;
        if (receiptFormItemVo.getFkCurrencyTypeNum().equals(settlementInstallmentUpdateDto.getPayableCurrencyTypeNum())) {
            receiptFormItemAmount = receiptFormItemVo.getAmountReceipt();
            receiptFormItemServiceFee = receiptFormItemVo.getServiceFee();
        } else if (fkCurrencyTypeNum.equals(settlementInstallmentUpdateDto.getPayableCurrencyTypeNum())) {
            //实收兑应收币种汇率
            BigDecimal lastExchangeRate = new BigDecimal(1).divide(receiptFormItemVo.getExchangeRateReceivable(), 6, RoundingMode.HALF_UP );
            receiptFormItemAmount = receiptFormItemVo.getAmountReceipt().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
            receiptFormItemServiceFee = receiptFormItemVo.getServiceFee().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
        } else {
            BigDecimal lastExchangeRate = exchangeRateService.getLastExchangeRate(false, receiptFormItemVo.getFkCurrencyTypeNum(), settlementInstallmentUpdateDto.getPayableCurrencyTypeNum()).getExchangeRate();
            receiptFormItemAmount = receiptFormItemVo.getAmountReceipt().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
            receiptFormItemServiceFee = receiptFormItemVo.getServiceFee().multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
        }
        //(应付金额/应收金额转应付币种的金额)比例
        if (receivableExchangeAmount.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        BigDecimal proportion = settlementInstallmentUpdateDto.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
        PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
        payablePlanSettlementInstallment.setFkPayablePlanId(settlementInstallmentUpdateDto.getFkPayablePlanId());
        payablePlanSettlementInstallment.setFkReceiptFormItemId(receiptFormItemVo.getId());
        //本次预计支付金额 = 收款单收款金额折合成应付币种的金额   *    (应付金额/应收金额转应付币种的金额)比例     收到的钱乘以应收应付金额比例
        BigDecimal amountExpect = receiptFormItemAmount.add(receiptFormItemServiceFee).multiply(proportion).setScale(2, RoundingMode.HALF_UP);
        //本次预计手续费金额/实际手续费
        BigDecimal serviceFeeExpect = receiptFormItemServiceFee.setScale(2, RoundingMode.HALF_UP);

        //实际支付金额
        BigDecimal amountActual;
        //实际支付手续费金额
        BigDecimal amountActualServiceFee = serviceFeeExpect;

        //没有预付直接按比例支付
        amountActual = amountExpect;
        //需要减去手续费
        amountActual = amountActual.subtract(serviceFeeExpect);

        if (settlementInstallmentUpdateDto.getHedgeAmount() != null && settlementInstallmentUpdateDto.getHedgeAmount().compareTo(BigDecimal.ZERO) > 0) {
            amountActual = amountActual.subtract(settlementInstallmentUpdateDto.getHedgeAmount());
            if (amountActual.compareTo(BigDecimal.ZERO) <= 0) {
                return true;
            }
        }

        //已付金额
        BigDecimal amountPaid = paymentFormItemService.getAmountPaidByPayablePlanId(settlementInstallmentUpdateDto.getFkPayablePlanId()).add(payablePlanSettlementInstallmentMapper.getAmountPaidByPayablePlanId(settlementInstallmentUpdateDto.getFkPayablePlanId()));
        //应付未付金额
        BigDecimal unpaidAmount = settlementInstallmentUpdateDto.getPayableAmount().subtract(amountPaid);
        //应付未付为 <= 0，已经付完钱给代理了，不需要再结算钱给他  (本次支付金额为负数的，是倒扣的，不需要判断)
        if (((unpaidAmount.compareTo(new BigDecimal(0)) <= 0 && settlementInstallmentUpdateDto.getPayableAmount().compareTo(BigDecimal.ZERO) > 0) ||
                (unpaidAmount.compareTo(new BigDecimal(0)) >= 0 && settlementInstallmentUpdateDto.getPayableAmount().compareTo(BigDecimal.ZERO) < 0) ||
                settlementInstallmentUpdateDto.getPayableAmount().compareTo(BigDecimal.ZERO) == 0
        ) && amountActual.compareTo(BigDecimal.ZERO) >= 0) {
            return true;
        }
        //如果本次结算佣金 + 手续费 > 应付未付   (本次支付金额为负数的，是倒扣的，不需要判断)
        if (amountActual.compareTo(BigDecimal.ZERO) >= 0 && (amountActual.add(serviceFeeExpect)).compareTo(unpaidAmount) > 0) {
            amountActual = unpaidAmount.subtract(serviceFeeExpect);
        }

        //如果负数倒扣金额 等于 已生成佣金金额 则删除佣金
//        BigDecimal firstStepCommission = payablePlanSettlementInstallmentMapper.getFirstStepCommission(settlementInstallmentUpdateDto.getFkPayablePlanId());
//        if (amountActual.compareTo(firstStepCommission.negate()) == 0) {
//            //删除抵消佣金
//            payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                    .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, settlementInstallmentUpdateDto.getFkPayablePlanId())
//                    .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key)
//                    .eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.UNSETTLED.key));
//            return true;
//        }

        payablePlanSettlementInstallment.setAmountExpect(amountExpect);
        payablePlanSettlementInstallment.setAmountActual(amountActual);
        payablePlanSettlementInstallment.setAmountActualInit(amountActual);
        payablePlanSettlementInstallment.setServiceFeeExpect(serviceFeeExpect);
        payablePlanSettlementInstallment.setServiceFeeActual(amountActualServiceFee);
        payablePlanSettlementInstallment.setServiceFeeActualInit(amountActualServiceFee);
        payablePlanSettlementInstallment.setRollBack(false);
        payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
        payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
        utilService.setCreateInfo(payablePlanSettlementInstallment);
        payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);

        PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
        payablePlanSettlementStatus.setFkPayablePlanId(settlementInstallmentUpdateDto.getFkPayablePlanId());
        payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
        payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
        utilService.setCreateInfo(payablePlanSettlementStatus);
        payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
        return true;
    }

    @Override
    @Transactional
    public Boolean insertSettlementInstallmentBatch(List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos) {
        for (SettlementInstallmentBatchUpdateDto settlementInstallmentBatchUpdateDto : settlementInstallmentBatchUpdateDtos) {
            insertSettlementInstallment(settlementInstallmentBatchUpdateDto.getSettlementInstallmentUpdateDto(), settlementInstallmentBatchUpdateDto.getReceiptFormItemVo());
        }
        return null;
    }

    /**
     * 作废应付时，删除佣金结算数据
     * @param payablePlanId
     * @return
     */
    @Override
    public Boolean deleteSettlementCommissionByPayablePlanId(Long payablePlanId) {
        //检查应付计划是否处于结算中
        List<Integer> status = new ArrayList<>();
        status.add(ProjectExtraEnum.INSTALLMENT_PROCESSING.key);
        status.add(ProjectExtraEnum.INSTALLMENT_COMPLETE.key);
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanId).in(PayablePlanSettlementInstallment::getStatus, status));
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sale_settlement_in_progress"));
        }
        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .eq(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanId)
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));
        return true;
  }

    /**
     * 应付计划一键结算按钮
     * @param oneClickSettlementDto
     * @return
     */
    @Override
    public Boolean oneClickSettlement(OneClickSettlementDto oneClickSettlementDto) {
        for (Long payablePlanId : oneClickSettlementDto.getPayablePlanIds()) {
            PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
            payablePlanSettlementInstallment.setFkPayablePlanId(payablePlanId);
            payablePlanSettlementInstallment.setAmountExpect(oneClickSettlementDto.getSettlementAmount());
            payablePlanSettlementInstallment.setAmountActual(oneClickSettlementDto.getSettlementAmount());
            payablePlanSettlementInstallment.setAmountActualInit(oneClickSettlementDto.getSettlementAmount());
            payablePlanSettlementInstallment.setFkReceiptFormItemId(0L);
            payablePlanSettlementInstallment.setServiceFeeExpect(new BigDecimal(0));
            payablePlanSettlementInstallment.setServiceFeeActual(new BigDecimal(0));
            payablePlanSettlementInstallment.setServiceFeeActualInit(new BigDecimal(0));
            payablePlanSettlementInstallment.setRollBack(false);
            payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
            payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
            utilService.setCreateInfo(payablePlanSettlementInstallment);
            payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
        }
        return true;
    }

    /**
     * 获取待结算标记代理ids
     * @return
     */
    @Override
    public List<Long> getSettlementFlagAgentIds() {
        List<RPayablePlanSettlementFlag> rPayablePlanSettlementFlags = payablePlanSettlementFlagMapper.selectList(Wrappers.<RPayablePlanSettlementFlag>query().lambda().eq(RPayablePlanSettlementFlag::getStatusSettlement, ProjectExtraEnum.SETTLEMENT_IN_PROGRESS.key));
        List<Long> agentFlagIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(rPayablePlanSettlementFlags)) {
            agentFlagIds = rPayablePlanSettlementFlags.stream().map(RPayablePlanSettlementFlag::getFkAgentId).collect(Collectors.toList());
        }
        return agentFlagIds;
    }

    /**
     * Author Cream
     * Description : 更新结算通知信息
     * Date 2023/2/28 11:13
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSettlementNotificationInformation(SettlementCommissionNoticeDto settlementCommissionNoticeDto) {
        if (Objects.isNull(settlementCommissionNoticeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long payablePlanId = settlementCommissionNoticeDto.getFkPayablePlanId();
        SettlementCommissionNotice commissionNotice = payableSettlementMessageMapper.selectOne(Wrappers.<SettlementCommissionNotice>lambdaQuery()
                .eq(SettlementCommissionNotice::getFkPayablePlanId,payablePlanId).isNull(SettlementCommissionNotice::getNumSettlementBatch));
        //有则update，没有结果则insert
        if (Objects.isNull(commissionNotice)) {
            SettlementCommissionNotice settlementCommissionNotice = new SettlementCommissionNotice();
            BeanUtils.copyProperties(settlementCommissionNoticeDto,settlementCommissionNotice);
            utilService.setCreateInfo(settlementCommissionNotice);
            payableSettlementMessageMapper.insert(settlementCommissionNotice);
        }else {
            BeanUtils.copyProperties(settlementCommissionNoticeDto,commissionNotice);
            utilService.setUpdateInfo(commissionNotice);
            payableSettlementMessageMapper.updateById(commissionNotice);
        }
        List<MediaAndAttachedDto> mediaAndAttachedDtos = settlementCommissionNoticeDto.getMediaAndAttachedVos();
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {
            mediaAndAttachedDtos = mediaAndAttachedDtos.stream().filter(m->Objects.isNull(m.getFkTableId())).collect(Collectors.toList());
            for (MediaAndAttachedDto attachedVo : mediaAndAttachedDtos) {
                attachedVo.setFkTableName(TableEnum.SALE_PAYABLE.key);
                attachedVo.setFkTableId(settlementCommissionNoticeDto.getFkPayablePlanId());
                attachedVo.setTypeKey(FileTypeEnum.SALE_PAYABLE_FILE.key);
            }
            Result result = saleCenterClient.saveBatchMediaAndAttached(mediaAndAttachedDtos);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }

    }

    /**
     * Author Cream
     * Description : 结算通知信息详情
     * Date 2023/2/28 11:15
     * Params:
     * Return
     */
    @Override
    public SettlementCommissionNoticeVo settlementNotificationInformationDetails(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SettlementCommissionNotice commissionNotice = payableSettlementMessageMapper.selectOne(Wrappers.<SettlementCommissionNotice>lambdaQuery()
                .eq(SettlementCommissionNotice::getFkPayablePlanId,id));
        if (Objects.isNull(commissionNotice)) {
            commissionNotice = new SettlementCommissionNotice();
        }
        SettlementCommissionNoticeVo settlementCommissionNoticeVo = new SettlementCommissionNoticeVo();
        BeanUtils.copyProperties(commissionNotice, settlementCommissionNoticeVo);
        Set<Long> fkTableId = new HashSet<Long>(1){{
            add(id);
        }};
        Result<Map<Long, List<MediaAndAttachedVo>>> result = saleCenterClient.getMediaAndAttachedDtoByFkTableIds(TableEnum.SALE_PAYABLE.key, fkTableId);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        Map<Long, List<MediaAndAttachedVo>> dto = result.getData();
        settlementCommissionNoticeVo.setMediaAndAttachedDtos(dto.get(id));
        return settlementCommissionNoticeVo;
    }

    /**
     * 入学失败，需要删除对应的未结算完的佣金数据,并返回是否有结算数据 true为有数据
     * @param payablePlanIdSet
     * @return
     */
    @Override
    public Boolean deleteUnsettledCommissionByAdmissionFailure(List<Long> payablePlanIdSet) {
        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdSet)
                .ne(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_COMPLETE.key));
        payablePlanSettlementAgentAccountMapper.delete(Wrappers.<PayablePlanSettlementAgentAccount>lambdaQuery()
                .in(PayablePlanSettlementAgentAccount::getFkPayablePlanId, payablePlanIdSet)
                .isNull(PayablePlanSettlementAgentAccount::getNumSettlementBatch));
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, payablePlanIdSet));
        return GeneralTool.isNotEmpty(payablePlanSettlementInstallments);
    }

    /**
     * 获取预付手续费
     *
     * @Date 14:56 2024/2/27
     * <AUTHOR>
     */
    private BigDecimal getPrepaidHandlingFees(Long fkCompanyId) {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        return new BigDecimal(configValue1);
    }


}
