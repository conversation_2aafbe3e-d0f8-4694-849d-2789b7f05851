package com.get.financecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.CompanyProfitAndLossItemDto;
import com.get.financecenter.service.ICompanyProfitAndLossItemService;
import com.get.financecenter.vo.CompanyProfitAndLossItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公司损益表项管理
 */
@Api(tags = "公司损益表项管理")
@RestController
@RequestMapping("finance/companyProfitAndLossItem")
public class CompanyProfitAndLossItemController{
    /**
     * 服务对象
     */
    @Resource
    private ICompanyProfitAndLossItemService companyProfitAndLossItemService;


    @ApiOperation(value = "添加", notes = "添加")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/公司损益表项管理/添加")
    @PostMapping("add")
    public ResponseBo<String> add(@RequestBody CompanyProfitAndLossItemDto companyProfitAndLossItemDto) {
        companyProfitAndLossItemService.save(companyProfitAndLossItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改", notes = "修改")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/公司损益表项管理/修改")
    @PostMapping("edit")
    public ResponseBo<String> edit(@RequestBody CompanyProfitAndLossItemDto companyProfitAndLossItemDto) {
        companyProfitAndLossItemService.updateById(companyProfitAndLossItemDto);
        return ResponseBo.ok();
    }




    @ApiOperation(value = "查询所有数据", notes = "查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/公司损益表项管理/查询所有数据")
    @PostMapping("list")
    public ResponseBo<CompanyProfitAndLossItemVo> list(@RequestBody SearchBean<CompanyProfitAndLossItemDto> page) {
        List<CompanyProfitAndLossItemVo> datas = companyProfitAndLossItemService.getCompanyProfitAndLossItem(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);

    }

//    code-显示对应科目的发生额
//    expand-显示对应科目及其展开的二级科目统计的发生额
//    sum-显示累计的发生额
    @ApiOperation(value = "显示模式下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/公司损益表项管理/科目类型下拉框")
    @GetMapping("/getShowMode")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<Object> accountingItemTypeSelect() {
        return new ListResponseBo<>(companyProfitAndLossItemService.getShowMode());
    }


//    @ApiOperation(value = "排序(对换顺序)", notes = "排序(对换顺序)")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/公司损益表项管理/排序")
//    @PostMapping("sort")
//    public ResponseBo sort(@RequestBody List<Long> ids) {
//        companyProfitAndLossItemService.sort(ids);
//        return ResponseBo.ok();
//    }

    @ApiOperation(value = "排序（拖拽）", notes = "排序（拖拽）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/公司损益表项管理/排序（拖拽）")
    @PostMapping("sort")
    public ResponseBo sort(@RequestParam("start") Integer start , @RequestParam("end") Integer end) {
        companyProfitAndLossItemService.movingOrder(start, end);
        return ResponseBo.ok();
    }



    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @ApiOperation(value = "删除", notes = "删除")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/公司损益表项管理/删除")
    @PostMapping("delete")
    public ResponseBo<String> delete(@RequestParam("id") Long id) {
        companyProfitAndLossItemService.delete(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "复制模版到项目", notes = "复制模版到项目")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/公司损益表项管理/复制模版到项目")
    @PostMapping("copyTemplateToProject")
    public ResponseBo copyTemplateToProject(@RequestParam("fkCompanyId") Long fkCompanyId) {
        companyProfitAndLossItemService.copyTemplateToProject(fkCompanyId);
        return ResponseBo.ok();
    }
}

