package com.get.financecenter.utils;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.file.utils.ConvertToMultipartFile;
import lombok.extern.slf4j.Slf4j;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:45
 * Date: 2021/12/15
 * Description:
 */
@Slf4j
@Component
public class TemplateExcelUtils {

    /**
     * 根据模板导出数据
     *
     * @param fileName
     * @param sourcePath resource/template文件夹下路径
     * @param beanParams
     * @param response
     * @
     */
    public static void downLoadExcel(String fileName, String sourcePath, Map<String, Object> beanParams, HttpServletResponse response) {
        OutputStream os = getOutputStream(fileName, response);
        //读取模板
        InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
        XLSTransformer transformer = new XLSTransformer();
        try {
            //向模板中写入内容
            Workbook workbook = transformer.transformXLS(is, beanParams);
            //写入成功后转化为输出流
            workbook.write(os);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(e.getMessage());
        }
    }


    public static MultipartFile packageExcel(String fileName, String sourcePath, Map<String, Object> beanParams) {
        //读取模板
        InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
        XLSTransformer transformer = new XLSTransformer();
        try {
            //向模板中写入内容
            Workbook workbook = transformer.transformXLS(is, beanParams);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            //写入成功后转化为输出流
            workbook.write(outputStream);
            byte[] bytes = outputStream.toByteArray();
            return new ConvertToMultipartFile(bytes,"newFile",fileName,"xlsx",bytes.length);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(e.getMessage());
        }
    }

    public static void mergeAttachments(Map<String,byte[]> att,ZipOutputStream zos){
        try{
            byte[] bytes = new byte[2048];
            for (Map.Entry<String, byte[]> entry : att.entrySet()) {
                zos.putNextEntry(new ZipEntry(entry.getKey()));
                ByteArrayInputStream inputStream = new ByteArrayInputStream(entry.getValue());
                BufferedInputStream bis = new BufferedInputStream(inputStream);
                int read;
                while ((read = bis.read(bytes)) != -1) {
                    zos.write(bytes, 0, read);
                }
            }
        }catch (IOException e){
            e.printStackTrace();
        }
    }

    public static void downLoadExcelZip(String fileName, String sourcePath, Map<String, Object> beanParams, ZipOutputStream zos) {
//        fileName = URLEncoder.encode(fileName , "UTF-8").replaceAll("\\+", "%20");
        try {
            //读取模板
            InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
            XLSTransformer transformer = new XLSTransformer();
            //向模板中写入内容
            Workbook workbook = transformer.transformXLS(is, beanParams);
            //把excel写进压缩文件流
            zos.putNextEntry(new ZipEntry(fileName + ".xlsx"));
            ByteArrayOutputStream tempos = new ByteArrayOutputStream();
            //将excel写入到字节流
            workbook.write(tempos);
            //将当前字节流写入到zos流实为方法全局ByteArrayOutputStream
            tempos.writeTo(zos);
            // 关闭 ZipEntry
            zos.closeEntry();
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    /**
     * 导出文件时为Writer生成OutputStream.
     *
     * @param fileName 文件名
     * @param response response
     * @return ""
     */
    private static OutputStream getOutputStream(String fileName,
                                                HttpServletResponse response) {
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (IOException e) {
            log.error("导出excel表格失败:" + e.getMessage(), e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        }
    }


    /**
     * 佣金结算 加印章导出
     * @param fileName
     * @param sourcePath
     * @param beanParams
     * @param zos
     */
    public static void commissionSettlementDownLoadExcelZip(String fileName, String sourcePath, Map<String, Object> beanParams, ZipOutputStream zos,int col1, int row1, int col2, int row2, int accumulatedRows, String sealFileName) {
        try {
            //读取模板
            InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
            XLSTransformer transformer = new XLSTransformer();
            //向模板中写入内容
            Workbook workbook = transformer.transformXLS(is, beanParams);

            Sheet sheet = workbook.getSheetAt(0);
            Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
            InputStream inputStream = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sealFileName);
            byte[] byteArray = IOUtils.toByteArray(inputStream);
            int pictureIdx = workbook.addPicture(byteArray, Workbook.PICTURE_TYPE_PNG);
            row1 = row1 + accumulatedRows;
            row2 = row2 + accumulatedRows;
            ClientAnchor anchor = drawingPatriarch.createAnchor(0, 0, 0, 0, col1, row1, col2, row2);
            Picture picture = drawingPatriarch.createPicture(anchor, pictureIdx);

            //把excel写进压缩文件流
            zos.putNextEntry(new ZipEntry(fileName + ".xlsx"));
            ByteArrayOutputStream tempos = new ByteArrayOutputStream();
            //将excel写入到字节流
            workbook.write(tempos);
            //将当前字节流写入到zos流实为方法全局ByteArrayOutputStream
            tempos.writeTo(zos);
            // 关闭 ZipEntry
            zos.closeEntry();
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

}
