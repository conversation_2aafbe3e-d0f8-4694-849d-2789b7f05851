package com.get.financecenter.controller;

import com.get.common.cache.CacheNames;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.service.IInvoiceReceivablePlanService;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.InvoiceVo;
import com.get.financecenter.vo.InvoiceSelectVo;
import com.get.financecenter.service.IInvoiceService;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.InvoiceDto;
import com.get.financecenter.dto.query.InvoiceQueryDto;
import com.get.salecenter.dto.PrepaymentButtonHtiDto;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.dto.InvoiceReceiptFormDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/16 17:05
 * @verison: 1.0
 * @description:
 */
@Api(tags = "发票管理")
@RestController
@RequestMapping("finance/invoice")
public class InvoiceController {
    @Resource
    private IInvoiceService invoiceService;

    @Lazy
    @Resource
    private IInvoiceReceivablePlanService invoiceReceivablePlanService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.InvoiceDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/发票管理/发票详情")
    @GetMapping("/{id}")
    public ResponseBo<InvoiceVo> detail(@PathVariable("id") Long id) {
        InvoiceVo data = invoiceService.findInvoiceById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [invoiceVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/发票管理/新增发票")
    @RedisLock(value = CacheNames.INVOICE_ADD, param = "#invoiceDto.num", waitTime = 5L)
    @PostMapping("/add")
    public ResponseBo add(@RequestBody @Validated(InvoiceDto.Add.class)  InvoiceDto invoiceDto) {
        return SaveResponseBo.ok(invoiceService.addInvoice(invoiceDto));
    }
    @ApiOperation(value = "批量编辑绑定金额", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/发票管理/批量编辑绑定金额")
    @PostMapping("batchUpdateBindingAmount")
    public SaveResponseBo batchUpdateBindingAmount(@RequestBody BatchUpdateBindingAmountDto batchUpdateBindingAmountDto) {
        return invoiceService.batchUpdateBindingAmount(batchUpdateBindingAmountDto);
    }

    @ApiOperation(value = "编辑申请佣金摘要并通知邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/发票管理/编辑申请佣金摘要并通知邮件")
    @PostMapping("editCommissionNotice")
    public SaveResponseBo editCommissionNotice(@RequestParam("invoiceId") Long invoiceId,@RequestParam("receivablePlanId") Long receivablePlanId,@RequestParam("remark") String remark) {
        return invoiceService.editCommissionNotice(invoiceId,receivablePlanId,remark);
    }

    /**
     * Author Cream
     * Description : 批量新增发票和应收计划映射
     * Date 2022/4/18 14:52
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/发票管理/批量绑定应收计划")
    @PostMapping("addBatch")
    public ResponseBo addBatch(@RequestBody @Validated InvoiceReceivablePlanBatchDto invoiceVo) {
        return invoiceService.batchMappingInvoiceAndReceivablePlan(invoiceVo);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/发票管理/删除发票")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        invoiceService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.InvoiceDto>
     * @Description :修改信息
     * @Param [invoiceVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/更新发票")
    @PostMapping("update")
    public ResponseBo<InvoiceVo> update(@RequestBody  @Validated(InvoiceDto.Update.class)  InvoiceDto invoiceDto) {
        return UpdateResponseBo.ok(invoiceService.updateInvoice(invoiceDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.InvoiceDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/发票管理/查询发票")
    @PostMapping("datas")
    public ResponseBo<InvoiceVo> datas(@RequestBody SearchBean<InvoiceQueryDto> page) {
        List<InvoiceVo> datas = invoiceService.getInvoices(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出发票列表Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/发票管理/导出发票列表Excel")
    @PostMapping("/exportInvoicesExcel")
    @ResponseBody
    public void exportInvoicesExcel(HttpServletResponse response, @RequestBody InvoiceQueryDto invoiceVo) {
        invoiceService.exportInvoicesExcel(response, invoiceVo);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询发票附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询发票附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/发票管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<FMediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = invoiceService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存发票附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存发票附件")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/发票管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addAgentMedia(@RequestBody  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(invoiceService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :是否作废
     * @Param [invoiceId]
     * <AUTHOR>
     */
    @ApiOperation(value = "是否作废", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/发票作废")
    @PostMapping("isCancel")
    public ResponseBo isCancel(@RequestParam("invoiceId") Long invoiceId,@RequestParam("status")Integer status) {
        invoiceService.isCancel(invoiceId,status);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 发票下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "发票下拉", notes = "")
    @GetMapping("getInvoiceSelect")
    public ResponseBo<BaseSelectEntity> getInvoiceSelect() {
        List<BaseSelectEntity> datas = invoiceService.getInvoiceSelect();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "应收计划列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/发票管理/应收计划列表")
    @PostMapping("/getReceivablePlans")
    public ResponseBo getReceivablePlans(@RequestBody SearchBean<InvoiceReceivablePlanDto> invoiceReceivablePlanVoSearchBean) {
        List<ReceivablePlanVo> data = invoiceService.getReceivablePlans(invoiceReceivablePlanVoSearchBean.getData(), invoiceReceivablePlanVoSearchBean);
        Page page = BeanCopyUtils.objClone(invoiceReceivablePlanVoSearchBean, Page::new);
        return new ListResponseBo(data, page);
    }

    @ApiOperation(value = "发票下应收计划IDS")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/发票管理/应收计划列表")
    @GetMapping("/getReceivablePlanIds")
    public ResponseBo<Long> getReceivablePlanIds(@RequestParam("invoiceId")Long invoiceId) {
        return new ListResponseBo<>(invoiceService.doGetReceivablePlanIds(invoiceId));
    }

    @ApiIgnore
    @ApiOperation(value = "feign调用，根据计划Ids获取发票编码")
    @PostMapping("/getFkInvoiceNum")
    public Map<Long, String> getFkInvoiceNum(@RequestParam("planIds") Set<Long> planIds) {
        return invoiceService.getFkInvoiceNum(planIds);
    }

    /**
     * 根据提目标类型获取对应发票下拉接口
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提目标类型获取对应发票下拉接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单管理/根据提目标类型获取对应发票下拉接口")
    @GetMapping("getInvoiceSelectByTargetId")
    public ResponseBo<InvoiceSelectVo> getInvoiceSelectByTargetId(@RequestParam("fkTypeTargetId") Long fkTypeTargetId, @RequestParam("fkTypeKey") String fkTypeKey, @RequestParam(value = "fkReceiptFormId", required = false) Long fkReceiptFormId) {
        return new ResponseBo(invoiceService.getInvoiceSelectByTargetId(fkTypeTargetId, fkTypeKey, fkReceiptFormId));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取收款单发票下拉列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单管理/获取收款单发票下拉列表")
    @PostMapping("getInvoiceSelectList")
    public ResponseBo<InvoiceSelectVo> getInvoiceSelectList(@RequestBody @Validated InvoiceReceiptFormDto invoiceFormVo) {
        List<InvoiceSelectVo> data = invoiceService.doGetInvoiceSelectList(invoiceFormVo);
        return new ListResponseBo<>(data);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取所有发票", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/应收计划管理/获取所有发票")
    @PostMapping("getAllInvoice")
    public ResponseBo<BaseSelectEntity> getAllInvoice(@RequestBody @Validated ReceivableInvoiceQueryDto receivableInvoiceQueryDto){
        return new ListResponseBo<>(invoiceService.doGetAllInvoice(receivableInvoiceQueryDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量解绑应收计划
     * @Param [invoiceId]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量解绑应收计划", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/批量解绑应收计划")
    @PostMapping("unBoundPlans")
    public ResponseBo unBoundPlans(@RequestBody List<Long> ids) {
        invoiceService.unBoundPlans(ids);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :获取绑定的发票编号
     * @Param [invoiceId]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取绑定的发票编号", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/获取绑定的发票编号")
    @PostMapping("getBindInvoiceNums")
    public ResponseBo getBindInvoiceNums(@RequestBody @Validated InvoiceBindDto invoiceBindDto) {
        String data = invoiceService.getBindInvoiceNums(invoiceBindDto);
        return new ResponseBo(data);
    }



    @ApiOperation(value = "获取发票列表总金额", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/获取发票列表总金额")
    @PostMapping("getInvoiceTotalAmountByIds")
    public ResponseBo<BigDecimal> getInvoiceTotalAmountByIds(@RequestBody Set<Long> list) {
        return invoiceService.doGetInvoiceTotalAmountByIds(list);
    }


    @ApiOperation(value = "批量编辑发票绑定金额", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/批量编辑发票绑定金额")
    @PostMapping("batchUpdateAmount")
    public ResponseBo batchUpdateAmount(@RequestBody List<BatchUpdateAmountDto> batchUpdateAmountDtos) throws GetServiceException {
        invoiceReceivablePlanService.batchUpdateAmount(batchUpdateAmountDtos);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "判断发票编号是否存在", notes = "true编号存在/false编号不存在")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/发票管理/判断发票编号是否存在")
    @PostMapping("isExistNum")
    public ResponseBo<Boolean> isExistInvoiceNum(@RequestParam("invoiceNum") String invoiceNum) {
        return new ResponseBo<>(invoiceService.isExistInvoiceNum(invoiceNum));
    }



    @ApiOperation(value = "HTI预付按钮", notes = "id为此条应付计划id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/发票管理/HTI预付按钮")
    @PostMapping("prepaymentButtonHti")
    public ResponseBo prepaymentButtonHti(@RequestBody @Validated(BaseVoEntity.Add.class) List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList) {
        String message = invoiceService.prepaymentButtonHti(prepaymentButtonHtiDtoList);
        if (GeneralTool.isNotEmpty(message)) {
            return ResponseBo.error(ErrorCodeEnum.REQUEST_OK_MESSAGE.getCode(), message);
        } else {
            return ResponseBo.ok();
        }
    }

    @ApiOperation(value = "HTI取消预付按钮")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/发票管理/HTI取消预付按钮")
    @PostMapping("cancelPrepaymentButtonHti")
    public ResponseBo cancelPrepaymentButtonHti(@RequestBody @Validated List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList) {
        String message = invoiceService.cancelPrepaymentButtonHti(prepaymentButtonHtiDtoList);
        if (GeneralTool.isNotEmpty(message)) {
            return ResponseBo.error(ErrorCodeEnum.REQUEST_OK_MESSAGE.getCode(), message);
        } else {
            return ResponseBo.ok();
        }
    }


}
