package com.get.financecenter.service;

import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.entity.ReceiptFormInvoice;
import com.get.financecenter.dto.BalancingReceiptFormDto;
import com.get.financecenter.dto.QuickReceiptFormDto;
import com.get.financecenter.dto.ReceiptFormInvoiceDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/24
 * @TIME: 14:57
 * @Description:
 **/
public interface IReceiptFormInvoiceService extends BaseService<ReceiptFormInvoice> {
    /**
     * @return void
     * @Description: 绑定
     * @Param [institutionPathwayVo]
     * <AUTHOR>
     **/
    void update(ReceiptFormInvoiceDto receiptFormInvoiceDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 根据收款单ids获取发票map
     *
     * @return
     */
    Map<Long, Set<Long>> getInvoiceByFkReceiptFormIds(Set<Long> fkReceiptFormIds);

    /**
     * 根据收款单id获取发票id列表
     *
     * @param fkReceiptFormId
     * @return
     */
    List<Long> getInvoiceByFormId(Long fkReceiptFormId);

    /**
     * 快速创建收款单
     * @param quickReceiptFormDto
     */
    ResponseBo doQuickCreateReceiptForm(QuickReceiptFormDto quickReceiptFormDto);

    /**
     * 校验发票是否合收款单绑定
     * @param invoiceId
     * @return
     */
    Boolean checkReceiptInvoiceMappingExist(Long invoiceId);

    /**
     * 批量找平
     * @param balancingReceiptFormDtos
     * @return
     */
    ResponseBo batchBalancingReceiptForm(List<BalancingReceiptFormDto> balancingReceiptFormDtos);
}
