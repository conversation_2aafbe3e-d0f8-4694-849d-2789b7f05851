package com.get.financecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.PrepaymentButtonHtiVo;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.dto.BatchUpdateAmountDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/4/27 12:37
 * @verison: 1.0
 * @description:
 */
public interface IInvoiceReceivablePlanService extends BaseService<InvoiceReceivablePlan> {

    /**
     * 批量编辑发票绑定金额
     * @param batchUpdateAmountDtos
     */
    void batchUpdateAmount(List<BatchUpdateAmountDto> batchUpdateAmountDtos);

    /**
     * 根据应收绑定发票
     * @param receivablePlanId
     * @param amount
     * @param invoiceId
     */
    void receivableBindingInvoice(Long receivablePlanId, BigDecimal amount, Long invoiceId);


    /**
     * 获取佣金通知信息
     * @param fkInvoiceId
     * @return
     */
    Map<Long, String> getInvoiceCommissionNotice(Long fkInvoiceId);

    /**
     * 更新发票绑定金额
     * @param invoiceId
     * @param receivablePlanId
     * @param amount
     * @return
     */
    Boolean updateInvoiceReceivablePlan(Long invoiceId, Long receivablePlanId, BigDecimal amount);

    /**
     * HTI预付
     *
     * @Date 18:17 2024/6/14
     * <AUTHOR>
     */
    Boolean prepaymentButtonHti(List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList);

    Map<Long, InvoiceReceivablePlan> getInvoiceAmountByIds(List<Long> fkInvoiceReceivablePlanIds);

    /**
     * 更新发票绑定金额
     *
     * @Date 13:36 2024/6/21
     * <AUTHOR>
     */
    Boolean updateInvoiceReceivablePlanAmount(Map<Long, BigDecimal> map);

    /**
     * 根据发票应收id,获取预付百分比
     * @param fkInvoiceReceivablePlanId
     * @return
     */
    InvoiceReceivablePlan getInvoiceReceivablePlanById(Long fkInvoiceReceivablePlanId);


}
