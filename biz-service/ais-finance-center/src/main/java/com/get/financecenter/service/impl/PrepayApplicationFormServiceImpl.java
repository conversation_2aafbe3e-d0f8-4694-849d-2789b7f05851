package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.cache.CacheNames;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.MediaAndAttachedMapper;
import com.get.financecenter.dao.PrepayApplicationFormMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.PrepayApplicationFormDto;
import com.get.financecenter.dto.query.PrepayApplicationFormQueryDto;
import com.get.financecenter.entity.FMediaAndAttached;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.Vouch;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IPrepayApplicationFormService;
import com.get.financecenter.utils.MyStringUtils;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.DepartmentVo;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:32
 */
@Service
public class PrepayApplicationFormServiceImpl extends BaseServiceImpl<PrepayApplicationFormMapper, PrepayApplicationForm> implements IPrepayApplicationFormService {

    @Resource
    PrepayApplicationFormMapper prepayApplicationFormMapper;
    @Resource
    MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private UtilService utilService;
    @Resource
    private VouchMapper vouchMapper;

    @Override
    public Long save(PrepayApplicationFormDto prepayApplicationFormDto) {
        PrepayApplicationForm borrowFrom = BeanCopyUtils.objClone(prepayApplicationFormDto, PrepayApplicationForm::new);
        borrowFrom.setGmtCreateUser(String.valueOf(SecureUtil.getStaffName()));
        borrowFrom.setStatus(0);
        utilService.updateUserInfoToEntity(borrowFrom);
        //撤单有可能 是有数据
        if (GeneralTool.isEmpty(prepayApplicationFormDto.getFkStaffId())) {
            borrowFrom.setFkStaffId(GetAuthInfo.getStaffId());
        }
        prepayApplicationFormMapper.insert(borrowFrom);
        borrowFrom.setNum(MyStringUtils.getFormNum("PAF",borrowFrom.getId()));
        prepayApplicationFormMapper.updateById(borrowFrom);

        if (null != prepayApplicationFormDto.getMediaAndAttachedVo()) {
            FMediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(prepayApplicationFormDto.getMediaAndAttachedVo(), FMediaAndAttached::new);
            mediaAndAttached.setFkTableId(borrowFrom.getId());
            utilService.updateUserInfoToEntity(mediaAndAttached);
            mediaAndAttachedMapper.insertSelective(mediaAndAttached);
        }
        return borrowFrom.getId();

    }

    @Override
    public Boolean startBorrowFlow(String businessKey, String procdefKey, String companyId) {
        //111111
        Result<Boolean> aBoolean = workflowCenterClient.startBorrowFlow(businessKey, procdefKey, companyId);
        if (!aBoolean.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_error"));
        }

        return true;

    }


    @Override
    public boolean updateBorrowMoneyStatus(PrepayApplicationForm borrowFrom) {
        utilService.updateUserInfoToEntity(borrowFrom);
        prepayApplicationFormMapper.updateById(borrowFrom);
        return true;
    }

    @Override
    public PrepayApplicationFormVo getBorrowMoneyById(Long id) {

        PrepayApplicationForm prepayApplicationForm = prepayApplicationFormMapper.selectById(id);
        PrepayApplicationFormVo prepayApplicationFormVo = BeanCopyUtils.objClone(prepayApplicationForm, PrepayApplicationFormVo::new);
        return prepayApplicationFormVo;

    }

    @Override
    public List<FMediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVo.get(0).getTypeKey().equals(FileTypeEnum.WORKFLOW_PAY_FILE.key)) {
//            Example example = new Example(MediaAndAttached.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("fkTableName", mediaAttachedVo.get(0).getFkTableName());
//            criteria.andEqualTo("fkTableId", mediaAttachedVo.get(0).getFkTableId());
//            criteria.andEqualTo("typeKey", TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);
//            mediaAndAttachedMapper.deleteByExample(example);
            mediaAndAttachedMapper.delete(Wrappers.<FMediaAndAttached>query().lambda()
                    .eq(FMediaAndAttached::getFkTableName, TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)
                    .eq(FMediaAndAttached::getFkTableId, mediaAttachedVo.get(0).getFkTableId())
                    .eq(FMediaAndAttached::getTypeKey, mediaAttachedVo.get(0).getTypeKey()));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVos : mediaAttachedVo) {
            //设置插入的表
            mediaAttachedVos.setFkTableName(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAttachedVos));
        }
        return mediaAndAttachedDtos;


    }

    @Override
    public List<PrepayApplicationFormVo> getBorrowMoneyData(PrepayApplicationFormQueryDto prepayApplicationFormVo, Page page) {
        if (GeneralTool.isNotEmpty(prepayApplicationFormVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(prepayApplicationFormVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        String num = "";
        if (GeneralTool.isNotEmpty(prepayApplicationFormVo.getNum())) {
            num = "%" + prepayApplicationFormVo.getNum() + "%";
        }
        //status 1全部  0个人 2我的审批
        List<PrepayApplicationFormVo> mpayDtoList = new ArrayList<>();
//        StaffVo staff = StaffContext.getStaff();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        String username = String.valueOf(GetAuthInfo.getLoginId());
        //111111
        Result<List<Long>> result1 = workflowCenterClient.getPersonalHistoryTasks(prepayApplicationFormVo.getProcdkey());
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        List<Long> personalHistoryTasks = result1.getData();
        IPage<PrepayApplicationFormVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<PrepayApplicationFormVo> borrowMoneyDtoList = prepayApplicationFormMapper.getBorrowFormData(iPage, prepayApplicationFormVo, personalHistoryTasks, num, username);
        page.setAll((int) iPage.getTotal());
        //公司ids
        Set<Long> companyIds = borrowMoneyDtoList.stream().map(PrepayApplicationFormVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }



        //部门ids
        Set<Long> departmentIds = borrowMoneyDtoList.stream().map(PrepayApplicationFormVo::getFkDepartmentId).collect(Collectors.toSet());
        //根据部门ids获取名称map
        Map<Long, String> departmentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(departmentIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getDepartmentNamesByIds(departmentIds);
            if (result.isSuccess() && result.getData() != null) {
                departmentNamesByIds = result.getData();
            }
        }

        //币种编号nums
        Set<String> currencyTypeNums = borrowMoneyDtoList.stream().map(PrepayApplicationFormVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }

        for (PrepayApplicationFormVo borrowMoneyDto : borrowMoneyDtoList) {
            PrepayApplicationFormVo returnBorrowMoneyDto = borrowMoneyDto;
            returnBorrowMoneyDto.setFkTableParentId(borrowMoneyDto.getFkPrepayApplicationFormIdRevoke());
            returnBorrowMoneyDto.setShortName(companyNamesByIds.get(borrowMoneyDto.getFkCompanyId()));
            returnBorrowMoneyDto.setDepartmentName(departmentNamesByIds.get(borrowMoneyDto.getFkDepartmentId()));
            String currencyNameByNum = currencyNamesByNums.get(borrowMoneyDto.getFkCurrencyTypeNum());
            returnBorrowMoneyDto.setFkCurrencyTypeNumName(currencyNameByNum);
            mpayDtoList.add(returnBorrowMoneyDto);
            //111111
            //查询task版本
            Result<ActRuTaskVo> resultAct =
                    workflowCenterClient.getTaskDataByBusinessKey(String.valueOf(returnBorrowMoneyDto.getId()), "m_prepay_application_form");
            if (resultAct.isSuccess() && GeneralTool.isNotEmpty(resultAct.getData())) {
                ActRuTaskVo taskVersionByBusinessKey = resultAct.getData();
                if (taskVersionByBusinessKey != null) {
                    returnBorrowMoneyDto.setTaskVersion(taskVersionByBusinessKey.getRev());
                    returnBorrowMoneyDto.setTaskId(taskVersionByBusinessKey.getId());
                    returnBorrowMoneyDto.setDeployId(taskVersionByBusinessKey.getDeployId());
                    returnBorrowMoneyDto.setProcInstId(taskVersionByBusinessKey.getProcInstId());
                    if (GeneralTool.isNotEmpty(taskVersionByBusinessKey.getRev()) || GeneralTool.isNotEmpty(taskVersionByBusinessKey.getId())) {
                        //待修改和上面
                        Result<Integer> result = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(), taskVersionByBusinessKey.getRev());
                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                            returnBorrowMoneyDto.setSignOrGetStatus(result.getData());
                        }
                    } else {
                        returnBorrowMoneyDto.setSignOrGetStatus(2);

                    }
                } else {
                    returnBorrowMoneyDto.setSignOrGetStatus(2);

                }
            }
        }
        return mpayDtoList;

    }


    @Override
    public PrepayApplicationFormVo getPrepayDetailData(Long businessKey) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        PrepayApplicationForm payForm = prepayApplicationFormMapper.selectById(businessKey);
        if (payForm == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        PrepayApplicationFormVo prepayApplicationFormVo = BeanCopyUtils.objClone(payForm, PrepayApplicationFormVo::new);

//        ActRuTaskVo prepayTaskDataByBusinessKey =
//                workflowCenterClient.getPrepayTaskDataByBusinessKey(String.valueOf(prepayApplicationFormDto.getId()), "m_prepay_application_form");
        Result<ActRuTaskVo> resultprepayTaskDataByBusinessKey =
                workflowCenterClient.getPrepayTaskDataByBusinessKey(String.valueOf(prepayApplicationFormVo.getId()), "m_prepay_application_form");
        if (!resultprepayTaskDataByBusinessKey.isSuccess()) {
            throw new GetServiceException(resultprepayTaskDataByBusinessKey.getMessage());
        }
        ActRuTaskVo prepayTaskDataByBusinessKey = resultprepayTaskDataByBusinessKey.getData();
        if (prepayTaskDataByBusinessKey != null) {
            prepayApplicationFormVo.setProcInstId(prepayTaskDataByBusinessKey.getProcInstId());
            prepayApplicationFormVo.setTaskVersion(prepayTaskDataByBusinessKey.getRev());
            prepayApplicationFormVo.setTaskId(prepayTaskDataByBusinessKey.getId());
            prepayApplicationFormVo.setDeployId(prepayTaskDataByBusinessKey.getDeployId());
            //待修改和上面
            if (GeneralTool.isNotEmpty(prepayTaskDataByBusinessKey.getRev()) || GeneralTool.isNotEmpty(prepayTaskDataByBusinessKey.getId())) {
                Result<Integer> result = workflowCenterClient.getSignOrGet(prepayTaskDataByBusinessKey.getId(), prepayTaskDataByBusinessKey.getRev());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    prepayApplicationFormVo.setSignOrGetStatus(result.getData());
                }
            } else {
                prepayApplicationFormVo.setSignOrGetStatus(2);
            }
        } else {
            prepayApplicationFormVo.setSignOrGetStatus(2);
        }

        Result<String> result = permissionCenterClient.getCompanyNameById(prepayApplicationFormVo.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            prepayApplicationFormVo.setShortName(result.getData());
        }

        String currencyNameByNum = currencyTypeService.getCurrencyNameByNum(prepayApplicationFormVo.getFkCurrencyTypeNum());
        if (GeneralTool.isNotEmpty(currencyNameByNum)) {
            prepayApplicationFormVo.setFkCurrencyTypeNumName(currencyNameByNum);
        }
        Result<DepartmentVo> departmentDtoResult = permissionCenterClient.getDepartmentById(prepayApplicationFormVo.getFkDepartmentId());
        if (departmentDtoResult.isSuccess() && departmentDtoResult.getData() != null) {
            prepayApplicationFormVo.setDepartmentName(departmentDtoResult.getData().getName());

        }

        Result<HiCommentFeignVo> result1 = workflowCenterClient.getHiComment(businessKey, TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        HiCommentFeignVo hiCommentFeignVo = result1.getData();
        if (GeneralTool.isNotEmpty(hiCommentFeignVo)) {
            prepayApplicationFormVo.setAgreeButtonType(hiCommentFeignVo.getAgreeButtonType());
            prepayApplicationFormVo.setRefuseButtonType(hiCommentFeignVo.getRefuseButtonType());
        }
        return prepayApplicationFormVo;
    }

    /**
     * 借款申请单作废
     * @param id
     */
    @Override
    @Transactional
    public void cancelPrepayApplicationForm(Long id) {
        PrepayApplicationForm prepayApplicationForm = prepayApplicationFormMapper.selectById(id);
        Result<List<ActHiTaskInstVo>> result = workflowCenterClient.getActHiTaskInstDtosByBusinessKey(String.valueOf(id), TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);
        if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())) {
            List<ActHiTaskInstVo> actHiTaskInstVos = result.getData();
            //删除流程
            if (0 != prepayApplicationForm.getStatus()) {
                String procInstId = actHiTaskInstVos.get(0).getProcInstId();
                Boolean aBoolean = workflowCenterClient.stopExecution(procInstId, "终止流程", TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key, String.valueOf(id)).getData();
                if (!aBoolean){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
                CacheUtil.clear(CacheNames.TASK_CACHE);
            }
        }

        prepayApplicationForm.setStatus(5);
        utilService.updateUserInfoToEntity(prepayApplicationForm);
        prepayApplicationFormMapper.updateById(prepayApplicationForm);
    }


    @Override
    public void updataPrepayData(PrepayApplicationFormDto prepayApplicationFormDto) {


        if (null != prepayApplicationFormDto.getMediaAndAttachedVo()) {
            //TODO 改过
           // FMediaAndAttachedVo mediaAndAttachedDto = BeanCopyUtils.objClone(prepayApplicationFormDto.getMediaAndAttachedVo(), FMediaAndAttachedVo::new);
            FMediaAndAttached mediaAndAttachedDto = BeanCopyUtils.objClone(prepayApplicationFormDto.getMediaAndAttachedVo(), FMediaAndAttached::new);
            utilService.updateUserInfoToEntity(mediaAndAttachedDto);
            mediaAndAttachedMapper.updateById(mediaAndAttachedDto);
        }
        // TODO 改过
        //PrepayApplicationFormVo prepayApplicationFormVo = BeanCopyUtils.objClone(prepayApplicationFormDto, PrepayApplicationFormVo::new);
        PrepayApplicationForm prepayApplicationFormVo = BeanCopyUtils.objClone(prepayApplicationFormDto, PrepayApplicationForm::new);
        utilService.updateUserInfoToEntity(prepayApplicationFormVo);
        prepayApplicationFormMapper.updateById(prepayApplicationFormVo);

    }

    @Override
    public List<FMediaAndAttachedVo> getPrepayFileData(MediaAndAttachedDto mediaAndAttachedDto, Page page) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);

        return attachedService.getMediaAndAttachedDto(mediaAndAttachedDto, page);

    }

    @Override
    public void getUserSubmit(String taskId, String status) {
        //111111
        Result<Boolean> result = workflowCenterClient.getUserSubmit(taskId, status);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
    }

    @Override
    public void getRevokePrepayApplication(Long id, String summary) {
        PrepayApplicationForm prepayApplicationForm = prepayApplicationFormMapper.selectById(id);
        if (GeneralTool.isEmpty(prepayApplicationForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (prepayApplicationFormMapper.getExistParentId(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        PrepayApplicationForm prepayApplicationForm1 = new PrepayApplicationForm();
        prepayApplicationForm1.setSummary(summary);
        prepayApplicationForm1.setFkPrepayApplicationFormIdRevoke(prepayApplicationForm.getId());
        prepayApplicationForm1.setFkCompanyId(prepayApplicationForm.getFkCompanyId());
        prepayApplicationForm1.setFkStaffId(prepayApplicationForm.getFkStaffId());
        prepayApplicationForm1.setAmount(prepayApplicationForm.getAmount());
        prepayApplicationForm1.setFkCurrencyTypeNum(prepayApplicationForm.getFkCurrencyTypeNum());
        prepayApplicationForm1.setFkDepartmentId(prepayApplicationForm.getFkDepartmentId());
        PrepayApplicationFormDto prepayApplicationFormDto = BeanCopyUtils.objClone(prepayApplicationForm1, PrepayApplicationFormDto::new);
        Long newId = this.save(prepayApplicationFormDto);
        if (GeneralTool.isNotEmpty(newId)) {
            List<FMediaAndAttachedVo> mediaAndAttachedList = mediaAndAttachedMapper.getMediaAndAttachedList(id);
            if (GeneralTool.isNotEmpty(mediaAndAttachedList)) {
                mediaAndAttachedList.stream().forEach(mediaAndAttachedDto -> {
                    mediaAndAttachedDto.setFkTableId(newId);
                    mediaAndAttachedDto.setId(null);
                    mediaAndAttachedDto.setGmtModifiedUser(null);
                    mediaAndAttachedDto.setGmtModified(null);
                });
                this.addInstitutionMedia(BeanCopyUtils.copyListProperties(mediaAndAttachedList, MediaAndAttachedDto::new));
            }
            prepayApplicationForm.setStatus(ProjectExtraEnum.REVOKED.key);
            utilService.updateUserInfoToEntity(prepayApplicationForm);
            prepayApplicationFormMapper.updateById(prepayApplicationForm);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 根据id获取借款申请单
     * @param targetId
     * @return
     */
    @Override
    public PrepayApplicationForm getPrepayApplicationFormById(Long targetId) {
        return prepayApplicationFormMapper.selectById(targetId);
    }
}
