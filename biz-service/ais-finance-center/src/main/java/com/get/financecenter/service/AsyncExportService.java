package com.get.financecenter.service;

import com.get.core.secure.UserInfo;
import com.get.financecenter.dto.PaymentFormDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AsyncExportService {


    /**
     * 导出收款单明细
     * @param paymentFormDto
     * @param headerMap
     * @param user
     * @param locale
     */
    void asyncExportPaymentFormItemExcel(PaymentFormDto paymentFormDto, Map<String, String> headerMap, UserInfo user, String locale);


    /**
     *
     * @param paymentFormDto
     * @param headerMap
     * @param user
     * @param locale
     */
    void asyncExportPaymentFormExcel(PaymentFormDto paymentFormDto, Map<String, String> headerMap, UserInfo user, String locale);


    /**
     * 导出代理结算
     * @param agentSettlementVo
     * @param headerMap
     * @param local
     * @param user
     */
    void asyncExportAgencySettlement(AgentSettlementQueryDto agentSettlementVo, Map<String, String> headerMap, String local, UserInfo user);

    /**
     * 第三步佣金结算总额表导出
     * @param agentSettlementVo
     * @param headerMap
     * @param local
     * @param user
     */
    void asyncExportAgentSettlementGrossAmount(AgentSettlementQueryDto agentSettlementVo, Map<String, String> headerMap, String local, UserInfo user, Long fkCompanyId);

    /**
     * 导出收款单汇总
     * @param receiptFormVo
     * @param headerMap
     * @param user
     * @param locale
     */
    void exportReceiptFormExcel(ReceiptFormQueryDto receiptFormVo, Map<String, String> headerMap, UserInfo user, String locale);

    /**
     * 导出明细汇总
     * @param receiptFormVo
     * @param headerMap
     * @param user
     * @param locale
     */
    void exportReceiptFormItemExcel(ReceiptFormQueryDto receiptFormVo, Map<String, String> headerMap, UserInfo user, String locale);
}
