package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.entity.CurrencyType;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.dto.CurrencyTypeDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/9/17 11:47
 * @verison: 1.0
 * @description:
 */
@Service
public class CurrencyTypeServiceImpl extends BaseServiceImpl<CurrencyTypeMapper, CurrencyType> implements ICurrencyTypeService {
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public CurrencyTypeVo findCurrencyTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CurrencyType currencyType = currencyTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(currencyType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CurrencyTypeVo dto = BeanCopyUtils.objClone(currencyType, CurrencyTypeVo::new);
        String fullName = dto.getTypeName();
        if (GeneralTool.isNotEmpty(dto.getNum())) {
            fullName = fullName + "（" + dto.getNum() + "）";
        }
        dto.setFullName(fullName);
        //公开对象
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(dto.getPublicLevel())) {
            List<String> result = Arrays.asList(dto.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            dto.setPublicLevelName(stringJoiner.toString());
        }
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<CurrencyTypeDto> currencyTypeDtos) {
        if (GeneralTool.isEmpty(currencyTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = currencyTypeMapper.getMaxViewOrder();
        for (CurrencyTypeDto currencyTypeDto : currencyTypeDtos) {
            if (GeneralTool.isEmpty(currencyTypeDto.getId())) {
                CurrencyType currencyType = BeanCopyUtils.objClone(currencyTypeDto, CurrencyType::new);
                String msg = validateAdd(currencyTypeDto);
                if (GeneralTool.isEmpty(msg)) {
                    currencyType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(currencyType);
                    currencyTypeMapper.insertSelective(currencyType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(msg);
                }
            } else {
                String msg = validateUpdate(currencyTypeDto);
                if (GeneralTool.isEmpty(msg)) {
                    CurrencyType currencyType = BeanCopyUtils.objClone(currencyTypeDto, CurrencyType::new);
                    utilService.updateUserInfoToEntity(currencyType);
                    currencyTypeMapper.updateById(currencyType);
                } else {
                    throw new GetServiceException(msg);
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (currencyTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = currencyTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public CurrencyTypeVo updateCurrencyType(CurrencyTypeDto currencyTypeDto) {
        if (currencyTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        CurrencyType result = currencyTypeMapper.selectById(currencyTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String msg = validateUpdate(currencyTypeDto);
        if (GeneralTool.isEmpty(msg)) {
            CurrencyType currencyType = BeanCopyUtils.objClone(currencyTypeDto, CurrencyType::new);
            if (StringUtils.isNotBlank(currencyType.getPublicLevel())&&currencyType.getPublicLevel().startsWith(",")){
                currencyType.setPublicLevel(currencyType.getPublicLevel().substring(1));
            }
            utilService.updateUserInfoToEntity(currencyType);
            currencyTypeMapper.updateById(currencyType);
        } else {
            throw new GetServiceException(msg);
        }
        return findCurrencyTypeById(currencyTypeDto.getId());
    }

    @Override
    public List<CurrencyTypeVo> getCurrencyTypes(CurrencyTypeDto currencyTypeDto, Page page) {
//        Example example = new Example(CurrencyType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(currencyTypeVo)) {
//            if (GeneralTool.isNotEmpty(currencyTypeVo.getKeyWord())) {
//                criteria.andLike("typeName", "%" + currencyTypeVo.getKeyWord() + "%");
//                criteria.orLike("num", "%" + currencyTypeVo.getKeyWord() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<CurrencyType> currencyTypes = currencyTypeMapper.selectByExample(example);
//        page.restPage(currencyTypes);
        LambdaQueryWrapper<CurrencyType> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(currencyTypeDto)) {
            if (GeneralTool.isNotEmpty(currencyTypeDto.getKeyWord())) {
                wrapper.like(CurrencyType::getTypeName, currencyTypeDto.getKeyWord());
                wrapper.or();
                wrapper.like(CurrencyType::getNum, currencyTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(CurrencyType::getViewOrder);
        IPage<CurrencyType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        page.setAll((int) pages.getTotal());
        List<CurrencyType> currencyTypes = pages.getRecords();
        List<CurrencyTypeVo> convertDatas = new ArrayList<>();
        for (CurrencyType currencyType : currencyTypes) {
            CurrencyTypeVo currencyTypeVo = BeanCopyUtils.objClone(currencyType, CurrencyTypeVo::new);
            //设置全名
            String fullName = currencyTypeVo.getTypeName();
            if (GeneralTool.isNotEmpty(currencyTypeVo.getNum())) {
                fullName = fullName + "（" + currencyTypeVo.getNum() + "）";
            }
            currencyTypeVo.setFullName(fullName);
            //设置公开对象
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(currencyTypeVo.getPublicLevel())) {
                String publicLevel = currencyTypeVo.getPublicLevel();
                if(publicLevel.startsWith(",")){
                    publicLevel = publicLevel.substring(1);
                }
                List<String> result = Arrays.asList(publicLevel.split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                currencyTypeVo.setPublicLevelName(stringJoiner.toString());
            }
            convertDatas.add(currencyTypeVo);
        }
        return convertDatas;
    }

    @Override
    public void movingOrder(List<CurrencyTypeDto> currencyTypeDtos) {
        if (GeneralTool.isEmpty(currencyTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        CurrencyType ro = BeanCopyUtils.objClone(currencyTypeDtos.get(0), CurrencyType::new);
        Integer oneorder = ro.getViewOrder();
        CurrencyType rt = BeanCopyUtils.objClone(currencyTypeDtos.get(1), CurrencyType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        currencyTypeMapper.updateById(ro);
        currencyTypeMapper.updateById(rt);
    }

    @Override
    public List<CurrencyTypeVo> getCurrencyTypeList() {
//        Example example = new Example(CurrencyType.class);
//        example.orderBy("viewOrder").desc();
//        List<CurrencyType> cts = currencyTypeMapper.selectByExample(example);

        List<CurrencyType> cts = this.currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .orderByDesc(CurrencyType::getViewOrder));
        List<CurrencyTypeVo> dtos = new ArrayList<>();
        for (CurrencyType ct : cts) {
            CurrencyTypeVo dto = BeanCopyUtils.objClone(ct, CurrencyTypeVo::new);
            String fullName = dto.getTypeName();
            if (GeneralTool.isNotEmpty(dto.getNum())) {
                fullName = fullName + "（" + dto.getNum() + "）";
            }
            dto.setFullName(fullName);
            dtos.add(dto);
        }
        return dtos;
    }

    @Override
    public List<CurrencyTypeVo> getCurrencyTypeSelect() {
        List<CurrencyType> cts = currencyTypeMapper.selectList(Wrappers.<CurrencyType>lambdaQuery().ne(CurrencyType::getNum, "FCY").orderByDesc(CurrencyType::getViewOrder));
        List<CurrencyTypeVo> dtos = new ArrayList<>();
        for (CurrencyType ct : cts) {
            CurrencyTypeVo dto = BeanCopyUtils.objClone(ct, CurrencyTypeVo::new);
            String fullName = dto.getTypeName();
            if (GeneralTool.isNotEmpty(dto.getNum())) {
                fullName = fullName + "（" + dto.getNum() + "）";
            }
            dto.setFullName(fullName);
            dtos.add(dto);
        }
        return dtos;
    }

    @Override
    public String getCurrencyNameByNum(String num) {
        return currencyTypeMapper.getCurrencyNameByNum(num);
    }

    @Override
    public Map<String, String> getCurrencyNamesByNums(Set<String> nums) {
        Map<String, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(nums)) {
            return map;
        }
//        Example example = new Example(CurrencyType.class);
//        example.createCriteria().andIn("num",nums);
//        List<CurrencyType> currencyTypes = currencyTypeMapper.selectByExample(example);
        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .in(CurrencyType::getNum, nums));
        if (GeneralTool.isEmpty(currencyTypes)) {
            return map;
        }
        for (CurrencyType currencyType : currencyTypes) {
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isEmpty(currencyType.getTypeName())) {
                //如果为空,则直接返回编号
                sb.append(currencyType.getNum());
            } else {
                sb.append(currencyType.getTypeName());
                sb.append("（");
                sb.append(currencyType.getNum());
                sb.append("）");
            }
            map.put(currencyType.getNum(), sb.toString());
        }
        return map;
    }


    @Override
    public Map<String, String> getNewCurrencyNamesByNums(Set<String> nums) {
        Map<String, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(nums)) {
            return map;
        }
//        Example example = new Example(CurrencyType.class);
//        example.createCriteria().andIn("num",nums);
//        List<CurrencyType> currencyTypes = currencyTypeMapper.selectByExample(example);

        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .in(CurrencyType::getNum, nums));

        if (GeneralTool.isEmpty(currencyTypes)) {
            return map;
        }
        for (CurrencyType currencyType : currencyTypes) {
            map.put(currencyType.getNum(), currencyType.getTypeName());
        }
        return map;
    }

    @Override
    public Map<String, String> getAllCurrencyTypeNames() {
        Map<String, String> map = new HashMap<>();
        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(null);
        if (GeneralTool.isEmpty(currencyTypes)) {
            return map;
        }
        for (CurrencyType currencyType : currencyTypes) {
            map.put(currencyType.getNum(), currencyType.getTypeName());
        }
        return map;
    }

    /**
     * 根据PublicLevel获取币种
     * @param key
     * @return
     */
    @Override
    public List<CurrencyTypeVo> getCurrencyByPublicLevel(Integer key) {
        List<CurrencyTypeVo> currencyTypeVos = currencyTypeMapper.getCurrencyByPublicLevel(key);
        return currencyTypeVos;
    }

    @Override
    public Map<String, CurrencyTypeVo> getCurrencyTypeDtoByNums(Set<String> currencyNums) {
        Map<String, CurrencyTypeVo> map = new HashMap<>();

        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .in(CurrencyType::getNum, currencyNums));
        if (GeneralTool.isEmpty(currencyTypes)) {
            return map;
        }
        return currencyTypes.stream().collect(Collectors.toMap(CurrencyType::getNum, currencyType -> BeanCopyUtils.objClone(currencyType, CurrencyTypeVo::new)));
    }

    private String validateAdd(CurrencyTypeDto currencyTypeDto) {
//        Example example = new Example(CurrencyType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", currencyTypeVo.getTypeName());
//        criteria.orEqualTo("num", currencyTypeVo.getNum());
//        List<CurrencyType> list = this.currencyTypeMapper.selectByExample(example);

        List<CurrencyType> list = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .eq(CurrencyType::getTypeName, currencyTypeDto.getTypeName())
                .or()
                .eq(CurrencyType::getNum, currencyTypeDto.getNum()));

        StringJoiner stringJoiner = new StringJoiner("，");
        for (CurrencyType currencyType : list) {
            if (currencyType.getNum().equals(currencyTypeDto.getNum())) {
                stringJoiner.add("货币类型编号已存在");
            }
            if (currencyType.getTypeName().equals(currencyTypeDto.getTypeName())) {
                stringJoiner.add("货币类型名称已存在");
            }
        }
        return stringJoiner.toString();
    }

    private String validateUpdate(CurrencyTypeDto currencyTypeDto) {
//        Example example = new Example(CurrencyType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", currencyTypeVo.getTypeName());
//        criteria.orEqualTo("num", currencyTypeVo.getNum());
//        List<CurrencyType> list = this.currencyTypeMapper.selectByExample(example);

        List<CurrencyType> list = currencyTypeMapper.selectList(Wrappers.<CurrencyType>query().lambda()
                .eq(CurrencyType::getTypeName, currencyTypeDto.getTypeName())
                .or()
                .eq(CurrencyType::getNum, currencyTypeDto.getNum()));

        StringJoiner stringJoiner = new StringJoiner("，");
        for (CurrencyType currencyType : list) {
            if (!currencyTypeDto.getId().equals(currencyType.getId())) {
                if (currencyType.getNum().equals(currencyTypeDto.getNum())) {
                    stringJoiner.add("货币类型编号已存在");
                }
                if (currencyType.getTypeName().equals(currencyTypeDto.getTypeName())) {
                    stringJoiner.add("货币类型名称已存在");
                }
            }
        }
        return stringJoiner.toString();
    }
}
