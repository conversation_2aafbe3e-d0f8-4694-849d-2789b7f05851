package com.get.financecenter.utils;

import com.get.salecenter.vo.SelItem;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SelItemUtils {

    public static Map<Long, Object> convert(List<SelItem> data) {
        Map<Long, Object> convertMap = new HashMap<>(data.size());
        for (SelItem selItem : data) {
            convertMap.put(selItem.getKeyId(), selItem.getVal());
        }
        return convertMap;
    }
}
