package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CommentMapper;
import com.get.financecenter.dto.CommentDto;
import com.get.financecenter.vo.FCommentVo;
import com.get.financecenter.entity.FComment;
import com.get.financecenter.service.ICommentService;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/1/5 11:30
 * @verison: 1.0
 * @description:
 */
@Service
public class CommentServiceImpl extends BaseServiceImpl<CommentMapper, FComment> implements ICommentService {
    @Resource
    private CommentMapper commentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<FCommentVo> datas(CommentDto commentDto, Page page) {
//        Example example = new Example(Comment.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(commentVo)) {
//            if (GeneralTool.isNotEmpty(commentVo.getFkTableName())) {
//                criteria.andEqualTo("fkTableName", commentVo.getFkTableName());
//            }
//            if (GeneralTool.isNotEmpty(commentVo.getFkTableId())) {
//                criteria.andEqualTo("fkTableId", commentVo.getFkTableId());
//            }
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Comment> comments = commentMapper.selectByExample(example);
//        page.restPage(comments);
//        List<CommentVo> commentDtos = comments.stream().map(comment -> BeanCopyUtils.objClone(comment, CommentVo.class)).collect(Collectors.toList());

        LambdaQueryWrapper<FComment> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getFkTableName())) {
                wrapper.eq(FComment::getFkTableName, commentDto.getFkTableName());
            }
            if (GeneralTool.isNotEmpty(commentDto.getFkTableId())) {
                wrapper.eq(FComment::getFkTableId, commentDto.getFkTableId());
            }
        }
        IPage<FComment> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<FComment> comments = pages.getRecords();
        List<FCommentVo> commentDtos = BeanCopyUtils.copyListProperties(comments, FCommentVo::new);
        //返回创建人员工id
        Set<String> createUsers = commentDtos.stream().map(FCommentVo::getGmtCreateUser).collect(Collectors.toSet());
        List<StaffVo> staffVos = new ArrayList<>();
        Result<List<StaffVo>> result = permissionCenterClient.getStaffByCreateUsers(createUsers);
        if (result.isSuccess() && result.getData() != null) {
            staffVos.addAll(result.getData());
        }
        Map<String, Long> staffMap = new HashMap<>();
        for (StaffVo staffVo : staffVos) {
            staffMap.put(staffVo.getLoginId(), staffVo.getId());
        }
        //TODO 改过
//        for (FCommentVo commentDto : commentDtos) {
//            commentDto.setFkStaffId(staffMap.get(commentDto.getGmtCreateUser()));
//        }
        for (FCommentVo comment : commentDtos) {
            comment.setFkStaffId(staffMap.get(comment.getGmtCreateUser()));
        }
        return commentDtos;
    }

    @Override
    public void addComment(FComment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.insertSelective(comment);
    }

    @Override
    public void updateComment(FComment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.updateById(comment);
    }

    @Override
    public void delete(Long id) {
        commentMapper.deleteById(id);
    }
}
