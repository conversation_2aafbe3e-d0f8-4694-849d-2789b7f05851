package com.get.financecenter.controller;

import com.get.financecenter.service.ICommonService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2021/4/8 15:52
 * @verison: 1.0
 * @description: 公共接口控制器
 */
@Api(tags = "公共接口")
@RestController
@RequestMapping("finance/common")
public class CommonController {

    @Resource
    private ICommonService commonService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :修改流程表单状态
     * @Param [status, tableName, businessKey]
     * <AUTHOR>
     */
 /*   @ApiIgnore
    @PostMapping(value = "changeStatus")
    public ResponseBo changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey) {
        commonService.changeStatus(status, tableName, businessKey);
        return ResponseBo.ok();
    }*/

}
