package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.PaymentApplicationFormItemVo;
import com.get.financecenter.entity.PaymentApplicationFormItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付申请单子项
 */
@Mapper
public interface PaymentApplicationFormItemMapper extends BaseMapper<PaymentApplicationFormItem> {

    List<PaymentApplicationFormItemVo> getDataByFkMpayId(@Param("pid") Long pid);
}