package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.dto.*;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.service.*;
import com.get.financecenter.utils.SelItemUtils;
import com.get.financecenter.vo.*;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentServiceFeeVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/21
 * @TIME: 17:07
 * @Description:
 **/
@Service
public class PaymentFormItemServiceImpl extends BaseServiceImpl<PaymentFormItemMapper, PaymentFormItem> implements IPaymentFormItemService {
    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IPaymentFormService paymentFormService;
    @Resource
    private AsyncExportService asyncExportService;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private PaymentFormMapper paymentFormMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;


    @Override
    public List<PaymentFormItemVo> datas(PaymentFormItemDto paymentFormItemDto, Page page) {
//        Example example = new Example(PaymentFormItem.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkPaymentFormId())) {
//            criteria.andEqualTo("fkPaymentFormId", paymentFormItemVo.getFkPaymentFormId());
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkCompanyId())) {
//            List<Long> formIds = getFormIds(paymentFormItemVo.getFkCompanyId());
//            criteria.andIn("fkPaymentFormId", formIds);
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkTypeKey())) {
//            List<Long> payablePlanId = feignSaleService.getPayablePlanId(paymentFormItemVo.getFkTypeKey(), paymentFormItemVo.getFkTypeTargetId());
//            if (GeneralTool.isEmpty(payablePlanId)) {
//                payablePlanId = new ArrayList<>();
//                payablePlanId.add(0L);
//            }
//            criteria.andIn("fkPayablePlanId", payablePlanId);
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkTypeTargetId())) {
//            List<Long> payablePlanId = feignSaleService.getPayablePlanId(paymentFormItemVo.getFkTypeKey(), paymentFormItemVo.getFkTypeTargetId());
//            if (GeneralTool.isEmpty(payablePlanId)) {
//                payablePlanId = new ArrayList<>();
//                payablePlanId.add(0L);
//            }
//            criteria.andIn("fkPayablePlanId", payablePlanId);
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getBeginTime())) {
//            criteria.andGreaterThanOrEqualTo("gmtCreate", paymentFormItemVo.getBeginTime());
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getEndTime())) {
//            criteria.andLessThanOrEqualTo("gmtCreate", paymentFormItemVo.getEndTime());
//        }
//        if (GeneralTool.isNotEmpty(paymentFormItemVo.getSummary())) {
//            criteria.andLike("summary", "%" + paymentFormItemVo.getSummary() + "%");
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectByExample(example);
//        page.restPage(paymentFormItems);

        LambdaQueryWrapper<PaymentFormItem> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getFkPaymentFormId())) {
            wrapper.eq(PaymentFormItem::getFkPaymentFormId, paymentFormItemDto.getFkPaymentFormId());
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getFkCompanyId())) {
            List<Long> formIds = getFormIds(paymentFormItemDto.getFkCompanyId());
            wrapper.in(PaymentFormItem::getFkPaymentFormId, formIds);
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getFkTypeKey())) {
            //111111
            Result<List<Long>> result = saleCenterClient.getPayablePlanId(paymentFormItemDto.getFkTypeKey(), paymentFormItemDto.getFkTypeTargetId());
            List<Long> payablePlanId = result.getData();//待修改111111
            if (GeneralTool.isEmpty(payablePlanId)) {
                payablePlanId = new ArrayList<>();
                payablePlanId.add(0L);
            }
            wrapper.in(PaymentFormItem::getFkPayablePlanId, payablePlanId);
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getFkTypeTargetId())) {
            //111111
            List<Long> payablePlanId = saleCenterClient.getPayablePlanId(paymentFormItemDto.getFkTypeKey(), paymentFormItemDto.getFkTypeTargetId()).getData();
            if (GeneralTool.isEmpty(payablePlanId)) {
                payablePlanId = new ArrayList<>();
                payablePlanId.add(0L);
            }
            wrapper.in(PaymentFormItem::getFkPayablePlanId, payablePlanId);
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getBeginTime())) {
            wrapper.ge(PaymentFormItem::getGmtCreate, paymentFormItemDto.getBeginTime());
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getEndTime())) {
            wrapper.le(PaymentFormItem::getGmtCreate, paymentFormItemDto.getEndTime());
        }
        if (GeneralTool.isNotEmpty(paymentFormItemDto.getSummary())) {
            wrapper.like(PaymentFormItem::getSummary, paymentFormItemDto.getSummary());
        }
        IPage<PaymentFormItem> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<PaymentFormItem> paymentFormItems = pages.getRecords();
        List<PaymentFormItemVo> collect = BeanCopyUtils.copyListProperties(paymentFormItems, PaymentFormItemVo::new);
        if (GeneralTool.isEmpty(collect)) {
            return null;
        }
        Set<Long> planIds = collect.stream().map(PaymentFormItemVo::getFkPayablePlanId).collect(Collectors.toSet());
        Map<Long, PayablePlanVo> offerItemMap = saleCenterClient.findOfferItemByPayIds(planIds).getData();
        Map<Long, String> companyMap = getCompanyMap();
        Set<Long> pIds = collect.stream().map(PaymentFormItemVo::getFkPayablePlanId).filter(Objects::nonNull).collect(Collectors.toSet());
        Result<List<PayablePlanVo>> result = saleCenterClient.getPayablePlanDetailsByIds(pIds);
        List<PayablePlanVo> plansDetail;
        if (result.isSuccess() && result.getData() != null) {
            plansDetail = result.getData();
        } else {
            plansDetail = Collections.emptyList();
        }
        Map<Long, PayablePlanVo> planDtoHashMap = new HashMap<>();
        for (PayablePlanVo planDto : plansDetail) {
            planDtoHashMap.put(planDto.getId(), planDto);
        }
        Set<Long> itemIds = collect.stream().map(PaymentFormItemVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SelItem> companyIdByItemIds = paymentFormItemMapper.getCompanyIdByItemIds(itemIds);
        Map<Long, Object> companyItemMap = SelItemUtils.convert(companyIdByItemIds);
        Set<Long> pfIds = collect.stream().map(PaymentFormItemVo::getFkPaymentFormId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SelItem> currencyByFormIds = paymentFormService.getCurrencyByFormIds(pfIds);
        Map<Long, Object> currencyMap = SelItemUtils.convert(currencyByFormIds);
        Map<String, String> allCurrencyTypeNames = currencyTypeService.getAllCurrencyTypeNames();
        if (GeneralTool.isEmpty(planIds)){
            planIds.add(0L);
        }
        List<AlreadyPayVo> alreadyPays = paymentFormItemMapper.getAlreadyPayByPlanIds(planIds);
        Map<Long, List<AlreadyPayVo>> alreadyPayMap = null;
        if (GeneralTool.isNotEmpty(alreadyPays)){
            alreadyPayMap = alreadyPays.stream().collect(Collectors.groupingBy(AlreadyPayVo::getFkPayablePlanId));
        }
        setDatasName(collect, planDtoHashMap, companyItemMap, currencyMap, companyMap, offerItemMap,allCurrencyTypeNames,alreadyPayMap);
        return collect;
    }

    @Override
    public void batchUpdate(List<PaymentFormItem> paymentFormItems) {
        if (GeneralTool.isNotEmpty(paymentFormItems)) {
            updateBatchById(paymentFormItems);
        }
    }


    /**
     * 导出付款款单子项列表
     *
     * @param
     * @
     */
    @Override
    public void exportPaymentFormItemExcel(PaymentFormDto paymentFormDto) {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.asyncExportPaymentFormItemExcel(paymentFormDto,headerMap,user,locale);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<PaymentFormItemDto> paymentFormItemDtos) {
        if (GeneralTool.isEmpty(paymentFormItemDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        Long formId = paymentFormItemDtos.get(0).getFkPaymentFormId();
        //付款单金额
        BigDecimal amountTotal = paymentFormService.getAmountByFormId(formId);

        BigDecimal totalPay = new BigDecimal("0");
        //付款单已绑定实付金额
        BigDecimal alreadyPayAmount = paymentFormItemMapper.getAmountByFormId(formId, null);
        totalPay = totalPay.add(alreadyPayAmount);
        for (PaymentFormItemDto paymentFormItemDto : paymentFormItemDtos) {
            //总新增的付款
            totalPay = totalPay.add(paymentFormItemDto.getAmountPayment());

            //应付计划
//            Long fkPayablePlanId = paymentFormItemVo.getFkPayablePlanId();
//            Example example = new Example(PaymentFormItem.class);
//            example.createCriteria().andEqualTo("fkPayablePlanId", fkPayablePlanId);
//            //应付计划绑定的收款单子项
//            List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectByExample(example);

//            List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>query().lambda().eq(PaymentFormItem::getFkPayablePlanId, fkPayablePlanId));
//            //应付计划应付金额 111111
//            Result<BigDecimal> result = saleCenterClient.getPayablePlanAmountById(fkPayablePlanId);
//            if (!result.isSuccess()) {
//                throw new GetServiceException(result.getMessage());
//            }
//            BigDecimal payablePlanAmount = result.getData();//待修改111111
//
//            //该计划总实付金额 = 本次该计划付款金额+以往该计划付款金额  付款金额= 付款金额（折合应付币种金额）+ 汇率调整*汇率（折合应付币种汇率）
//            BigDecimal totalReceivable = paymentFormItemVo.getAmountPayable().
//                    add(paymentFormItemVo.getAmountExchangeRate());
//            if (GeneralTool.isNotEmpty(paymentFormItems)) {
//                for (PaymentFormItem paymentFormItem : paymentFormItems) {
//                    totalReceivable = totalReceivable.add(paymentFormItem.getAmountPayable())
//                            .add(paymentFormItem.getAmountExchangeRate());
//                }
//            }
//            //该计划总共实付金额 不能大于该应付计划的应付金额
//            if (payablePlanAmount.compareTo(totalReceivable) < 0) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("totalPay_greater_payablePlanAmount"));
//            }
        }
        if (amountTotal.compareTo(totalPay) < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("totalPay_greater_formAmount"));
        }
        List<PaymentFormItem> paymentFormItems = new ArrayList<>();
        for (PaymentFormItemDto paymentFormItemDto : paymentFormItemDtos) {
            PaymentFormItem paymentFormItem = BeanCopyUtils.objClone(paymentFormItemDto, PaymentFormItem::new);
            paymentFormItem.setAmountExchangeRate(BigDecimal.ZERO);
            utilService.setCreateInfo(paymentFormItem);
            paymentFormItems.add(paymentFormItem);
//            paymentFormItemMapper.insert(paymentFormItem);
        }
        if (GeneralTool.isNotEmpty(paymentFormItems)) {
            saveBatch(paymentFormItems);
        }
        //手工单
        List<Long> fkPayablePlanIdList = paymentFormItemDtos.stream().map(PaymentFormItemDto::getFkPayablePlanId).collect(Collectors.toList());
        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, fkPayablePlanIdList).eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.UNSETTLED.key)
                .eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key));

    }


    @Override
    public PaymentFormItemVo update(PaymentFormItemDto paymentFormItemDto) {
        if (GeneralTool.isEmpty(paymentFormItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        PayablePlanVo payablePlanVo = saleCenterClient.getPayablePlanDetail(paymentFormItemDto.getFkPayablePlanId()).getData();
        if (!(TableEnum.INSTITUTION_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())
                ||TableEnum.SALE_BUSINESS_PROVIDER.key.equals(payablePlanVo.getFkTypeKey()))) {
            if (!ProjectExtraEnum.UNSETTLED.key.equals(payablePlanVo.getStatusSettlement())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLAN_PROGRESS"));
            }
        }
        Long formId = paymentFormItemDto.getFkPaymentFormId();
        //付款单金额
        BigDecimal amountTotal = paymentFormService.getAmountByFormId(formId);

        //该付款单已绑定的付款单子项 （排除自己）
        List<PaymentFormItem> paymentFormItemList = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>query().lambda().eq(PaymentFormItem::getFkPaymentFormId, formId)
                .ne(PaymentFormItem::getId, paymentFormItemDto.getId()));


        //应付计划总金额上限判断
        //所绑定的应付计划
//        Long fkPayablePlanId = paymentFormItemVo.getFkPayablePlanId();
//        //应付计划绑定的收款单子项
//        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>query().lambda()
//                .eq(PaymentFormItem::getFkPayablePlanId, fkPayablePlanId)
//                .ne(PaymentFormItem::getId, paymentFormItemVo.getId()));
//
//
//        //应付计划应付金额
//        BigDecimal payablePlanAmount = saleCenterClient.getPayablePlanAmountById(fkPayablePlanId).getData();

        //该计划总实付金额 = 本次该计划付款金额+以往该计划付款金额  付款金额= 付款金额（折合应付币种金额）+ 汇率调整
//        BigDecimal totalReceivable = paymentFormItemVo.getAmountPayment().
//                add(paymentFormItemVo.getAmountExchangeRate());
//        if (GeneralTool.isNotEmpty(paymentFormItems)) {
//            for (PaymentFormItem paymentFormItem : paymentFormItems) {
//                totalReceivable = totalReceivable.add(paymentFormItem.getAmountPayable())
//                        .add(paymentFormItem.getAmountExchangeRate());
//            }
//        }
//        //该计划总共实付金额 不能大于该应付计划的应付金额
//        if (payablePlanAmount.compareTo(totalReceivable) < 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("totalPay_greater_payablePlanAmount"));
//        }

        List<PaymentFormItemDto> paymentFormItemDtos = BeanCopyUtils.copyListProperties(paymentFormItemList, PaymentFormItemDto::new);
        paymentFormItemDtos.add(paymentFormItemDto);


        BigDecimal totalPay = new BigDecimal("0");
        //应付计划应付金额
//        BigDecimal alreadyPayAmount = paymentFormItemMapper.getAmountByFormId(formId, paymentFormItemVo.getId());
//        totalPay = totalPay.add(alreadyPayAmount);
        //该付款单已绑定的付款单子项 + 自己
        for (PaymentFormItemDto itemVo : paymentFormItemDtos) {
            //总新增的付款
            totalPay = totalPay.add(itemVo.getAmountPayment());
        }
        if (amountTotal.compareTo(totalPay) < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("totalPay_greater_formAmount"));
        }
        PaymentFormItem paymentFormItem = BeanCopyUtils.objClone(paymentFormItemDto, PaymentFormItem::new);
        utilService.updateUserInfoToEntity(paymentFormItem);
        paymentFormItemMapper.updateById(paymentFormItem);
        return findPaymentFormItemById(paymentFormItemDto.getId());
    }

    @Override
    public PaymentFormItemVo findPaymentFormItemById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentFormItem paymentFormItem = paymentFormItemMapper.selectById(id);
        PaymentFormItemVo paymentFormItemVo = BeanCopyUtils.objClone(paymentFormItem, PaymentFormItemVo::new);
        paymentFormItemVo.setAlreadyPayDtos(paymentFormItemMapper.getAlreadyPayByPlanId(paymentFormItem.getFkPayablePlanId()));
        Map<Long, String> companyMap = getCompanyMap();
        setName(paymentFormItemVo, companyMap);
        return paymentFormItemVo;
    }

    @Override
    public List<AlreadyPayVo> getAlreadyPayByPlanIds(Set<Long> planIds) {
        return paymentFormItemMapper.getAlreadyPayByPlanIds(planIds);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentFormItem paymentFormItem = paymentFormItemMapper.selectById(id);
        PayablePlanVo payablePlanVo = saleCenterClient.getPayablePlanDetail(paymentFormItem.getFkPayablePlanId()).getData();
        if (!(TableEnum.INSTITUTION_PROVIDER.key.equals(payablePlanVo.getFkTypeKey())
                ||TableEnum.SALE_BUSINESS_PROVIDER.key.equals(payablePlanVo.getFkTypeKey()))) {
            if (!ProjectExtraEnum.UNSETTLED.key.equals(payablePlanVo.getStatusSettlement())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLAN_PROGRESS"));
            }
        }
        paymentFormItemMapper.deleteById(id);
    }

    @Override
    public List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM_ITEM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    @Override
    public List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM_ITEM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<PaymentFormVo> getPayFormList(Long planId) {
        if (GeneralTool.isEmpty(planId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PaymentFormVo> payFormList = paymentFormService.getPayFormList(planId);
        Set<String> currencyTypeNums = payFormList.stream().map(PaymentFormVo::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        for (PaymentFormVo paymentFormVo : payFormList) {
            paymentFormVo.setFkCurrencyTypeName(currencyNamesByNums.get(paymentFormVo.getFkCurrencyTypeNum()));
        }
        return payFormList;
    }

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 19:01 2021/11/22
     * <AUTHOR>
     */
    @Override
    public List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds) {
        if (GeneralTool.isEmpty(planIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PaymentFormVo> payFormList = paymentFormService.getPayFormListFeignByPlanIds(planIds);
        Set<String> currencyTypeNums = payFormList.stream().map(PaymentFormVo::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        for (PaymentFormVo paymentFormVo : payFormList) {
            paymentFormVo.setFkCurrencyTypeName(currencyNamesByNums.get(paymentFormVo.getFkCurrencyTypeNum()));
        }
        return payFormList;
    }

    @Override
    public Integer getPayFormItemCount(Set<Long> planIds) {
        if (planIds.isEmpty()) {
            return 0;
        }
        return paymentFormItemMapper.getPayFormItemCount(planIds);
    }

    @Override
    public PaymentFormItemVo getPayableFormItem(Long payPlanId, Long formId) {
        //111111
        Result<PayablePlanVo> result = saleCenterClient.getPayablePlanDetail(payPlanId);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        PayablePlanVo payablePlanVo = result.getData();
        if (GeneralTool.isEmpty(payablePlanVo)) {
            return null;
        }
        PaymentFormItemVo paymentFormItemVo = new PaymentFormItemVo();

        paymentFormItemVo.setFkPayablePlanId(payablePlanVo.getId());
        //计划类型
        paymentFormItemVo.setFkTypeName(payablePlanVo.getFkTypeName());
        //目标对象
        paymentFormItemVo.setTargetNames(payablePlanVo.getTargetNames());
        //应付币种
        String payPlanCurrency = payablePlanVo.getFkCurrencyTypeNum();
        paymentFormItemVo.setPayablePlanCurrency(currencyTypeService.getCurrencyNameByNum(payPlanCurrency));
        //总应付金额
        BigDecimal payableAmount = payablePlanVo.getPayableAmount();
        paymentFormItemVo.setPayablePlanAmount(payableAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //实付币种
        String payFormCurrency = paymentFormService.getCurrencyByFormId(formId);
        paymentFormItemVo.setPayFormCurrency(currencyTypeService.getCurrencyNameByNum(payFormCurrency));
        //折合应付汇率(计算)
//        BigDecimal exchangeRate = getRate(payPlanCurrency, payFormCurrency);
        //折合应收汇率（前端显示汇率）
        BigDecimal showExchangeRate = getRate(payFormCurrency, payPlanCurrency);
        paymentFormItemVo.setExchangeRatePayable(showExchangeRate);

        //实付金额
        //需要计算还未完成付款的金额
        Long planId = payablePlanVo.getId();
        List<AlreadyPayVo> alreadyPay = paymentFormItemMapper.getAlreadyPayByPlanId(planId);
        Set<String> currencyNums = alreadyPay.stream().map(AlreadyPayVo::getAlreadyPayCurrency).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyNums);

        paymentFormItemVo.setAlreadyPayDtos(alreadyPay);
        //该学习计划累计已付金额
        BigDecimal totalBeforePay = new BigDecimal("0");
        if (GeneralTool.isNotEmpty(alreadyPay)) {
            for (AlreadyPayVo alreadyPayVo : alreadyPay) {
//                alreadyPayDto.setAlreadyPayCurrencyName(currencyNamesByNums.get(alreadyPayDto.getAlreadyPayCurrency()));
//                alreadyPayDto.setTargetNames(payablePlanVo.getTargetNames());
//                String beforeFormCurrency = alreadyPayDto.getAlreadyPayCurrency();
//                //汇率
//                BigDecimal formRate = getRate(beforeFormCurrency, payFormCurrency);
//                BigDecimal beforePayAmountPayable = alreadyPayDto.getAlreadyPayAmount();
//                //已付金额
//                BigDecimal beforePayAmountExchange = beforePayAmountPayable.multiply(formRate);
//                //汇率调整
//                BigDecimal alreadyExchangeRate = alreadyPayDto.getAlreadyExchangeRate();
//
//                totalBeforePay = totalBeforePay.add(beforePayAmountExchange).add(alreadyExchangeRate);

                //收款金额（折合应收币种金额）
                BigDecimal beforeReceiptAmountExchange = alreadyPayVo.getAmountPayable();
                //汇率调整
                BigDecimal alreadyExchangeRate = alreadyPayVo.getAlreadyExchangeRate();

                totalBeforePay = totalBeforePay.add(beforeReceiptAmountExchange).add(alreadyExchangeRate);
            }
        }
        if (payableAmount.compareTo(totalBeforePay) <= 0) {
            //已收齐
            throw new GetServiceException(LocaleMessageUtils.getMessage("plan_collect_amount"));
        }
        //实付金额
        BigDecimal nowAmountPayment = payableAmount.divide(showExchangeRate, 2, RoundingMode.HALF_UP).subtract(totalBeforePay.divide(showExchangeRate, 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
        paymentFormItemVo.setAmountPayment(nowAmountPayment.setScale(2, BigDecimal.ROUND_HALF_UP));
        //折合金额 付款金额
        BigDecimal amountPayable = nowAmountPayment.multiply(showExchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        paymentFormItemVo.setAmountPayable(amountPayable);
        //汇率调整 应付金额-已付折合金额- 折合金额
        BigDecimal amountExchangeRate = payableAmount.subtract(totalBeforePay).subtract(amountPayable);
        paymentFormItemVo.setAmountExchangeRate(amountExchangeRate);
        //港币汇率
        BigDecimal hkdRate = getRate(payFormCurrency, "HKD");
        paymentFormItemVo.setExchangeRateHkd(hkdRate);
        //折合港币
        BigDecimal amountHkd = hkdRate.multiply(nowAmountPayment);
        paymentFormItemVo.setAmountHkd(amountHkd.setScale(2, BigDecimal.ROUND_HALF_UP));
        //人名币汇率
        BigDecimal rmbRate = getRate(payFormCurrency, "CNY");
        paymentFormItemVo.setExchangeRateRmb(rmbRate);
        //折合人名币
        BigDecimal amountRmb = rmbRate.multiply(nowAmountPayment);
        paymentFormItemVo.setAmountRmb(amountRmb.setScale(2, BigDecimal.ROUND_HALF_UP));

        return paymentFormItemVo;
    }

    @Override
    public List<BaseSelectEntity> getStudentOfferItemSelect(String tableName, String fkTypeKey, Long fkTypeTargetId) {
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(tableName)) {
            if (TableEnum.SALE_AGENT.key.equals(fkTypeKey)) {
                Result<List<BaseSelectEntity>> result = saleCenterClient.getOfferItemSelectByAgentId(tableName, fkTypeTargetId);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
                List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();//111111
                baseSelectEntities = result.getData();
                baseSelectEntities.removeIf(Objects::isNull);
                if (GeneralTool.isNotEmpty(baseSelectEntities)) {


                    Iterator<BaseSelectEntity> iterator = baseSelectEntities.iterator();
                    while (iterator.hasNext()) {
                        BaseSelectEntity baseSelectEntity = iterator.next();
                        //忽略已收齐
                        Long payPlanId = baseSelectEntity.getId();
                        //111111
                        Result<PayablePlanVo> resultPayablePlanDto = saleCenterClient.getPayablePlanDetail(payPlanId);
                        if (!result.isSuccess()) {
                            throw new GetServiceException(result.getMessage());
                        }
                        PayablePlanVo payablePlanVo = new PayablePlanVo();
                        payablePlanVo = resultPayablePlanDto.getData();
                        if (GeneralTool.isEmpty(payablePlanVo)) {
                            continue;
                        }
                        //应付币种
                        String payPlanCurrency = payablePlanVo.getFkCurrencyTypeNum();
                        //总应付金额
                        BigDecimal payableAmount = payablePlanVo.getPayableAmount();
                        //实付金额
                        //需要计算还未完成付款的金额
                        Long planId = payablePlanVo.getId();
                        List<AlreadyPayVo> alreadyPay = paymentFormItemMapper.getAlreadyPayByPlanId(planId);

                        BigDecimal totalBeforePay = new BigDecimal("0");
                        if (GeneralTool.isNotEmpty(alreadyPay)) {
                            for (AlreadyPayVo alreadyPayVo : alreadyPay) {
                                alreadyPayVo.setTargetNames(payablePlanVo.getTargetNames());
//                                String beforeFormCurrency = alreadyPayDto.getAlreadyPayCurrency();
                                //汇率
//                                BigDecimal formRate = getRate(beforeFormCurrency, payPlanCurrency);
                                //付款金额（折合应付币种金额）
                                BigDecimal beforePayAmountPayable = alreadyPayVo.getAmountPayable();
                                //已付金额
//                                BigDecimal beforePayAmountExchange = beforePayAmountPayable.multiply(formRate);
                                //汇率调整
                                BigDecimal alreadyExchangeRate = alreadyPayVo.getAlreadyExchangeRate();

                                totalBeforePay = totalBeforePay.add(beforePayAmountPayable).add(alreadyExchangeRate);
                            }
                        }
                        if (payableAmount.compareTo(totalBeforePay) <= 0) {
                            iterator.remove();
                        }
                    }
                }
                return baseSelectEntities;
            }
        }
        return null;
    }


    /**
     * Author Cream
     * Description : 创建付款单
     * Date 2022/5/7 15:12
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo quickCreatePaymentForm(QuickPaymentFormDto quickPaymentFormDto) {
        if (GeneralTool.isNull(quickPaymentFormDto.getPayablePlanId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Long payablePlanId = quickPaymentFormDto.getPayablePlanId();
        Result<PayablePlan> result = saleCenterClient.getPayableInfoById(payablePlanId);
        PayablePlan plan = result.getData();
        PaymentForm paymentForm = new PaymentForm();
        paymentForm.setFkTypeKey("m_agent");
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            Result<Long> agentId = saleCenterClient.getAgentIdByPayablePlanId(plan.getFkTypeTargetId());
            if (agentId.isSuccess()) {
                paymentForm.setFkTypeTargetId(agentId.getData());
            }
        } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            Result<Long> results = saleCenterClient.getAccommodationAgentId(plan.getFkTypeTargetId());
            if (results.isSuccess() && results.getData() != null) {
                paymentForm.setFkTypeTargetId(results.getData());
            }
        } else if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            Result<Long> results = saleCenterClient.getInsuranceAgentId(plan.getFkTypeTargetId());
            if (results.isSuccess() && results.getData() != null) {
                paymentForm.setFkTypeTargetId(results.getData());
            }
        }else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            Result<StudentServiceFeeVo> results = saleCenterClient.getServiceFeeById(plan.getFkTypeTargetId());
            if (results.isSuccess() && results.getData() != null) {
                paymentForm.setFkTypeTargetId(results.getData().getFkAgentId());
            }
        }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            paymentForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
            paymentForm.setFkTypeTargetId(plan.getFkTypeTargetId());
        }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(quickPaymentFormDto.getFkTypeKey())) {
            paymentForm.setFkTypeKey(TableEnum.SALE_BUSINESS_PROVIDER.key);
            paymentForm.setFkTypeTargetId(plan.getFkTypeTargetId());
        }
        if (paymentForm.getFkTypeTargetId() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_corresponding_agent"));
        }
        paymentForm.setFkCompanyId(plan.getFkCompanyId());
        paymentForm.setFkBankAccountId(quickPaymentFormDto.getTargetBankAccountId());
        paymentForm.setFkBankAccountIdCompany(quickPaymentFormDto.getPaymentBankAccountId());
        paymentForm.setNumBank(quickPaymentFormDto.getPaymentSerialNumber());
        String fkCurrencyTypeNum = quickPaymentFormDto.getFkCurrencyTypeNum();
        paymentForm.setFkCurrencyTypeNum(fkCurrencyTypeNum);
        BigDecimal paymentAmount = quickPaymentFormDto.getPaymentAmount();
        BigDecimal paymentFee = quickPaymentFormDto.getPaymentFee();
        paymentForm.setAmount(paymentAmount);
        paymentForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(fkCurrencyTypeNum, "HKD"));
        paymentForm.setAmountHkd(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateHkd()));
        paymentForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(fkCurrencyTypeNum, "CNY"));
        paymentForm.setAmountRmb(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateRmb()));
        paymentForm.setServiceFee(paymentFee);
        paymentForm.setStatus(1);
        utilService.setCreateInfo(quickPaymentFormDto);
        paymentForm.setGmtCreate(quickPaymentFormDto.getGmtCreate());
        paymentForm.setGmtCreateUser(quickPaymentFormDto.getGmtCreateUser());
        paymentFormMapper.insert(paymentForm);
        String payFormNum = GetStringUtils.getPayFormNum(paymentForm.getId());
        paymentForm.setNumSystem(payFormNum);
        paymentFormMapper.updateById(paymentForm);

        PaymentFormItem item = new PaymentFormItem();
        item.setFkPaymentFormId(paymentForm.getId());
        item.setFkPayablePlanId(payablePlanId);
        item.setAmountPayment(paymentAmount);
        item.setServiceFee(paymentFee);
        item.setAmountExchangeRate(BigDecimal.ZERO);
        item.setExchangeRatePayable(exchangeRateService.getRateByCurrency(plan.getFkCurrencyTypeNum(),paymentForm.getFkCurrencyTypeNum()));
        item.setAmountPayable(paymentAmount.add(paymentFee).multiply(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(),plan.getFkCurrencyTypeNum())));
        item.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
        item.setAmountHkd(paymentAmount.add(paymentFee).multiply(item.getExchangeRateHkd()));
        item.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
        item.setAmountRmb(paymentAmount.add(paymentFee).multiply(item.getExchangeRateRmb()));
        item.setGmtCreate(quickPaymentFormDto.getGmtCreate());
        item.setGmtCreateUser(quickPaymentFormDto.getGmtCreateUser());
        paymentFormItemMapper.insert(item);
        return SaveResponseBo.ok();
    }


    /**
     * 快速补单
     * @param quickPaymentFormDtos
     */
    public void quickCreatePaymentForms(List<QuickPaymentFormDto> quickPaymentFormDtos) {
        if (GeneralTool.isEmpty(quickPaymentFormDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Set<String> currencyTypeNums = quickPaymentFormDtos.stream().map(QuickPaymentFormDto::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String,BigDecimal> hkdRate = Maps.newHashMap();
        Map<String,BigDecimal> cnyRate = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            for (String currencyTypeNum : currencyTypeNums) {
                BigDecimal hkd = exchangeRateService.getRateByCurrency(currencyTypeNum, "HKD");
                hkdRate.put(currencyTypeNum,hkd);
                BigDecimal cny = exchangeRateService.getRateByCurrency(currencyTypeNum, "CNY");
                cnyRate.put(currencyTypeNum,cny);
            }
        }

        for (QuickPaymentFormDto quickPaymentFormDto : quickPaymentFormDtos) {
            if (GeneralTool.isNull(quickPaymentFormDto.getPayablePlanId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
            }
            Long payablePlanId = quickPaymentFormDto.getPayablePlanId();
            Result<PayablePlan> result = saleCenterClient.getPayableInfoById(payablePlanId);
            PayablePlan plan = result.getData();
            PaymentForm paymentForm = new PaymentForm();
            paymentForm.setFkTypeKey("m_agent");
            if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                Result<Long> agentId = saleCenterClient.getAgentIdByPayablePlanId(plan.getFkTypeTargetId());
                if (agentId.isSuccess()) {
                    paymentForm.setFkTypeTargetId(agentId.getData());
                }
            } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                Result<Long> results = saleCenterClient.getAccommodationAgentId(plan.getFkTypeTargetId());
                if (results.isSuccess() && results.getData() != null) {
                    paymentForm.setFkTypeTargetId(results.getData());
                }
            } else if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                Result<Long> results = saleCenterClient.getInsuranceAgentId(plan.getFkTypeTargetId());
                if (results.isSuccess() && results.getData() != null) {
                    paymentForm.setFkTypeTargetId(results.getData());
                }
            }else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                Result<StudentServiceFeeVo> results = saleCenterClient.getServiceFeeById(plan.getFkTypeTargetId());
                if (results.isSuccess() && results.getData() != null) {
                    paymentForm.setFkTypeTargetId(results.getData().getFkAgentId());
                }
            }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                paymentForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
                paymentForm.setFkTypeTargetId(plan.getFkTypeTargetId());
            }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(quickPaymentFormDto.getFkTypeKey())) {
                paymentForm.setFkTypeKey(TableEnum.SALE_BUSINESS_PROVIDER.key);
                paymentForm.setFkTypeTargetId(plan.getFkTypeTargetId());
            }
            if (paymentForm.getFkTypeTargetId() == null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_corresponding_agent"));
            }
            paymentForm.setFkCompanyId(plan.getFkCompanyId());
            //代理的首选账户
            if ("m_agent".equals(paymentForm.getFkTypeKey())){
                Map<Long, List<AgentContractAccountVo>> agentContractAccountDtoMap = saleCenterClient.getAgentContractAccountByAgentIds(Lists.newArrayList(paymentForm.getFkTypeTargetId())).getData();
                List<AgentContractAccountVo> agentContractAccountVos = agentContractAccountDtoMap.get(paymentForm.getFkTypeTargetId());
                if (GeneralTool.isNotEmpty(agentContractAccountVos)){
                    //TODO 改过
                   // List<AgentContractAccountVo> accountDtos = agentContractAccountVos.stream().filter(AgentContractAccount::getIsDefault).collect(Collectors.toList());
                    List<AgentContractAccountVo> accountDtos = agentContractAccountVos.stream().filter(AgentContractAccountVo::getIsDefault).collect(Collectors.toList());
                    if (GeneralTool.isNotEmpty(accountDtos)){
                        paymentForm.setFkBankAccountId(accountDtos.get(0).getId());
                    }else {
                        paymentForm.setFkBankAccountId(agentContractAccountVos.get(0).getId());
                    }
                }
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_corresponding_agent"));
            }
//            paymentForm.setFkBankAccountId(quickPaymentFormVo.getTargetBankAccountId());
            //批量找平写死账户
            paymentForm.setFkBankAccountIdCompany(1L);
            paymentForm.setNumBank(quickPaymentFormDto.getPaymentSerialNumber());
            String fkCurrencyTypeNum = quickPaymentFormDto.getFkCurrencyTypeNum();
            paymentForm.setFkCurrencyTypeNum(fkCurrencyTypeNum);
            BigDecimal paymentAmount = quickPaymentFormDto.getPaymentAmount();
            BigDecimal paymentFee = quickPaymentFormDto.getPaymentFee();
            paymentForm.setAmount(paymentAmount);
            paymentForm.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(fkCurrencyTypeNum))?hkdRate.get(fkCurrencyTypeNum):new BigDecimal("1.0000"));
            paymentForm.setAmountHkd(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateHkd()));
            paymentForm.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(fkCurrencyTypeNum))?cnyRate.get(fkCurrencyTypeNum):new BigDecimal("1.0000"));
            paymentForm.setAmountRmb(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateRmb()));
            paymentForm.setServiceFee(paymentFee);
            paymentForm.setStatus(1);
            paymentForm.setSummary(quickPaymentFormDto.getSummary());
            utilService.setCreateInfo(quickPaymentFormDto);
            paymentForm.setGmtCreate(quickPaymentFormDto.getGmtCreate());
            paymentForm.setGmtCreateUser(quickPaymentFormDto.getGmtCreateUser());
            paymentFormMapper.insert(paymentForm);
            String payFormNum = GetStringUtils.getPayFormNum(paymentForm.getId());
            paymentForm.setNumSystem(payFormNum);
            paymentFormMapper.updateById(paymentForm);

            PaymentFormItem item = new PaymentFormItem();
            item.setFkPaymentFormId(paymentForm.getId());
            item.setFkPayablePlanId(payablePlanId);
            item.setAmountPayment(paymentAmount);
            item.setServiceFee(paymentFee);
            item.setAmountExchangeRate(BigDecimal.ZERO);
            item.setExchangeRatePayable(exchangeRateService.getRateByCurrency(plan.getFkCurrencyTypeNum(),paymentForm.getFkCurrencyTypeNum()));
            item.setAmountPayable(paymentAmount.add(paymentFee).multiply(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(),plan.getFkCurrencyTypeNum())));
            item.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
            item.setAmountHkd(paymentAmount.add(paymentFee).multiply(item.getExchangeRateHkd()));
            item.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
            item.setAmountRmb(paymentAmount.add(paymentFee).multiply(item.getExchangeRateRmb()));
            item.setGmtCreate(quickPaymentFormDto.getGmtCreate());
            item.setGmtCreateUser(quickPaymentFormDto.getGmtCreateUser());
            paymentFormItemMapper.insert(item);
        }


    }

    /**
     * feign 根据应付计划id获取已付折合金额  + 已生成佣金金额
     *
     * @param payablePlanId
     * @Date 15:47 2022/6/9
     * <AUTHOR>
     */
    @Override
    public BigDecimal getAmountPaidByPayablePlanId(Long payablePlanId) {
        return paymentFormItemMapper.getAmountPaidByPayablePlanId(payablePlanId);
    }

    /**
     *
     * @param balancingPaymentFormDtos
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo batchBalancingPaymentForm(List<BalancingPaymentFormDto> balancingPaymentFormDtos) {
        //当为0时，不需要做找平处理
        balancingPaymentFormDtos = balancingPaymentFormDtos.stream().filter(b -> !BigDecimal.ZERO.equals(b.getDiffPayableAmount())).collect(Collectors.toList());

        List<QuickPaymentFormDto> quickPaymentFormDtos = BeanCopyUtils.copyListProperties(balancingPaymentFormDtos, QuickPaymentFormDto::new, (balancingPaymentFormDto, quickPaymentFormDto) -> {
            quickPaymentFormDto.setPayablePlanId(balancingPaymentFormDto.getId());
            quickPaymentFormDto.setPaymentFee(balancingPaymentFormDto.getDiffPayableAmount());
            quickPaymentFormDto.setPaymentSerialNumber("NA");
            quickPaymentFormDto.setSummary("【批量找平】手续费找平，找平币种和应付计划币种一致");
        });

        //快速补单找平
        quickCreatePaymentForms(quickPaymentFormDtos);

        return ResponseBo.ok();
    }

    /**
     * 根据keys和学生id集合获取付款单子项
     *
     * @param keys       m_student_offer_item（留学申请计划）
     *                   m_student_insurance（留学保险）
     *                   m_student_accommodation（留学住宿）
     *                   m_student_service_fee（留学服务费）
     *                   必须包含其中一个，否则SQL会报错
     * @param studentIds 学生id集合
     * @return
     */
    @Override
    public Map<Long, Integer> getPaymentFormItemList(List<String> keys, Set<Long> studentIds) {
        if (GeneralTool.isEmpty(studentIds)) {
            return Collections.emptyMap();
        }
        PayablePlanTypeKeyDto payablePlanTypeKeyDto = new PayablePlanTypeKeyDto();
        for (String key : keys) {
            switch (key) {
                case "m_student_offer_item":
                    payablePlanTypeKeyDto.setMStudentOfferItem(true);
                    break;
                case "m_student_insurance":
                    payablePlanTypeKeyDto.setMStudentInsurance(true);
                    break;
                case "m_student_accommodation":
                    payablePlanTypeKeyDto.setMStudentAccommodation(true);
                    break;
                case "m_student_service_fee":
                    payablePlanTypeKeyDto.setMStudentServiceFee(true);
                    break;
                default:
                    break;
            }
        }
        List<PaymentFormItemCountVo> countList = paymentFormItemMapper.getPaymentFormItemList(payablePlanTypeKeyDto, studentIds);
        return countList.stream().collect(Collectors.groupingBy(PaymentFormItemCountVo::getFkStudentId, Collectors.summingInt(PaymentFormItemCountVo::getNum)));
    }

    /**
     * @return java.math.BigDecimal
     * @Description: 获取汇率
     * @Param [payPlanCurrency, toCurrency]
     * <AUTHOR>
     */
    private BigDecimal getRate(String payPlanCurrency, String toCurrency) {
        if (GeneralTool.isEmpty(payPlanCurrency) || GeneralTool.isEmpty(toCurrency)) {
            return new BigDecimal(0);
        }
        if (payPlanCurrency.equals(toCurrency)) {
            return new BigDecimal(1);
        }
        return exchangeRateService.getLastExchangeRate(false, payPlanCurrency, toCurrency).getExchangeRate();
    }

    private void setName(PaymentFormItemVo paymentFormItemVo, Map<Long, String> companyMap) {
        if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkPayablePlanId())) {
            //111111
            Result<PayablePlanVo> resultPlanDetail = saleCenterClient.getPayablePlanDetail(paymentFormItemVo.getFkPayablePlanId());
            if (!resultPlanDetail.isSuccess()) {
                throw new GetServiceException(resultPlanDetail.getMessage());
            }
            PayablePlanVo payablePlanVo = resultPlanDetail.getData();
            if (GeneralTool.isNotEmpty(payablePlanVo)) {
                paymentFormItemVo.setFkTypeName(payablePlanVo.getFkTypeName());
                paymentFormItemVo.setTargetNames(payablePlanVo.getTargetNames());

                paymentFormItemVo.setPayablePlanAmount(payablePlanVo.getPayableAmount());
                paymentFormItemVo.setPayablePlanCurrency(payablePlanVo.getFkCurrencyTypeNum());
            }
        }
        //公司
        Long companyId = paymentFormItemMapper.getCompanyIdByItemId(paymentFormItemVo.getId());
        if (GeneralTool.isNotEmpty(companyId)) {
            paymentFormItemVo.setCompanyName(companyMap.get(companyId));
        }
        //实付币种
        String payFormCurrency = paymentFormService.getCurrencyByFormId(paymentFormItemVo.getFkPaymentFormId());
        paymentFormItemVo.setPayFormCurrency(currencyTypeService.getCurrencyNameByNum(payFormCurrency));
        //已付
        Long planId = paymentFormItemVo.getFkPayablePlanId();
        List<AlreadyPayVo> alreadyPay = paymentFormItemMapper.getAlreadyPayByPlanId(planId);
        paymentFormItemVo.setAlreadyPayDtos(alreadyPay);
        paymentFormItemVo.setPayablePlanCurrencyName(currencyTypeService.getCurrencyNameByNum(paymentFormItemVo.getPayablePlanCurrency()));
    }

    private void setDatasName(List<PaymentFormItemVo> paymentFormItemVos, Map<Long, PayablePlanVo> planDtoHashMap, Map<Long, Object> companyItemMap,
                              Map<Long, Object> currencyMap,
                              Map<Long, String> companyMap, Map<Long, PayablePlanVo> offerItemMap,
                              Map<String, String> allCurrencyTypeNames,
                              Map<Long, List<AlreadyPayVo>> alreadyPayMap) {
        Long fkAgentId;
        String nameNote;
        Map<Long, String> institutionProviderSelectNamesByIds = new HashMap<>();
        Set<Long> institutionProviderIds = planDtoHashMap.values().stream().filter(f -> TableEnum.INSTITUTION_PROVIDER.key.equals(f.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(institutionProviderIds)) {
            institutionProviderSelectNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds).getData();
        }
        Map<Long, String> businessProviderSelectNamesByIds = new HashMap<>();
        Set<Long> businessProviderIds = planDtoHashMap.values().stream().filter(f -> TableEnum.SALE_BUSINESS_PROVIDER.key.equals(f.getFkTypeKey())).map(PayablePlanVo::getFkTypeTargetId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(businessProviderIds)) {
            businessProviderSelectNamesByIds = saleCenterClient.getBusinessProviderNameByIds(businessProviderIds).getData();
        }
        for (PaymentFormItemVo paymentFormItemVo : paymentFormItemVos) {
            if (GeneralTool.isNotEmpty(paymentFormItemVo.getFkPayablePlanId())) {
                PayablePlanVo payablePlanVo = planDtoHashMap.get(paymentFormItemVo.getFkPayablePlanId());
                if (GeneralTool.isNotEmpty(payablePlanVo)) {
                    paymentFormItemVo.setFkTypeName(payablePlanVo.getFkTypeName());
                    paymentFormItemVo.setTargetNames(payablePlanVo.getTargetNames());

                    paymentFormItemVo.setPayablePlanAmount(payablePlanVo.getPayableAmount());
                    paymentFormItemVo.setPayablePlanCurrency(payablePlanVo.getFkCurrencyTypeNum());
//                    PaymentForm paymentForm = paymentForms.get(paymentFormItemDto.getFkPaymentFormId());
                    String fkTypeKey = payablePlanVo.getFkTypeKey();

                    if (TableEnum.INSTITUTION_PROVIDER.key.equals(fkTypeKey)){
                        paymentFormItemVo.setBusinessInformation(institutionProviderSelectNamesByIds.get(payablePlanVo.getFkTypeTargetId()));
                    }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(fkTypeKey)){
                        paymentFormItemVo.setBusinessInformation(businessProviderSelectNamesByIds.get(payablePlanVo.getFkTypeTargetId()));
                    }else {
                        //学生信息
                        paymentFormItemVo.setFkAgentName(payablePlanVo.getFkAgentName());
                        PayablePlanVo offerItem = offerItemMap.get(payablePlanVo.getId());
                        paymentFormItemVo.setIsDeferEntrance(offerItem.getIsDeferEntrance());
                        paymentFormItemVo.setFkAreaCountryName(offerItem.getFkAreaCountryName());
                        //学生信息
                        paymentFormItemVo.setStudentInformation(offerItem.getStudentInformation());
                        paymentFormItemVo.setBusinessInformation(offerItem.getBusinessInformation());
                        //渠道信息
                        paymentFormItemVo.setChannelInformation(offerItem.getChannelInformation());
                    }

                }
            }
            //公司
            Object val = companyItemMap.get(paymentFormItemVo.getId());
            if (val != null) {
                paymentFormItemVo.setCompanyName(companyMap.get(Long.valueOf(String.valueOf(val))));
            }
            //实付币种
            if (currencyMap.containsKey(paymentFormItemVo.getFkPaymentFormId())) {
                String payFormCurrency = (String) currencyMap.get(paymentFormItemVo.getFkPaymentFormId());
                paymentFormItemVo.setPayFormCurrency(payFormCurrency);
            }
            //已付
            Long planId = paymentFormItemVo.getFkPayablePlanId();
            List<AlreadyPayVo> alreadyPay = null;
//            List<AlreadyPayDto> alreadyPay = paymentFormItemMapper.getAlreadyPayByPlanId(planId);
            if (GeneralTool.isNotEmpty(alreadyPayMap)&&GeneralTool.isNotEmpty(alreadyPayMap.get(planId))){
                alreadyPay = alreadyPayMap.get(planId);
            }
            paymentFormItemVo.setAlreadyPayDtos(alreadyPay);
            paymentFormItemVo.setPayablePlanCurrencyName(allCurrencyTypeNames.get(paymentFormItemVo.getPayablePlanCurrency()) + "（" + paymentFormItemVo.getPayablePlanCurrency() + "）");
        }
    }

    private List<Long> getFormIds(Long fkCompanyId) {
        List<Long> formIds = paymentFormService.getFormByCompanyId(fkCompanyId);
        if (GeneralTool.isEmpty(formIds)) {
            formIds = new ArrayList<>();
            formIds.add(0L);
        }
        return formIds;
    }


    private Map<Long, String> getCompanyMap() {
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            List<CompanyTreeVo> companyTreeVos = result.getData();
            List<CompanyTreeVo> companyTreeDtoList = BeanCopyUtils.copyListProperties(companyTreeVos, CompanyTreeVo::new);
            if (GeneralTool.isNotEmpty(companyTreeDtoList)) {
                for (CompanyTreeVo companyTreeVo : companyTreeDtoList) {
                    if (companyTreeVo.getId() != null) {
                        companyMap.put(companyTreeVo.getId(), companyTreeVo.getShortName());
                    }
                }
            }
        }
        return companyMap;
    }


}
