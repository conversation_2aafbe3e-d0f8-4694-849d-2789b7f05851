package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ExpenseClaimFormItemMapper extends BaseMapper<ExpenseClaimFormItem> {

    /**
     * @return List<ExpenseClaimFormItem>
     * @Description :获取对应表单id的子项
     * @Param [id]
     * <AUTHOR>
     */
    List<ExpenseClaimFormItem> getFormItemByExpenseClaimFormId(@Param("id") Long id);

    /**
     * @return BigDecimal
     * @Description :获取对应表单id的子项总金额
     * @Param [id]
     * <AUTHOR>
     */
    BigDecimal getExpenseClaimFormTotalAmount(@Param("id") Long id);
}