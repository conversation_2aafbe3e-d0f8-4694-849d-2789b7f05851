package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.ExchangeRateVo;
import com.get.financecenter.entity.ExchangeRate;
import com.get.financecenter.dto.ExchangeRateDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/21 16:50
 * @verison: 1.0
 * @description:
 */
public interface IExchangeRateService extends BaseService<ExchangeRate> {
    /**
     * @return com.get.salecenter.vo.ExchangeRateDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ExchangeRateVo findExchangeRateById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [exchangeRateVos]
     * <AUTHOR>
     */
    void batchAdd(List<ExchangeRateDto> exchangeRateDtos);

    /**
     * @return void
     * @Description :汇率自动批量新增
     * @Param [exchangeRateVos]
     * <AUTHOR>
     */
    Boolean batchAddAuTo(List<ExchangeRateDto> exchangeRateDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ExchangeRateDto
     * @Description :修改
     * @Param [exchangeRateVo]
     * <AUTHOR>
     */
    ExchangeRateVo updateExchangeRate(ExchangeRateDto exchangeRateDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ExchangeRateDto>
     * @Description :列表
     * @Param [exchangeRateVo, page]
     * <AUTHOR>
     */
    List<ExchangeRateVo> getExchangeRates(ExchangeRateDto exchangeRateDto, Page page);

    /**
     * @return java.math.BigDecimal
     * @Description: 获取指定货币汇率
     * @Param [fromCurrency, toCurrency]
     * <AUTHOR>
     */
    BigDecimal getRateByCurrency(String fromCurrency, String toCurrency);

    /**
     * 保存汇率
     *
     * @param exchangeRateDto
     * @return
     */
    Long addExchangeRate(ExchangeRateDto exchangeRateDto);


    /**
     * 获取最新汇率
     * last为true时，读取当天最新汇率
     *
     * @param last
     * @param fromCurrency
     * @param toCurrency
     * @return
     */
    ExchangeRateVo getLastExchangeRate(Boolean last, String fromCurrency, String toCurrency);

    /**
     * 获取所有币种对港币的汇率
     */
    Map<String,BigDecimal> getLastExchangeRateHkd(Set<String> toCurrencys);


    /**
     * 获取集合对目标币种的汇率
     * @param toCurrencys
     * @param currencyNum
     * @return
     */
    Map<String,BigDecimal> getLastExchangeRate(Set<String> toCurrencys, String currencyNum);
}
