package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.vo.BankAccountVo;
import com.get.financecenter.service.IBankAccountService;
import com.get.financecenter.dto.BankAccountDto;
import com.get.financecenter.dto.query.BankAccountQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/21 9:57
 * @verison: 1.0
 * @description:
 */
@Api(tags = "银行账号管理")
@RestController
@RequestMapping("finance/bankAccount")
public class BankAccountController {
    @Resource
    private IBankAccountService bankAccountService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.BankAccountDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/银行账号管理/银行账号详情")
    @GetMapping("/{id}")
    public ResponseBo<BankAccountVo> detail(@PathVariable("id") Long id) {
        BankAccountVo data = bankAccountService.findBankAccountById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [bankAccountVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/银行账号管理/新增银行账号")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody   @Validated(BankAccountDto.Add.class) ValidList<BankAccountDto> bankAccountDtos) {
        bankAccountService.batchAdd(bankAccountDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/银行账号管理/删除银行账号")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        bankAccountService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.BankAccountDto>
     * @Description :修改信息
     * @Param [bankAccountVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/银行账号管理/更新银行账号")
    @PostMapping("update")
    public ResponseBo<BankAccountVo> update(@RequestBody @Validated(BankAccountDto.Update.class)  BankAccountDto bankAccountDto) {
        return UpdateResponseBo.ok(bankAccountService.updateBankAccount(bankAccountDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.BankAccountDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord 银行名称/账号名称/银行账号 搜索关键字")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/银行账号管理/查询银行账号")
    @PostMapping("datas")
    public ResponseBo<BankAccountVo> datas(@RequestBody SearchBean<BankAccountQueryDto> page) {
        List<BankAccountVo> datas = bankAccountService.getBankAccounts(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [bankAccountVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/银行账号管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<BankAccountDto> bankAccountDtos) {
        bankAccountService.movingOrder(bankAccountDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :是否激活
     * @Param [bankAccountId, isActive]
     * <AUTHOR>
     */
    @ApiOperation(value = "是否激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/银行账号管理/是否激活")
    @PostMapping("isActive")
    public ResponseBo isActive(@RequestParam Long bankAccountId, @RequestParam Boolean isActive) {
        bankAccountService.isActive(bankAccountId, isActive);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 银行账户下拉框
     * @Param [bankAccountId, isActive]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "银行账户下拉框", notes = "")
    @PostMapping("getBankAccountSelect")
    public ResponseBo<BankAccountVo> getBankAccountSelect(@RequestParam(name = "fkCompanyId", required = false) Long fkCompanyId) {
        return new ListResponseBo<>(bankAccountService.getBankAccountSelect(fkCompanyId));
    }
}
