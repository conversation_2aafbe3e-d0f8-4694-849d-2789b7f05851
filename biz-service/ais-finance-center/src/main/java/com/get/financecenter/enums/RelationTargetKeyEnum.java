package com.get.financecenter.enums;

import com.get.financecenter.vo.PaymentFeeTypeVo;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * 关联项类型枚举
 */
@Getter
public enum RelationTargetKeyEnum {

    STAFF("m_staff", "staff（员工）"),

    PROVIDER("m_provider", "provider（提供商）"),

    STUDENT("m_student","student（学生）"),

    STUDENT_SERVICE_FEE("m_student_service_fee", "studentServiceFee（学生服务费）");

    /**
     * 关联项关联类型Key（目标类型表名）
     */
    public String relationTargetKey;
    /**
     * 描述
     */
    public String name;

    RelationTargetKeyEnum(String relationTargetKey, String name) {
        this.relationTargetKey = relationTargetKey;
        this.name = name;
    }

    public static String getNameByRelationTargetKey(String relationTargetKey) {
        for (RelationTargetKeyEnum value : RelationTargetKeyEnum.values()) {
            if (value.getRelationTargetKey().equals(relationTargetKey)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<PaymentFeeTypeVo> getOptions() {
        return Arrays.stream(values())
                .map(type ->{
                    PaymentFeeTypeVo selectEntity = new PaymentFeeTypeVo();
                    selectEntity.setRelationTargetKey(type.relationTargetKey);
                    selectEntity.setRelationTargetKeyName(type.name);
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }
}
