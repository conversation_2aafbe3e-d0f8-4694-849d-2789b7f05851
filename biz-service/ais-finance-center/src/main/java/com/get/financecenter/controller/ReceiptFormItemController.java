package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.ReceiptFormItemDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.dto.ReceiptReDto;
import com.get.financecenter.dto.query.ReceiptFormQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/31
 * @TIME: 14:28
 * @Description:
 **/
@Api(tags = "收款单子项管理")
@RestController
@RequestMapping("finance/receiptFormItem")
public class ReceiptFormItemController {

    @Resource
    private IReceiptFormItemService receiptFormItemService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ReceiptFormItemDto>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款单子项管理/查询收款单")
    @PostMapping("datas")
    public ResponseBo<ReceiptFormItemVo> datas(@RequestBody SearchBean<ReceiptFormItemDto> page) {
        List<ReceiptFormItemVo> datas = receiptFormItemService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 导出收款单子项列表
     *
     * @param response
     */
    @ApiOperation(value = "导出收款单子项列表", notes = "")
    @PostMapping("/exportReceiptFormItemExcel")
    @ResponseBody
    public void exportReceiptFormItemExcel(HttpServletResponse response, @RequestBody ReceiptFormQueryDto receiptFormVo) {
        CommonUtil.ok(response);
        receiptFormItemService.exportReceiptFormItemExcel(response, receiptFormVo);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ReceiptFormItemDto>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款单子项管理/收款单详情")
    @GetMapping("/{id}")
    public ResponseBo<ReceiptFormItemVo> detail(@PathVariable("id") Long id) {
        ReceiptFormItemVo data = receiptFormItemService.findReceiptFormItemById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量新增接口
     * @Param [receiptFormItemVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款单子项管理/新增收款单子单")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ReceiptFormItemDto.Add.class)  ValidList<ReceiptFormItemDto> receiptFormItemDtos) {
        receiptFormItemService.addReceiptFormItem(receiptFormItemDtos);
        return SaveResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 修改信息
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单子项管理/更新")
    @PostMapping("update")
    public ResponseBo<ReceiptFormItemVo> update(@RequestBody @Validated(ReceiptFormItemDto.Update.class) ReceiptFormItemDto receiptFormItemDto) {
        return UpdateResponseBo.ok(receiptFormItemService.updateReceiptFormItem(receiptFormItemDto));
    }

    /**
     * 激活佣金结算
     *
     * @Date 17:16 2022/5/5
     * <AUTHOR>
     */
    @ApiOperation(value = "激活佣金结算", notes = "receiptFormItemId:收款单子单id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单子项管理/激活佣金结算")
    @PostMapping("activateCommissionSettlement")
    public ResponseBo<ReceiptFormItemVo> activateCommissionSettlement(@RequestBody Set<Long> receiptFormItemIds) {
        receiptFormItemService.activateCommissionSettlement(receiptFormItemIds);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/收款单子项管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        receiptFormItemService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件列表", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款单子项管理/查询附件")
    @PostMapping("getMedia")
    public ResponseBo<FMediaAndAttachedVo> getMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = receiptFormItemService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 附件保存
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "附件保存")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/收款单子项管理/附件保存接口")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addMedia(@RequestBody  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(receiptFormItemService.addMedia(mediaAttachedVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.PaymentFormItemDto>
     * @Description: 获取付款单列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取收款单列表")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款单子项管理/获取付款单列表")
    @PostMapping("getReceiptFormList")
    public ResponseBo<ReceiptFormVo> getPayFormList(@RequestParam("planId") Long planId) {
        List<ReceiptFormVo> datas = receiptFormItemService.getReceiptFormList(planId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据应收计划id获取收款单子项
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getReceiptFormListFeign")
    public List<ReceiptFormVo> getReceiptFormListFeign(@RequestParam("planId") Long planId) {
        List<ReceiptFormVo> receiptFormList = receiptFormItemService.getReceiptFormList(planId);
        return receiptFormList.stream().map(receiptForm -> BeanCopyUtils.objClone(receiptForm, ReceiptFormVo::new)).collect(Collectors.toList());
    }

    /**
     * feign根据应收计划ids获取所绑定的收款单子项
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getReceiptFormListFeignByPlanIds")
    public List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(@RequestParam("planIds") Set<Long> planIds) {
        return receiptFormItemService.getReceiptFormListFeignByPlanIds(planIds);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @ApiOperation(value = "收款-学生申请方案项目下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/收款单子项管理/收款-学生申请方案项目下拉")
    @PostMapping("getStudentOfferItemSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getStudentOfferItemSelect(@RequestParam(value = "tableName") String tableName,
                                                                  @RequestParam(value = "fkTypeKey") String fkTypeKey,
                                                                  @RequestParam(value = "fkTypeTargetId") Long fkTypeTargetId) {
        return new ListResponseBo<>(receiptFormItemService.getStudentOfferItemSelect(tableName, fkTypeKey, fkTypeTargetId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ReceiptFormItemDto>
     * @Description: 获取应收计划数据
     * @Param [typeKey, targetId, formId]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取应收计划数据", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款单子项管理/详情")
    @PostMapping("getReceiptFormItem")
    public ResponseBo<ReceiptFormItemVo> getPayableFormItem(@RequestParam("planId") Long planId,
                                                            @RequestParam("formId") Long formId) {
        ReceiptFormItemVo data = receiptFormItemService.getReceiptFormItem(planId, formId);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ReceiptFormItemDto>
     * @Description: 获取应收计划数据
     * @Param [typeKey, targetId, formId]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取应收计划列表数据", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/收款单子项管理/列表数据")
    @PostMapping("getReceiptFormItemList")
    public ResponseBo<ReceiptFormItemVo> getReceiptFormItemList(@RequestBody ReceiptReDto receiptReDto) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<ReceiptFormItemVo> datas = receiptFormItemService.getReceiptFormItemList(receiptReDto, times, 0, 0);
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @ApiOperation(value = "获取应收计划列表数据分页信息", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/收款单子项管理/列表数据分页")
    @PostMapping("getReceiptFormItemListPagination")
    public ResponseBo getReceiptFormItemListPagination(@RequestBody SearchBean<ReceiptReDto> page) {
        //[0]-o-主SQL执行时间,[1]-f-远程调用时间
        String[] times = {"0", "0"};
        return receiptFormItemService.getReceiptFormItemListPagination(page.getData().getFormId(), times, page);
    }

}
