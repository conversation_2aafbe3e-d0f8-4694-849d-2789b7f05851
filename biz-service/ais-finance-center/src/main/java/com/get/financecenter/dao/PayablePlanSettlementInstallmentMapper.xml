<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper">
  <update id="updateFee">
    UPDATE ais_finance_center.r_payable_plan_settlement_installment SET service_fee_actual_init = service_fee_actual_init + 5,service_fee_actual = service_fee_actual + 5,gmt_modified_user='admin-tp'
    WHERE id IN
    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>
  <update id="saveIsExchangeInput">

    UPDATE r_payable_plan_settlement_installment SET is_exchange_input=#{complete},exchange_input_time=now()
    WHERE num_settlement_batch IN
    <foreach collection="numSettlementBatchs" item="item" open="(" close=")" separator=",">
      #{item.numSettlementBatch}
    </foreach>

  </update>
  <select id="cancelFinancialBatchSettlement" resultType="com.get.financecenter.dto.CancelSettlementDto">
    SELECT
      fk_payable_plan_id AS payablePlanId,
      fk_agent_contract_account_id
    FROM
     ais_finance_center.r_payable_plan_settlement_installment
    where  num_settlement_batch = #{numSettlementBatch}
    GROUP BY
      fk_payable_plan_id,
      fk_agent_contract_account_id
  </select>

  <select id="getInsertSettlementAmountActual" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual
    FROM ais_sale_center.m_payable_plan AS mpp
    -- 计算应付计划分期表为未处理状态的实付金额
    LEFT JOIN (
    SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
    -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
    LEFT JOIN (
    SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
    -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
    LEFT JOIN (
    SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
    SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
    AND rppsi1.num_settlement_batch is not null
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
    GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementPrepaidAmount" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0))
        + SUM(IFNULL(a1.service_fee_actual,0)) + SUM(IFNULL(a2.service_fee_actual,0)) + SUM(IFNULL(a3.service_fee_actual,0)) AS amount_actual
    FROM ais_sale_center.m_payable_plan AS mpp
           -- 计算应付计划分期表为未处理状态的实付金额
           LEFT JOIN (
      SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS service_fee_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
      -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
      LEFT JOIN (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS service_fee_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
      -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
      LEFT JOIN (
      SELECT b.id,SUM(b.amount_actual) AS amount_actual, SUM(b.service_fee_actual) AS service_fee_actual FROM (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS service_fee_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
      AND rppsi1.num_settlement_batch is not null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
      GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementNotPrepaidAmount" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual
    FROM ais_sale_center.m_payable_plan AS mpp
           -- 计算应付计划分期表为未处理状态的实付金额
           LEFT JOIN (
      SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
      -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
      LEFT JOIN (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
      -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
           LEFT JOIN (
      SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
      AND rppsi1.num_settlement_batch is not null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
      GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementPrepaidHedgingAmount" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(amount_expect), 0) + IFNULL(SUM(service_fee_expect), 0) AS amount_expect FROM ais_finance_center.r_payable_plan_settlement_installment where fk_payable_plan_id = #{payablePlanId} AND status = 0 AND fk_receipt_form_item_id is not null
  </select>
  <select id="getInsertSettlementPrepaidHedgingServiceFeeAmount" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(service_fee_expect), 0) + IFNULL(SUM(service_fee_expect), 0) AS amount_expect FROM ais_finance_center.r_payable_plan_settlement_installment where fk_payable_plan_id = #{payablePlanId} AND status = 0 AND fk_receipt_form_item_id is not null
  </select>
    <select id="getList" resultType="com.get.financecenter.entity.PayablePlanSettlementInstallment">
      SELECT * from ais_finance_center.r_payable_plan_settlement_installment where gmt_modified_user = 'admin-tp'
    </select>
    <select id="getPayPlanList" resultType="com.get.financecenter.entity.PayablePlanSettlementInstallment">
        SELECT
         rppsi.*
        FROM
         `ais_sale_center`.`m_payable_plan`  AS mpp
         INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        WHERE
         `is_pay_in_advance` = '1' AND rppsi.fk_receipt_form_item_id is null
    </select>

    <select id="getAmountPaidByPayablePlanId" resultType="java.math.BigDecimal">
      SELECT
        IFNULL(SUM(a.amountPaid), 0)
      FROM
        (
          SELECT
            SUM( amount_actual ) + SUM( service_fee_actual ) AS amountPaid
          FROM
            ais_finance_center.r_payable_plan_settlement_installment
          WHERE
            fk_payable_plan_id = #{payablePlanId}
            AND STATUS = 0
          GROUP BY
            fk_payable_plan_id, status_settlement

          UNION ALL

          SELECT
            MAX( amount_actual ) + MAX( service_fee_actual ) AS amountPaid
          FROM
            ais_finance_center.r_payable_plan_settlement_installment
          WHERE
            fk_payable_plan_id = #{payablePlanId}
            AND STATUS = 1
          GROUP BY
            fk_payable_plan_id, status_settlement
        )a
    </select>
  <select id="getCancelSettlement" resultType="com.get.financecenter.entity.PayablePlanSettlementInstallment">
    SELECT
      *
    FROM
     ais_finance_center.r_payable_plan_settlement_installment
    WHERE
    <foreach collection="cancelSettlementVoList" item="cancelSettlementDto" open="(" close=")" separator=" OR ">
      ( fk_payable_plan_id = #{cancelSettlementDto.payablePlanId} AND fk_agent_contract_account_id = #{cancelSettlementDto.fkAgentContractAccountId} )
    </foreach>
    AND status_settlement  = 1
  </select>
    <select id="getFirstStepCommission" resultType="java.math.BigDecimal">
      SELECT
        COALESCE(SUM(IFNULL(amount_actual, 0)), 0) + COALESCE(SUM(IFNULL(service_fee_actual, 0)), 0) AS total_sum
      FROM
          (SELECT 0 AS dummy) AS dummy_table
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_installment ON fk_payable_plan_id = #{payablePlanId} AND status=0 AND status_settlement=0
      GROUP BY
        fk_payable_plan_id
    </select>

  <select id="getPayablePlanByNumSettlementBatch" resultType="com.get.salecenter.vo.CommissionSummaryBatchPayablePlanVo">
    SELECT mpp.*, rppsi.fk_agent_contract_account_id, rppsi.fk_currency_type_num AS agentCurrencyTypeNum
    FROM ais_sale_center.m_payable_plan AS mpp
           INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.num_settlement_batch = #{numSettlementBatch}
    GROUP BY
      mpp.id
  </select>

  <select id="getPayablePlanInfoAndAgentIdByNumSettlementBatch" resultType="com.get.salecenter.vo.PayablePlanVo">
    SELECT
      *
    FROM
      (
        SELECT
          mpp.fk_type_key,
          mpp.fk_currency_type_num,
          a.id AS fkAgentId,
          a.NAME AS agentName
        FROM
          ais_sale_center.m_payable_plan AS mpp
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
            AND mpp.fk_type_key = 'm_student_offer_item'
            INNER JOIN (
            SELECT
              rppsi1.fk_payable_plan_id,
              MAX( rppsi1.fk_agent_id_settlement ) fk_agent_id_settlement
            FROM
              ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE
              rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY
              rppsi1.fk_payable_plan_id
          ) rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement

        UNION ALL

        SELECT
          mpp.fk_type_key,
          mpp.fk_currency_type_num,
          a.id AS fkAgentId,
          a.NAME AS agentName
        FROM
          ais_sale_center.m_payable_plan AS mpp
            INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
            AND mpp.fk_type_key = 'm_student_insurance'
            INNER JOIN (
            SELECT
              rppsi1.fk_payable_plan_id,
              MAX( rppsi1.fk_agent_id_settlement ) fk_agent_id_settlement
            FROM
              ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE
              rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY
              rppsi1.fk_payable_plan_id
          ) rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement

        UNION ALL

        SELECT
          mpp.fk_type_key,
          mpp.fk_currency_type_num,
          a.id AS fkAgentId,
          a.NAME AS agentName
        FROM
          ais_sale_center.m_payable_plan AS mpp
            INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
            AND mpp.fk_type_key = 'm_student_service_fee'
            INNER JOIN (
            SELECT
              rppsi1.fk_payable_plan_id,
              MAX( rppsi1.fk_agent_id_settlement ) fk_agent_id_settlement
            FROM
              ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE
              rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY
              rppsi1.fk_payable_plan_id
          ) rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement

        UNION ALL

        SELECT
          mpp.fk_type_key,
          mpp.fk_currency_type_num,
          a.id AS fkAgentId,
          a.NAME AS agentName
        FROM
          ais_sale_center.m_payable_plan AS mpp
            INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
            AND mpp.fk_type_key = 'm_student_accommodation'
            INNER JOIN (
            SELECT
              rppsi1.fk_payable_plan_id,
              MAX( rppsi1.fk_agent_id_settlement ) fk_agent_id_settlement
            FROM
              ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE
              rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY
              rppsi1.fk_payable_plan_id
          ) rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      ) aa
    GROUP BY
      aa.fk_currency_type_num,
      aa.fkAgentId,
      aa.fk_type_key
  </select>

  <select id="commissionSummaryBatchItemList" resultType="com.get.salecenter.vo.CommissionSummaryBatchItemVo">
    SELECT * FROM (
    SELECT
    a.id,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
    mpp.fk_currency_type_num AS planCurrencyNum,
    mpp.fk_type_key,
    IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
    IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
    aca.fk_currency_type_num AS accountCurrencyNum,
    SUM( rppsi.amount_actual ) AS payableAmount,
    GROUP_CONCAT( DISTINCT s.NAME ) AS studentName,
    aca.bank_account,
    aca.bank_account_num,
    aca.bank_name,
    aca.bank_branch_name,
    aca.bank_address,
    CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
    SUM(rppsi.amount_actual) AS amount_actual,
    SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
    GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
    FROM
    ais_sale_center.m_payable_plan AS mpp
    INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_offer_item'
    AND mpp.STATUS = 1
    <!-- AND EXISTS (SELECT 1 FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 WHERE rppsi1.fk_payable_plan_id = mpp.id AND rppsi1.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}) -->
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    WHERE rppsi1.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
    <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
      AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
    </if>
    <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
      AND s.fk_company_id in
      <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    INNER JOIN ais_permission_center.m_company AS com ON mpp.fk_company_id = com.id
    LEFT JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
    AND ras.is_active = 1
    LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
    --      显示已付金额、差额
    LEFT JOIN (
    SELECT
    mpp2.id,
    IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
    mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp2
    LEFT JOIN (
    SELECT
    mpp3.id,
    IFNULL( SUM( mpfi2.amount_payable ), 0 )  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp3
    LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
    AND mpp3.fk_type_key = 'm_student_offer_item'
    LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
    WHERE
    mpp3.STATUS = 1 AND mpf2.status = 1
    GROUP BY
    mpp3.id
    ) mpp4 ON mpp4.id = mpp2.id
    ) c ON c.id = mpp.id
    GROUP BY
    a.id,
    mpp.fk_currency_type_num,
    aca.id

    UNION ALL

    SELECT
    a.id,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
    mpp.fk_currency_type_num AS planCurrencyNum,
    mpp.fk_type_key,
    IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
    IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
    aca.fk_currency_type_num AS accountCurrencyNum,
    SUM( mpp.payable_amount ) AS payableAmount,
    CASE
    WHEN msi.type = 1
    THEN msi.insurant_name
    ELSE GROUP_CONCAT( DISTINCT s.NAME ) END AS studentName,
    aca.bank_account,
    aca.bank_account_num,
    aca.bank_name,
    aca.bank_branch_name,
    aca.bank_address,
    CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code
    WHEN aca.bank_code IS NULL OR aca.bank_code = '' THEN ''
    ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
    SUM(rppsi.amount_actual) AS amount_actual,
    SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
    GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
    FROM
    ais_sale_center.m_payable_plan AS mpp
    INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_insurance'
    AND mpp.STATUS = 1
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    WHERE rppsi1.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id
    <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
      AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
    </if>
    <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
      AND s.fk_company_id in
      <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    LEFT JOIN ais_permission_center.m_company AS com ON mpp.fk_company_id = com.id
    <!--AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}-->
    LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
    AND ras.is_active = 1
    LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
    --      显示已付金额、差额
    LEFT JOIN (
    SELECT
    mpp2.id,
    IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
    mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp2
    LEFT JOIN (
    SELECT
    mpp3.id,
    IFNULL( SUM( mpfi2.amount_payable ), 0 )  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp3
    LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
    AND mpp3.fk_type_key = 'm_student_insurance'
    LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
    WHERE
    mpp3.STATUS = 1 AND mpf2.status = 1
    GROUP BY
    mpp3.id
    ) mpp4 ON mpp4.id = mpp2.id
    ) c ON c.id = mpp.id
    <!--            <if test="studentName != null and studentName != ''">-->
    <!--                AND s.name LIKE CONCAT('%', #{studentName}, '%')-->
    <!--            </if>-->
    GROUP BY
    a.id,
    mpp.fk_currency_type_num,
    aca.id

    UNION ALL

    SELECT
    a.id,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
    mpp.fk_currency_type_num AS planCurrencyNum,
    mpp.fk_type_key,
    IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
    IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
    aca.fk_currency_type_num AS accountCurrencyNum,
    SUM( mpp.payable_amount ) AS payableAmount,
    GROUP_CONCAT( DISTINCT s.NAME ) AS studentName,
    aca.bank_account,
    aca.bank_account_num,
    aca.bank_name,
    aca.bank_branch_name,
    aca.bank_address,
    CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
    SUM(rppsi.amount_actual) AS amount_actual,
    SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
    GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
    FROM
    ais_sale_center.m_payable_plan AS mpp
    INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_accommodation'
    AND mpp.STATUS = 1
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    WHERE rppsi1.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    LEFT JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
    <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
      AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
    </if>
    <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
      AND s.fk_company_id in
      <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
    AND ras.is_active = 1
    LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
    LEFT JOIN ais_permission_center.m_company AS com ON mpp.fk_company_id = com.id
    --      显示已付金额、差额
    LEFT JOIN (
    SELECT
    mpp2.id,
    IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
    mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp2
    LEFT JOIN (
    SELECT
    mpp3.id,
    IFNULL( SUM( mpfi2.amount_payable ), 0 ) +  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp3
    LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
    AND mpp3.fk_type_key = 'm_student_accommodation'
    LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
    WHERE
    mpp3.STATUS = 1 AND mpf2.status = 1
    GROUP BY
    mpp3.id
    ) mpp4 ON mpp4.id = mpp2.id
    ) c ON c.id = mpp.id
    GROUP BY
    a.id,
    mpp.fk_currency_type_num,
    aca.id

    UNION ALL

    SELECT
    a.id,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
    mpp.fk_currency_type_num AS planCurrencyNum,
    mpp.fk_type_key,
    IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
    IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
    aca.fk_currency_type_num AS accountCurrencyNum,
    SUM( mpp.payable_amount ) AS payableAmount,
    GROUP_CONCAT( DISTINCT s.NAME ) AS studentName,
    aca.bank_account,
    aca.bank_account_num,
    aca.bank_name,
    aca.bank_branch_name,
    aca.bank_address,
    CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
    SUM(rppsi.amount_actual) AS amount_actual,
    SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
    GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
    FROM
    ais_sale_center.m_payable_plan AS mpp
    INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_service_fee'
    AND mpp.STATUS = 1
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    WHERE rppsi1.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    LEFT JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
    <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
      AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
    </if>
    <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
      AND s.fk_company_id in
      <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
        #{fkCompanyId}
      </foreach>
    </if>
    LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
    AND ras.is_active = 1
    LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
    LEFT JOIN ais_permission_center.m_company AS com ON mpp.fk_company_id = com.id
    --      显示已付金额、差额
    LEFT JOIN (
    SELECT
    mpp2.id,
    IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
    mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp2
    LEFT JOIN (
    SELECT
    mpp3.id,
    IFNULL( SUM( mpfi2.amount_payable ), 0 ) +  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
    FROM
    ais_sale_center.m_payable_plan AS mpp3
    LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
    AND mpp3.fk_type_key = 'm_student_service_fee'
    LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
    WHERE
    mpp3.STATUS = 1 AND mpf2.status = 1
    GROUP BY
    mpp3.id
    ) mpp4 ON mpp4.id = mpp2.id
    ) c ON c.id = mpp.id
    <!--            <if test="studentName != null and studentName != ''">-->
    <!--                AND s.name LIKE CONCAT('%', #{studentName}, '%')-->
    <!--            </if>-->
    GROUP BY
    a.id,
    mpp.fk_currency_type_num,
    aca.id
    )x
    ORDER BY
    x.agentName
  </select>

  <select id="getNumSettlementBatchByVo" resultType="java.lang.String">
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_offer_item'">
      SELECT
      rppsi.num_settlement_batch
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      AND mpp.fk_type_key = 'm_student_offer_item'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      WHERE
      rppsi.num_settlement_batch IS NOT NULL
      <if test="commissionSummaryBatchDto.isEexchangeInputFlag != null and commissionSummaryBatchDto.isEexchangeInputFlag ">
        AND  (rppsi.is_exchange_input=0 OR rppsi.is_exchange_input IS NULL)
      </if>

      <if test="commissionSummaryBatchDto.isEexchangeInputFlag != null and !commissionSummaryBatchDto.isEexchangeInputFlag ">
        AND  rppsi.is_exchange_input=1
      </if>


      <if test="commissionSummaryBatchDto.numSettlementBatch != null and commissionSummaryBatchDto.numSettlementBatch != ''">
        AND rppsi.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
      </if>
      <if test="commissionSummaryBatchDto.agentNameOrNum != null and commissionSummaryBatchDto.agentNameOrNum != ''">
        AND (a.num =#{commissionSummaryBatchDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{commissionSummaryBatchDto.agentNameOrNum}, '%') )
      </if>
      <if test="commissionSummaryBatchDto.studentName != null and commissionSummaryBatchDto.studentName != ''">
        AND
        (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{commissionSummaryBatchDto.studentName},'%')
        OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{commissionSummaryBatchDto.studentName},'%')
        OR s.`name` like concat('%',#{commissionSummaryBatchDto.studentName},'%')
        OR s.last_name like concat('%',#{commissionSummaryBatchDto.studentName},'%')
        OR s.first_name like concat('%',#{commissionSummaryBatchDto.studentName},'%'))
      </if>
      <if test="commissionSummaryBatchDto.fkCurrencyTypeNum != null and commissionSummaryBatchDto.fkCurrencyTypeNum != ''">
        AND mpf.fk_currency_type_num = #{commissionSummaryBatchDto.fkCurrencyTypeNum}
      </if>
      <if test="commissionSummaryBatchDto.numBank != null and commissionSummaryBatchDto.numBank != ''">
        AND mpf.num_bank = #{commissionSummaryBatchDto.numBank}
      </if>
      <if test="commissionSummaryBatchDto.agentAreaStateId != null and  commissionSummaryBatchDto.agentAreaStateId !=''">
        and a.fk_area_state_id =#{commissionSummaryBatchDto.agentAreaStateId}
      </if>
    </if>


    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_insurance'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_offer_item'">
        UNION
      </if>
      SELECT
      rppsi.num_settlement_batch
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
      AND mpp.fk_type_key = 'm_student_insurance'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      WHERE
      rppsi.num_settlement_batch IS NOT NULL
      <if test="commissionSummaryBatchDto.numSettlementBatch != null and commissionSummaryBatchDto.numSettlementBatch != ''">
        AND rppsi.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
      </if>
      <if test="commissionSummaryBatchDto.agentNameOrNum != null and commissionSummaryBatchDto.agentNameOrNum != ''">
        AND (a.num =#{commissionSummaryBatchDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{commissionSummaryBatchDto.agentNameOrNum}, '%') )
      </if>
      <if test="commissionSummaryBatchDto.studentName != null and commissionSummaryBatchDto.studentName != ''">
        AND (s.name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%')
        OR msi.insurant_name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%')
        OR msi.insurant_last_name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%')
        OR msi.insurant_first_name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%'))
      </if>
      <if test="commissionSummaryBatchDto.fkCurrencyTypeNum != null and commissionSummaryBatchDto.fkCurrencyTypeNum != ''">
        AND mpf.fk_currency_type_num = #{commissionSummaryBatchDto.fkCurrencyTypeNum}
      </if>
      <if test="commissionSummaryBatchDto.numBank != null and commissionSummaryBatchDto.numBank != ''">
        AND mpf.num_bank = #{commissionSummaryBatchDto.numBank}
      </if>
      <if test="commissionSummaryBatchDto.agentAreaStateId != null and  commissionSummaryBatchDto.agentAreaStateId !=''">
        and a.fk_area_state_id =#{commissionSummaryBatchDto.agentAreaStateId}
      </if>
    </if>
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_accommodation'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_insurance'">
        UNION
      </if>
      SELECT
      rppsi.num_settlement_batch
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
      AND mpp.fk_type_key = 'm_student_accommodation'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      WHERE
      rppsi.num_settlement_batch IS NOT NULL
      <if test="commissionSummaryBatchDto.numSettlementBatch != null and commissionSummaryBatchDto.numSettlementBatch != ''">
        AND rppsi.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
      </if>
      <if test="commissionSummaryBatchDto.agentNameOrNum != null and commissionSummaryBatchDto.agentNameOrNum != ''">
        AND (a.num =#{commissionSummaryBatchDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{commissionSummaryBatchDto.agentNameOrNum}, '%') )
      </if>
      <if test="commissionSummaryBatchDto.studentName != null and commissionSummaryBatchDto.studentName != ''">
        AND s.name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%')
      </if>
      <if test="commissionSummaryBatchDto.fkCurrencyTypeNum != null and commissionSummaryBatchDto.fkCurrencyTypeNum != ''">
        AND mpf.fk_currency_type_num = #{commissionSummaryBatchDto.fkCurrencyTypeNum}
      </if>
      <if test="commissionSummaryBatchDto.numBank != null and commissionSummaryBatchDto.numBank != ''">
        AND mpf.num_bank = #{commissionSummaryBatchDto.numBank}
      </if>
      <if test="commissionSummaryBatchDto.agentAreaStateId != null and  commissionSummaryBatchDto.agentAreaStateId !=''">
        and a.fk_area_state_id =#{commissionSummaryBatchDto.agentAreaStateId}
      </if>
    </if>
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_service_fee'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_accommodation'">
        UNION
      </if>
      SELECT
      rppsi.num_settlement_batch
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
      AND mpp.fk_type_key = 'm_student_service_fee'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      INNER JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      WHERE
      rppsi.num_settlement_batch IS NOT NULL
      <if test="commissionSummaryBatchDto.numSettlementBatch != null and commissionSummaryBatchDto.numSettlementBatch != ''">
        AND rppsi.num_settlement_batch = #{commissionSummaryBatchDto.numSettlementBatch}
      </if>
      <if test="commissionSummaryBatchDto.agentNameOrNum != null and commissionSummaryBatchDto.agentNameOrNum != ''">
        AND (a.num =#{commissionSummaryBatchDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{commissionSummaryBatchDto.agentNameOrNum}, '%') )
      </if>
      <if test="commissionSummaryBatchDto.studentName != null and commissionSummaryBatchDto.studentName != ''">
        AND s.name LIKE CONCAT('%', #{commissionSummaryBatchDto.studentName}, '%')
      </if>
      <if test="commissionSummaryBatchDto.fkCurrencyTypeNum != null and commissionSummaryBatchDto.fkCurrencyTypeNum != ''">
        AND mpf.fk_currency_type_num = #{commissionSummaryBatchDto.fkCurrencyTypeNum}
      </if>
      <if test="commissionSummaryBatchDto.numBank != null and commissionSummaryBatchDto.numBank != ''">
        AND mpf.num_bank = #{commissionSummaryBatchDto.numBank}
      </if>
      <if test="commissionSummaryBatchDto.agentAreaStateId != null and  commissionSummaryBatchDto.agentAreaStateId !=''">
        and a.fk_area_state_id =#{commissionSummaryBatchDto.agentAreaStateId}
      </if>
    </if>
  </select>

  <select id="selectisExchangeInputNo" resultType="java.lang.String">
    SELECT
    DISTINCT rppsi.num_settlement_batch
    FROM
    ais_finance_center.r_payable_plan_settlement_installment AS rppsi

    <where>
      <if test="numSettlementBatchList != null and numSettlementBatchList.size()>0 ">
        AND rppsi.num_settlement_batch IN
        <foreach collection="numSettlementBatchList" item="numSettlementBatch" index="index" open="("
                 separator="," close=")">
          #{numSettlementBatch}
        </foreach>
      </if>
      AND (rppsi.is_exchange_input=0 OR rppsi.is_exchange_input IS NULL)
    </where>

  </select>

  <select id="commissionSummaryBatchList" resultType="com.get.salecenter.vo.CommissionSummaryBatchVo">
    SELECT
    x.num_settlement_batch,
    SUM( x.agentNum ) AS agentNum,
    SUM( x.studentNum ) AS studentNum,
    SUM( x.payablePlanNum ) AS payablePlanNum,
    SUM( x.payableFormNum ) AS payableFormNum,
    GROUP_CONCAT(DISTINCT x.numBank ) AS numBank,
    GROUP_CONCAT(DISTINCT x.fkCompanyName ) AS fkCompanyName
    FROM
    (
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_offer_item'">
      SELECT
      rppsi.num_settlement_batch,
      COUNT( DISTINCT a.id ) AS agentNum,
      COUNT( DISTINCT s.id ) AS studentNum,
      COUNT( DISTINCT mpp.id ) AS payablePlanNum,
      COUNT( DISTINCT mpf.id ) AS payableFormNum,
      GROUP_CONCAT( DISTINCT mpf.num_bank ) AS numBank,
      GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      AND mpp.fk_type_key = 'm_student_offer_item'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      INNER JOIN ais_permission_center.m_company AS com ON s.fk_company_id = com.id
      <!--AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}-->
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = msoi.fk_agent_id
      AND a.is_active = 1
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      <where>
        <if test="numSettlementBatchList != null and numSettlementBatchList.size()>0 ">
          AND rppsi.num_settlement_batch IN
          <foreach collection="numSettlementBatchList" item="numSettlementBatch" index="index" open="("
                   separator="," close=")">
            #{numSettlementBatch}
          </foreach>
        </if>
      </where>
      GROUP BY
      rppsi.num_settlement_batch
    </if>


    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_insurance'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_offer_item'">
        UNION ALL
      </if>
      SELECT
      rppsi.num_settlement_batch,
      COUNT( DISTINCT a.id ) AS agentNum,
      COUNT( DISTINCT s.id ) AS studentNum,
      COUNT( DISTINCT mpp.id ) AS payablePlanNum,
      COUNT( DISTINCT mpf.id ) AS payableFormNum,
      GROUP_CONCAT( DISTINCT mpf.num_bank ) AS numBank,
      GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      AND mpp.fk_type_key = 'm_student_insurance'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      AND a.is_active = 1
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      INNER JOIN ais_permission_center.m_company AS com ON s.fk_company_id = com.id
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      <where>
        <if test="numSettlementBatchList != null and numSettlementBatchList.size()>0 ">
          AND rppsi.num_settlement_batch IN
          <foreach collection="numSettlementBatchList" item="numSettlementBatch" index="index" open="("
                   separator="," close=")">
            #{numSettlementBatch}
          </foreach>
        </if>
      </where>
      GROUP BY
      rppsi.num_settlement_batch

    </if>
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_accommodation'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_insurance'">
        UNION ALL
      </if>
      SELECT
      rppsi.num_settlement_batch,
      COUNT( DISTINCT a.id ) AS agentNum,
      COUNT( DISTINCT s.id ) AS studentNum,
      COUNT( DISTINCT mpp.id ) AS payablePlanNum,
      COUNT( DISTINCT mpf.id ) AS payableFormNum,
      GROUP_CONCAT( DISTINCT mpf.num_bank ) AS numBank,
      GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN
      ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      AND mpp.fk_type_key = 'm_student_accommodation'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      AND a.is_active = 1
      INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      INNER JOIN ais_permission_center.m_company AS com ON s.fk_company_id = com.id
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      <where>
        <if test="numSettlementBatchList != null and numSettlementBatchList.size()>0 ">
          AND rppsi.num_settlement_batch IN
          <foreach collection="numSettlementBatchList" item="numSettlementBatch" index="index" open="("
                   separator="," close=")">
            #{numSettlementBatch}
          </foreach>
        </if>
      </where>
      GROUP BY
      rppsi.num_settlement_batch
    </if>
    <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_service_fee'">
      <if test="commissionSummaryBatchDto.businessType == null or commissionSummaryBatchDto.businessType == '' or commissionSummaryBatchDto.businessType == 'm_student_accommodation'">
        UNION ALL
      </if>
      SELECT
      rppsi.num_settlement_batch,
      COUNT( DISTINCT a.id ) AS agentNum,
      COUNT( DISTINCT s.id ) AS studentNum,
      COUNT( DISTINCT mpp.id ) AS payablePlanNum,
      COUNT( DISTINCT mpf.id ) AS payableFormNum,
      GROUP_CONCAT( DISTINCT mpf.num_bank ) AS numBank,
      GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName
      FROM
      ais_finance_center.r_payable_plan_settlement_installment AS rppsi
      INNER JOIN
      ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
      AND mpp.fk_type_key = 'm_student_service_fee'
      AND mpp.STATUS = 1
      INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
      INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
      AND a.is_active = 1
      INNER JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
      <if test="commissionSummaryBatchDto.fkCompanyId != null and commissionSummaryBatchDto.fkCompanyId !=''">
        AND s.fk_company_id = #{commissionSummaryBatchDto.fkCompanyId}
      </if>
      <if test="commissionSummaryBatchDto.fkCompanyIds != null and commissionSummaryBatchDto.fkCompanyIds.size()>0">
        AND s.fk_company_id in
        <foreach collection="commissionSummaryBatchDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
          #{fkCompanyId}
        </foreach>
      </if>
      INNER JOIN ais_permission_center.m_company AS com ON s.fk_company_id = com.id
      LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.id = rppsi.fk_payment_form_item_id
      LEFT JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      <where>
        <if test="numSettlementBatchList != null and numSettlementBatchList.size()>0 ">
          AND rppsi.num_settlement_batch IN
          <foreach collection="numSettlementBatchList" item="numSettlementBatch" index="index" open="("
                   separator="," close=")">
            #{numSettlementBatch}
          </foreach>
        </if>
      </where>
      GROUP BY
      rppsi.num_settlement_batch
    </if>
    ) x
    GROUP BY
    x.num_settlement_batch
    ORDER BY
    x.num_settlement_batch DESC
  </select>

  <select id="selectAgentAccountCurrencyTypeNum" resultType="java.lang.String">
    SELECT
      rppsi.fk_currency_type_num
    FROM
     ais_finance_center.r_payable_plan_settlement_installment AS rppsi
    WHERE rppsi.num_settlement_batch = #{numSettlementBatch}
    GROUP BY
      rppsi.fk_currency_type_num
      limit 1
  </select>

  <select id="iFileGroupByCurrencyInfo" resultType="com.get.salecenter.vo.IFileInfoVo">
    select max(id)id,agentid,max(agentName)agentName,max(agentAreaCountryId)agentAreaCountryId,
    max(address)address,max(bank_branch_name)bank_branch_name,max(bank_account)bank_account,max(area_country_code)area_country_code,
    max(bank_name)bank_name,max(bank_address)bank_address,max(accountCurrencyTypeNum)accountCurrencyTypeNum,
    max(bank_account_num)bank_account_num,max(fk_currency_type_num)fk_currency_type_num,
    max(swift_code)swift_code,max(nature)nature,max(num_settlement_batch)num_settlement_batch,max(ftk)ftk,
    sum(payable_amount)payable_amount from (
    select aa.id,aa.agentid,aa.agentName,aa.agentAreaCountryId,
    aa.address,aa.bank_branch_name,aa.bank_account,aa.area_country_code,aa.bank_name,aa.bank_address,aa.accountCurrencyTypeNum,
    aa.bank_account_num,
    IFNULL(bb.fk_currency_type_num_exchange,aa.fk_currency_type_num)fk_currency_type_num,aa.swift_code,aa.nature,
    IFNULL(aa.payable_amount*exchange_rate,aa.payable_amount)payable_amount,
    aa.num_settlement_batch,aa.name,aa.ftk
    from (select max(id)id,max(agentid)agentid,max(agentName)agentName,max(agentAreaCountryId)agentAreaCountryId,
    max(address)address,max(bank_branch_name)bank_branch_name,max(bank_account)bank_account,max(area_country_code)area_country_code,
    max(bank_name)bank_name,max(bank_address)bank_address,max(accountCurrencyTypeNum)accountCurrencyTypeNum,
    max(bank_account_num)bank_account_num,
    max(fk_currency_type_num)fk_currency_type_num,max(swift_code)swift_code,max(nature)nature,
    SUM(payableamount)payable_amount,
    max(num_settlement_batch)num_settlement_batch,group_concat(studentname separator ',')name,
    max(ftk)ftk
    from (
    SELECT
    mpp.id,
    a.id AS agentid,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    a.fk_area_country_id AS agentAreaCountryId,
    a.fk_area_city_id AS agentAreaCityId,
    a.address,
    maca.bank_branch_name,
    maca.bank_account,maca.area_country_code,
    maca.bank_name,
    maca.bank_address,
    maca.fk_currency_type_num AS accountCurrencyTypeNum,
    rppsi.amount_actual AS payableAmount,
    rppsi.num_settlement_batch,
    maca.bank_account_num,
    mpp.fk_currency_type_num,
    s.name studentname,
    CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
    a.nature,mpp.fk_type_key as ftk
    FROM
    ais_sale_center.m_payable_plan AS mpp
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_offer_item'
    WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_offer_item'
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id

    UNION ALL
    SELECT
    mpp.id,
    a.id AS agentid,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    a.fk_area_country_id AS agentAreaCountryId,
    a.fk_area_city_id AS agentAreaCityId,
    a.address,

    maca.bank_branch_name,
    maca.bank_account,maca.area_country_code,
    maca.bank_name,
    maca.bank_address,
    maca.fk_currency_type_num AS accountCurrencyTypeNum,
    rppsi.amount_actual AS payableAmount,
    rppsi.num_settlement_batch,
    maca.bank_account_num,
    mpp.fk_currency_type_num,
    s.name studentname,
    CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
    a.nature,mpp.fk_type_key as ftk
    FROM
    ais_sale_center.m_payable_plan AS mpp
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_accommodation'
    WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_accommodation'
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    <!--
            INNER JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
            AND ras.is_active = 1
                    INNER JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
                    AND ms.is_active = 1
                     -->

    UNION ALL
    SELECT
    mpp.id,
    a.id AS agentid,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    a.fk_area_country_id AS agentAreaCountryId,
    a.fk_area_city_id AS agentAreaCityId,
    a.address,

    maca.bank_branch_name,
    maca.bank_account,maca.area_country_code,
    maca.bank_name,
    maca.bank_address,
    maca.fk_currency_type_num AS accountCurrencyTypeNum,
    rppsi.amount_actual AS payableAmount,
    rppsi.num_settlement_batch,
    maca.bank_account_num,
    mpp.fk_currency_type_num,
    s.name studentname,
    CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
    a.nature,mpp.fk_type_key as ftk
    FROM
    ais_sale_center.m_payable_plan AS mpp
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_insurance'
    WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_insurance'
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement AND a.is_active = 1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id

    UNION ALL
    SELECT
    mpp.id,
    a.id AS agentid,
    case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
    a.fk_area_country_id AS agentAreaCountryId,
    a.fk_area_city_id AS agentAreaCityId,
    a.address,

    maca.bank_branch_name,
    maca.bank_account,maca.area_country_code,
    maca.bank_name,
    maca.bank_address,
    maca.fk_currency_type_num AS accountCurrencyTypeNum,
    rppsi.amount_actual AS payableAmount,
    rppsi.num_settlement_batch,
    maca.bank_account_num,
    mpp.fk_currency_type_num,
    s.name studentname,
    CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
    a.nature,mpp.fk_type_key as ftk
    FROM
    ais_sale_center.m_payable_plan AS mpp
    -- 实际支付金额
    INNER JOIN (
    SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
    MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
    INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_service_fee'
    WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.num_settlement_batch
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_service_fee'
    INNER JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement AND a.is_active = 1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    INNER JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id

    )a
    GROUP BY a.agentid,fk_currency_type_num)aa
    left join (select num_settlement_batch,fk_agent_id,fk_currency_type_num,fk_type_key ,fk_currency_type_num_exchange,exchange_rate
    from r_payable_plan_settlement_batch_exchange
    group by num_settlement_batch,fk_agent_id,fk_currency_type_num,fk_type_key,fk_currency_type_num_exchange,exchange_rate)bb
    on aa.num_settlement_batch=bb.num_settlement_batch
    and aa.agentid=bb.fk_agent_id
    and aa.fk_currency_type_num=bb.fk_currency_type_num
    and aa.ftk=bb.fk_type_key
    )cc group by cc.agentid,cc.fk_currency_type_num
  </select>
  <select id="iFileExcelInfo" resultType="com.get.salecenter.vo.IFileInfoVo">
    SELECT
      mpp.id,
      a.id AS agentid,
      case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
      a.fk_area_country_id AS agentAreaCountryId,
      a.fk_area_city_id AS agentAreaCityId,
      a.address,
      msoi.fk_staff_id,
      maca.bank_branch_name,macaa.bank_branch_name cbank_branch_name,
      maca.bank_account,macaa.bank_account cbank_account,maca.area_country_code,
      maca.bank_name,macaa.bank_name cbank_name,
      maca.bank_address,macaa.bank_address cbank_address,
      maca.fk_currency_type_num AS accountCurrencyTypeNum,
      rppsi.amount_actual AS payableAmount,
      maca.bank_account_num,macaa.bank_account_num cbank_account_num,
      mpp.fk_currency_type_num,
      s.name studentname,
      CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
            a.nature,mpp.fk_type_key as ftk
        FROM
            ais_sale_center.m_payable_plan AS mpp
                -- 实际支付金额
                INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
                       MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_offer_item'
                WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_offer_item'
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    left join ais_sale_center.m_agent_contract_account macaa on a.id=macaa.fk_agent_id and macaa.fk_currency_type_num='CNY' and macaa.is_active =1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id

    UNION ALL
    SELECT
      mpp.id,
      a.id AS agentid,
      case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
      a.fk_area_country_id AS agentAreaCountryId,
      a.fk_area_city_id AS agentAreaCityId,
      a.address,
      msa.fk_staff_id,
      maca.bank_branch_name,macaa.bank_branch_name cbank_branch_name,
      maca.bank_account,macaa.bank_account cbank_account,maca.area_country_code,
      maca.bank_name,macaa.bank_name cbank_name,
      maca.bank_address,macaa.bank_address cbank_address,
      maca.fk_currency_type_num AS accountCurrencyTypeNum,
      rppsi.amount_actual AS payableAmount,
      maca.bank_account_num,macaa.bank_account_num cbank_account_num,
      mpp.fk_currency_type_num,
      s.name studentname,
      CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
            a.nature,mpp.fk_type_key as ftk
        FROM
            ais_sale_center.m_payable_plan AS mpp
                -- 实际支付金额
                INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
                       MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_accommodation'
                WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_accommodation'
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    left join ais_sale_center.m_agent_contract_account macaa on a.id=macaa.fk_agent_id and macaa.fk_currency_type_num='CNY' and macaa.is_active =1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
    UNION ALL
    SELECT
      mpp.id,
      a.id AS agentid,
      case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
      a.fk_area_country_id AS agentAreaCountryId,
      a.fk_area_city_id AS agentAreaCityId,
      a.address,
      msi.fk_staff_id,
      maca.bank_branch_name,macaa.bank_branch_name cbank_branch_name,
      maca.bank_account,macaa.bank_account cbank_account,maca.area_country_code,
      maca.bank_name,macaa.bank_name cbank_name,
      maca.bank_address,macaa.bank_address cbank_address,
      maca.fk_currency_type_num AS accountCurrencyTypeNum,
      rppsi.amount_actual AS payableAmount,
      maca.bank_account_num,macaa.bank_account_num cbank_account_num,
      mpp.fk_currency_type_num,
      s.name studentname,
      CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
            a.nature,mpp.fk_type_key as ftk
        FROM
            ais_sale_center.m_payable_plan AS mpp
                -- 实际支付金额
                INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
                       MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_insurance'
                WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_insurance'
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    left join ais_sale_center.m_agent_contract_account macaa on a.id=macaa.fk_agent_id and macaa.fk_currency_type_num='CNY' and macaa.is_active =1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id

    UNION ALL
    SELECT
      mpp.id,
      a.id AS agentid,
      case when a.name_note != null OR a.name_note != "" then CONCAT(a.NAME,CONCAT("(",a.name_note,")")) ELSE a.NAME END agentName,
      a.fk_area_country_id AS agentAreaCountryId,
      a.fk_area_city_id AS agentAreaCityId,
      a.address,
      mssf.fk_staff_id,
      maca.bank_branch_name,macaa.bank_branch_name cbank_branch_name,
      maca.bank_account,macaa.bank_account cbank_account,maca.area_country_code,
      maca.bank_name,macaa.bank_name cbank_name,
      maca.bank_address,macaa.bank_address cbank_address,
      maca.fk_currency_type_num AS accountCurrencyTypeNum,
      rppsi.amount_actual AS payableAmount,
      maca.bank_account_num,macaa.bank_account_num cbank_account_num,
      mpp.fk_currency_type_num,
      s.name studentname,
      CASE maca.bank_code_type WHEN 'SwiftCode' THEN maca.bank_code WHEN 'BSB' THEN CONCAT('BSB:',maca.bank_code) ELSE '' END swift_code,
            a.nature,mpp.fk_type_key as ftk
        FROM
            ais_sale_center.m_payable_plan AS mpp
                -- 实际支付金额
                INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual,
                       MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.id = rppsi1.fk_payable_plan_id AND mpp1.fk_type_key = 'm_student_service_fee'
                WHERE rppsi1.num_settlement_batch = #{numSettlementBatch}
    GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
    ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
    INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id
    AND mpp.fk_type_key = 'm_student_service_fee'
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = rppsi.fk_agent_id_settlement
    left join ais_sale_center.m_agent_contract_account macaa on a.id=macaa.fk_agent_id and macaa.fk_currency_type_num='CNY' and macaa.is_active =1
    LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    LEFT JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
  </select>
    <select id="getHedgeAmount" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp
        -- 计算应付计划分期表为未处理状态的实付金额
        LEFT JOIN (
          SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
          INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
          WHERE mpp1.id = #{payablePlanId}
            AND (rppsi1.fk_invoice_receivable_plan_id is not null)
          GROUP BY mpp1.id
        )a1 ON a1.id = mpp.id
        -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
        LEFT JOIN (
          SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
          INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
          WHERE mpp1.id = #{payablePlanId}
            AND (rppsi1.fk_invoice_receivable_plan_id is not null)
          GROUP BY mpp1.id
        )a2 ON a2.id = mpp.id
        -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
        LEFT JOIN (
          SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
          SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM ais_sale_center.m_payable_plan AS mpp1
          INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
          AND rppsi1.num_settlement_batch is not null
          WHERE mpp1.id = #{payablePlanId}
            AND (rppsi1.fk_invoice_receivable_plan_id is not null)
          GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
          GROUP BY b.id
        )a3 ON a3.id = mpp.id
    </select>
</mapper>