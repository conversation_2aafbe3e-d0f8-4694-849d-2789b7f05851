package com.get.financecenter.controller;

import com.get.core.tool.api.Result;
import com.get.financecenter.vo.OccVo;
import com.get.financecenter.service.ImportService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@RestController
public class ImportController {

    @Resource
    private ImportService importService;

    @PostMapping("/importReGea")
    public Result<BigDecimal> importRe(@RequestBody List<OccVo> receivedOcList, @RequestParam("receivablePlanId")Long receivablePlanId, @RequestParam("targetId") Long targetId, @RequestParam("currency")String currency){
        return importService.importReceivableData(receivedOcList,receivablePlanId,targetId,currency);
    }

    @PostMapping("/importPayGea")
    public void importPay(@RequestBody List<OccVo> hiPaidInfoList, @RequestParam("payablePlanId") Long payablePlanId, @RequestParam("currency") String currency,
                          @RequestParam("agentId") Long agentId, @RequestParam("payableAmount") Double payableAmount, @RequestParam("receivableFee") Double receivableFee){
         importService.importPayableData(hiPaidInfoList,payablePlanId,currency,agentId,BigDecimal.valueOf(payableAmount),receivableFee);
    }
}
