package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.CompanyAccountingItemMapper;
import com.get.financecenter.dto.CompanyAccountingDto;
import com.get.financecenter.dto.CompanyAccountingItemDto;
import com.get.financecenter.dto.CompanyAccountingItemOperateDto;
import com.get.financecenter.entity.CompanyAccountingItem;
import com.get.financecenter.enums.AccountingTypeEnum;
import com.get.financecenter.service.ICompanyAccountingItemService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.utils.GetNameUtils;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import com.get.financecenter.vo.CompanyAccountingVo;
import com.get.permissioncenter.entity.Company;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 套账科目服务实现类
 */
@Service("financeCompanyAccountingItemService")
public class CompanyAccountingItemServiceImpl extends ServiceImpl<CompanyAccountingItemMapper, CompanyAccountingItem> implements ICompanyAccountingItemService {
    @Resource
    private CompanyAccountingItemMapper companyAccountingItemMapper;
    @Autowired
    private UtilService utilService;
    @Resource
    private AccountingItemMapper accountingItemMapper;

    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;

    @Resource
    private GetNameUtils getNameUtils;


    @Override
    public Integer updateById(CompanyAccountingItemDto companyAccountingItemDto) {
        if (GeneralTool.isEmpty(companyAccountingItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        if (GeneralTool.isEmpty(companyAccountingItemDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CompanyAccountingItem companyAccountingItemSearch = companyAccountingItemMapper.selectById(companyAccountingItemDto.getId());
        if (GeneralTool.isEmpty(companyAccountingItemSearch)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CompanyAccountingItem companyAccountingItem = BeanCopyUtils.objClone(companyAccountingItemDto, CompanyAccountingItem::new);
        utilService.setUpdateInfo(companyAccountingItem);
        LambdaUpdateWrapper<CompanyAccountingItem> companyAccountingItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        companyAccountingItemLambdaUpdateWrapper.eq(CompanyAccountingItem::getId,companyAccountingItemDto.getId());
        companyAccountingItemLambdaUpdateWrapper.set(CompanyAccountingItem::getGmtModified,companyAccountingItem.getGmtModified());
        companyAccountingItemLambdaUpdateWrapper.set(CompanyAccountingItem::getGmtModifiedUser,companyAccountingItem.getGmtModifiedUser());
        companyAccountingItemLambdaUpdateWrapper.set(CompanyAccountingItem::getAmountDrOpeningBalance,companyAccountingItem.getAmountDrOpeningBalance());
        companyAccountingItemLambdaUpdateWrapper.set(CompanyAccountingItem::getAmountCrOpeningBalance,companyAccountingItem.getAmountCrOpeningBalance());

        return companyAccountingItemMapper.update(companyAccountingItem,companyAccountingItemLambdaUpdateWrapper);



    }

    @Override
    public Integer save(CompanyAccountingItemDto companyAccountingItemDto) {
        if (GeneralTool.isEmpty(companyAccountingItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(companyAccountingItemDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        if (GeneralTool.isEmpty(companyAccountingItemDto.getFkAccountingItemId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
        }
        Long fkBalanceSheetTypeIdByAccountingItemId = accountingItemMapper.getFkBalanceSheetTypeIdByAccountingItemId(companyAccountingItemDto.getFkAccountingItemId());

        CompanyAccountingItem companyAccountingItem = BeanCopyUtils.objClone(companyAccountingItemDto, CompanyAccountingItem::new);
        companyAccountingItem.setFkAccountingItemId(companyAccountingItemDto.getFkAccountingItemId());
        companyAccountingItem.setFkCompanyId(companyAccountingItemDto.getFkCompanyId());
        utilService.setCreateInfo(companyAccountingItem);
        companyAccountingItem.setFkBalanceSheetTypeId(fkBalanceSheetTypeIdByAccountingItemId);

       return companyAccountingItemMapper.insert(companyAccountingItem);

    }


    @Override
    public List<CompanyAccountingItemVo> getCompanyAccountingItem(CompanyAccountingItemDto companyAccountingItemDto, Page page) {
        if (GeneralTool.isEmpty(companyAccountingItemDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        LambdaQueryWrapper<CompanyAccountingItem> wrapper = new LambdaQueryWrapper();
        IPage<CompanyAccountingItem> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<CompanyAccountingItemVo> companyAccountingItemVos =companyAccountingItemMapper.getCompanyAccountingItem(pages,companyAccountingItemDto);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(companyAccountingItemVos)){
            return Collections.emptyList();
        }

        for (CompanyAccountingItemVo companyAccountingItemVo : companyAccountingItemVos) {
            if (GeneralTool.isNotEmpty(companyAccountingItemVo.getFkCompanyId())){
                getNameUtils.getCompanyNameById(companyAccountingItemVo.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(companyAccountingItemVo.getDirection())) {
                companyAccountingItemVo.setDirectionName(companyAccountingItemVo.getDirection().equals("0") ? "借" : "贷");
            }
            if (GeneralTool.isNotEmpty(companyAccountingItemVo.getFkAccountingItemId())) {
                companyAccountingItemVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingName(companyAccountingItemVo.getFkAccountingItemId()));
            }
            if (GeneralTool.isNotEmpty(companyAccountingItemVo.getFkBalanceSheetTypeId())) {
                companyAccountingItemVo.setBalanceSheetTypeName("[" + AccountingTypeEnum.getNameById(Math.toIntExact(companyAccountingItemVo.getFkBalanceSheetTypeId())) + "]");
            }

        }
        return companyAccountingItemVos;


    }

    @Override
    public List<CompanyAccountingVo> getCompanyAccountingDatas(CompanyAccountingDto companyAccountingDto, Page page) {
        IPage<Company> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CompanyAccountingVo> accountingItemSelectVos =companyAccountingItemMapper.getCompanyAccountingDatas(pages,companyAccountingDto,SecureUtil.getCompanyIds());
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(accountingItemSelectVos)){
            return Collections.emptyList();
        }

        return accountingItemSelectVos;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOperation(CompanyAccountingItemOperateDto companyAccountingItemOperateDto) {
        if (GeneralTool.isEmpty(companyAccountingItemOperateDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(companyAccountingItemOperateDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }

        //新增ids不为空，添加
        if (GeneralTool.isNotEmpty(companyAccountingItemOperateDto.getAddFkAccountingItemIds())){
            for (Long addFkAccountingItemId : companyAccountingItemOperateDto.getAddFkAccountingItemIds()) {
                CompanyAccountingItem companyAccountingItem = new CompanyAccountingItem();
                companyAccountingItem.setFkCompanyId(companyAccountingItemOperateDto.getFkCompanyId());
                companyAccountingItem.setFkAccountingItemId(addFkAccountingItemId);
                companyAccountingItem.setFkBalanceSheetTypeId(accountingItemMapper.getFkBalanceSheetTypeIdByAccountingItemId(addFkAccountingItemId));
                utilService.setCreateInfo(companyAccountingItem);
                companyAccountingItemMapper.insert(companyAccountingItem);
            }


        }
        //删除ids不为空，删除
        if (GeneralTool.isNotEmpty(companyAccountingItemOperateDto.getDeleteFkAccountingItemIds())){
            List<Long> ids = companyAccountingItemMapper.selectBatchFkAccountingItemIds(companyAccountingItemOperateDto.getDeleteFkAccountingItemIds(), companyAccountingItemOperateDto.getFkCompanyId());
            if (GeneralTool.isNotEmpty(ids)){
                companyAccountingItemMapper.deleteBatchIds(ids);
            }

        }

    }

    @Override
    public List<Long> getCompanyProfitAndLossItemBind(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        List<Long> companyProfitAndLossItemBind = companyAccountingItemMapper.getCompanyProfitAndLossItemBind(fkCompanyId);
        return companyProfitAndLossItemBind;
    }


    @Override
    public void delete(Long id) {
        if (GeneralTool.isNotEmpty(id)){
            CompanyAccountingItem companyAccountingItem = companyAccountingItemMapper.selectById(id);
            if (GeneralTool.isEmpty(companyAccountingItem)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
            }
            int i = companyAccountingItemMapper.deleteById(id);
            if (i <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }

    }


}

