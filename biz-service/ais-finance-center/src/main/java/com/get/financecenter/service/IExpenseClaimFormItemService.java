package com.get.financecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.ExpenseClaimFormItemVo;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.dto.ExpenseClaimFormItemDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/7 16:22
 * @verison: 1.0
 * @description:
 */
public interface IExpenseClaimFormItemService extends BaseService<ExpenseClaimFormItem> {
    /**
     * @return void
     * @Description :批量新增
     * @Param [expenseClaimFormItemVos]
     * <AUTHOR>
     */
    void batchAdd(List<ExpenseClaimFormItemDto> expenseClaimFormItemDtos);

    /**
     * @return java.util.List<com.get.financecenter.vo.ExpenseClaimFormItemDto>
     * @Description :根据费用报销单id查找所有对应费用报销单子项对象集合
     * @Param [id]
     * <AUTHOR>
     */
    List<ExpenseClaimFormItemVo> getDtoByExpenseClaimFormId(Long id);

    /**
     * @return void
     * @Description :根据费用报销单id删除
     * @Param [expenseClaimFormId]
     * <AUTHOR>
     */
    void deleteByFkid(Long expenseClaimFormId);
}
