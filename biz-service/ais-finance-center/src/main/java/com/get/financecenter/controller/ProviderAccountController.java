package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.ProviderAccountDto;
import com.get.financecenter.vo.ProviderAccountVo;
import com.get.financecenter.service.IProviderAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/25 15:11
 * @verison: 1.0
 * @description: 结算账号管理控制器
 */
@Api(tags = "结算账号管理")
@RestController
@RequestMapping("finance/providerAccount")
public class ProviderAccountController {
    @Resource
    private IProviderAccountService providerAccountService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderAccountDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/结算账号管理/结算账号详情")
    @GetMapping("/{id}")
    public ResponseBo<ProviderAccountVo> detail(@PathVariable("id") Long id) {
        ProviderAccountVo data = providerAccountService.findProviderAccountById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [providerAccountVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/结算账号管理/新增结算账号")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ProviderAccountDto.Add.class)  ProviderAccountDto providerAccountDto) {
        return SaveResponseBo.ok(providerAccountService.addProviderAccount(providerAccountDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/结算账号管理/删除结算账号")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        providerAccountService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderAccountDto>
     * @Description :修改信息
     * @Param [providerAccountVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/结算账号管理/更新结算账号")
    @PostMapping("update")
    public ResponseBo<ProviderAccountVo> update(@RequestBody @Validated(ProviderAccountDto.Update.class)  ProviderAccountDto providerAccountDto) {
        return UpdateResponseBo.ok(providerAccountService.updateProviderAccount(providerAccountDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderAccountDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/结算账号管理/查询结算账号")
    @PostMapping("datas")
    public ResponseBo<ProviderAccountVo> datas(@RequestBody SearchBean<ProviderAccountDto> page) {
        List<ProviderAccountVo> datas = providerAccountService.getProviderAccounts(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :是否激活
     * @Param [providerAccountId, isActive]
     * <AUTHOR>
     */
    @ApiOperation(value = "是否激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/结算账号管理/是否激活")
    @PostMapping("isActive")
    public ResponseBo isActive(@RequestParam Long providerAccountId, Boolean isActive) {
        providerAccountService.isActive(providerAccountId, isActive);
        return ResponseBo.ok();
    }
}
