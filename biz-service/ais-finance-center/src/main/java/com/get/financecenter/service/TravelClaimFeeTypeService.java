package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.TravelClaimFeeTypeDto;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 差离报销费用类型服务接口
 */
public interface TravelClaimFeeTypeService{

    List<TravelClaimFeeTypeVo> getTravelClaimFeeTypes(@Valid TravelClaimFeeTypeDto travelClaimFeeTypeDto, Page page);

    void batchAdd(List<TravelClaimFeeTypeDto> travelClaimFeeTypeDtos);

    TravelClaimFeeTypeVo update(TravelClaimFeeTypeDto travelClaimFeeTypeDto);

    void deleteTravelClaimFeeType(Long id);

    void sort(List<Long> ids);

    void movingOrder(Integer start, Integer end);
}

