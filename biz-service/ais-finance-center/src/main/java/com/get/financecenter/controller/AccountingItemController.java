package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.AccountingItemDto;
import com.get.financecenter.service.AccountingItemService;
import com.get.financecenter.vo.AccountingItemDropdownMenuVo;
import com.get.financecenter.vo.AccountingItemSelectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "科目管理")
@RestController
@RequestMapping("finance/accountingItem")
public class AccountingItemController {

    @Resource
    private AccountingItemService accountingItemService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "科目下拉框", notes = "科目id: accountingItemId")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/科目下拉框")
    @GetMapping("/accountingItemSelect/{accountingItemId}")
    public ResponseBo<AccountingItemSelectVo> accountingItemSelectById(@PathVariable("accountingItemId") Long accountingItemId) {
        AccountingItemSelectVo accountingItemSelectVo = accountingItemService.accountingItemSelectById(accountingItemId);
        return new ResponseBo<>(accountingItemSelectVo);
    }

    @ApiOperation(value = "关联项下拉框", notes = "relationTargetKey: 关联项关联类型Key（目标类型表名）")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/关联项下拉框")
    @GetMapping("/relationTargetSelect/{relationTargetKey}")
    public ResponseBo<BaseSelectEntity> relationTargetSelect(@PathVariable("relationTargetKey") String relationTargetKey, @RequestParam("companyId") Long companyId) {
        return new ListResponseBo<>(accountingItemService.relationTargetSelect(relationTargetKey, companyId));
    }

    @ApiOperation(value = "科目类型下拉框", notes = "1=资产/2=负债/3=权益/4=成本/5=损益/6=共同")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/科目类型下拉框")
    @GetMapping("/accountingItemTypeSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> accountingItemTypeSelect() {
        return new ListResponseBo<>(accountingItemService.accountingItemTypeSelect());
    }

    @ApiOperation(value = "余额方向" , notes = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/余额方向")
    @GetMapping("/balanceDirection")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> balanceDirection() {
        return new ListResponseBo<>(accountingItemService.balanceDirection());
    }


    /**
     * 分页查询所有数据
     * @param page                      分页对象
     * @return 所有数据
     */
    @ApiOperation(value = "分页查询所有数据", notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/分页查询所有数据")
    @PostMapping("list")
    public ResponseBo<AccountingItemSelectVo> seletctAll(@RequestBody SearchBean<AccountingItemDto> page) {
        List<AccountingItemSelectVo> datas = accountingItemService.getAccountingItemAll(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


//    通过grade的参数查询对应的上一级
    @ApiOperation(value = "通过grade的参数查询对应的上一级", notes = "通过grade的参数查询对应的上一级")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/通过grade的参数查询对应的上一级")
    @GetMapping("selectParent")
    public ResponseBo<AccountingItemDropdownMenuVo> selectParent(@RequestParam  Integer grade) {
       return new ListResponseBo<>(accountingItemService.getAccountingItemByGrade(grade));
    }

    /**
     * 所有科目下拉
     * @return
     */
    @ApiOperation(value = "所有科目下拉", notes = "所有科目下拉")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/科目/所有科目下拉")
    @GetMapping("dropdownMenu")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<AccountingItemDropdownMenuVo> getAccountingItemDropdownMenu() {
        return new ListResponseBo<>(accountingItemService.getAccountingItemDropdownMenu());
    }

    @ApiOperation(value = "添加", notes = "添加")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/科目/添加")
    @PostMapping("add")
    public ResponseBo<String> add(@RequestBody AccountingItemDto accountingItemDto) {
        accountingItemService.save(accountingItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改", notes = "修改")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/科目/修改")
    @PostMapping("update")
    public ResponseBo<String> update(@RequestBody AccountingItemDto accountingItemDto) {
        accountingItemService.updateById(accountingItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "删除")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/科目/删除")
    @PostMapping("delete")
    public ResponseBo<String> delete(@RequestParam("id") Long id) {
        accountingItemService.delete(id);
        return ResponseBo.ok();
    }

}
