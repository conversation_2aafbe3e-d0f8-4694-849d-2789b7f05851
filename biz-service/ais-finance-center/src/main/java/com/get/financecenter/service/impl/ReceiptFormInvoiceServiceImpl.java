package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.InvoiceMapper;
import com.get.financecenter.dao.InvoiceTargetMapper;
import com.get.financecenter.dao.ReceiptFormInvoiceMapper;
import com.get.financecenter.dao.ReceiptFormItemMapper;
import com.get.financecenter.dao.ReceiptFormMapper;
import com.get.financecenter.entity.InvoiceTarget;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.entity.ReceiptFormInvoice;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IReceiptFormInvoiceService;
import com.get.financecenter.dto.BalancingReceiptFormDto;
import com.get.financecenter.dto.QuickReceiptFormDto;
import com.get.financecenter.dto.ReceiptFormInvoiceDto;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/1/6
 * @TIME: 17:58
 * @Description:
 **/
@Service
@Slf4j
public class ReceiptFormInvoiceServiceImpl extends BaseServiceImpl<ReceiptFormInvoiceMapper, ReceiptFormInvoice> implements IReceiptFormInvoiceService {
    @Resource
    private UtilService utilService;
    @Resource
    private ReceiptFormInvoiceMapper receiptFormInvoiceMapper;
    @Resource
    private ISaleCenterClient iSaleCenterClient;
    @Autowired
    private IExchangeRateService exchangeRateService;
    @Autowired
    private ReceiptFormMapper receiptFormMapper;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private InvoiceTargetMapper invoiceTargetMapper;
    @Autowired
    private ReceiptFormItemMapper receiptFormItemMapper;


    @Override
    public void update(ReceiptFormInvoiceDto receiptFormInvoiceDto) {
//        Example example = new Example(ReceiptFormInvoice.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkReceiptFormId", receiptFormInvoiceVo.getFkReceiptFormId());
//        criteria.andEqualTo("fkInvoiceId", receiptFormInvoiceVo.getFkInvoiceId());
//        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectByExample(example);

        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>query().lambda()
                .eq(ReceiptFormInvoice::getFkReceiptFormId, receiptFormInvoiceDto.getFkReceiptFormId())
                .eq(ReceiptFormInvoice::getFkInvoiceId, receiptFormInvoiceDto.getFkInvoiceId()));
        if (GeneralTool.isNotEmpty(receiptFormInvoices)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("bound_repeat"));
        }
        ReceiptFormInvoice receiptFormInvoice = new ReceiptFormInvoice();
        receiptFormInvoice.setFkInvoiceId(receiptFormInvoiceDto.getFkInvoiceId());
        receiptFormInvoice.setFkReceiptFormId(receiptFormInvoiceDto.getFkReceiptFormId());
        utilService.updateUserInfoToEntity(receiptFormInvoice);
        receiptFormInvoiceMapper.insertSelective(receiptFormInvoice);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (receiptFormInvoiceMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        receiptFormInvoiceMapper.deleteById(id);
    }

    @Override
    public Map<Long, Set<Long>> getInvoiceByFkReceiptFormIds(Set<Long> fkReceiptFormIds) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(fkReceiptFormIds)) {
            return map;
        }
//        Example example = new Example(ReceiptFormInvoice.class);
//        example.createCriteria().andIn("fkReceiptFormId",fkReceiptFormIds);
//        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectByExample(example);

        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>query().lambda().in(ReceiptFormInvoice::getFkReceiptFormId, fkReceiptFormIds));
        if (GeneralTool.isEmpty(receiptFormInvoices)) {
            return map;
        }
        for (ReceiptFormInvoice receiptFormInvoice : receiptFormInvoices) {
            //如果集合包含这个收款单id,则往原来的数据发票编号ids
            if (map.containsKey(receiptFormInvoice.getFkReceiptFormId())) {
                Set<Long> beforeInvoiceIds = map.get(receiptFormInvoice.getFkReceiptFormId());
                beforeInvoiceIds.add(receiptFormInvoice.getFkInvoiceId());
                map.put(receiptFormInvoice.getFkReceiptFormId(), beforeInvoiceIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            Set<Long> invoiceSet = new HashSet<>();
            invoiceSet.add(receiptFormInvoice.getFkInvoiceId());
            map.put(receiptFormInvoice.getFkReceiptFormId(), invoiceSet);
        }
        return map;
    }

    @Override
    public List<Long> getInvoiceByFormId(Long fkReceiptFormId) {
        if (GeneralTool.isEmpty(fkReceiptFormId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return receiptFormInvoiceMapper.getfkInvoiceIdsByfkReceiptFormId(fkReceiptFormId);
    }


    /**
     * Author Cream
     * Description : 快速创建收款单
     * Date 2022/4/27 10:32
     * Params: QuickReceiptFormVo quickReceiptFormVo
     * Return SaveResponseBo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo doQuickCreateReceiptForm(QuickReceiptFormDto quickReceiptFormDto) {
        Long receivablePlanId = quickReceiptFormDto.getReceivablePlanId();
        if (GeneralTool.isEmpty(receivablePlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Result<ReceivablePlanVo> planDtoResult = iSaleCenterClient.getReceivablePlanById(receivablePlanId);
        if (planDtoResult.isSuccess() && GeneralTool.isNotEmpty(planDtoResult.getData())) {
            ReceivablePlanVo planDto = planDtoResult.getData();
//            String planCurrency = planDto.getFkCurrencyTypeNum();
            String currencyTypeNum = quickReceiptFormDto.getFkCurrencyTypeNum();
            Long companyId = planDto.getFkCompanyId();
            BigDecimal receivedAmount = quickReceiptFormDto.getReceivedAmount();
            BigDecimal receivedFee = quickReceiptFormDto.getReceivedFee();
            utilService.setCreateInfo(quickReceiptFormDto);
            Date createTime = quickReceiptFormDto.getGmtCreate();
            String createUser = quickReceiptFormDto.getGmtCreateUser();
            ReceiptForm receiptForm = new ReceiptForm();
            receiptForm.setFkCompanyId(companyId);
            Long targetId = quickReceiptFormDto.getFkTypeTargetId();
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                receiptForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
                receiptForm.setFkTypeTargetId(quickReceiptFormDto.getFkTypeTargetId());
            } else if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                Result<StudentOfferItem> detail = iSaleCenterClient.getStudentOfferItemById(quickReceiptFormDto.getFkTypeTargetId());
                if (detail.isSuccess() && GeneralTool.isNotEmpty(detail.getData())) {
                    StudentOfferItem data = detail.getData();
                    Long providerId = data.getFkInstitutionProviderId();
                    if (GeneralTool.isEmpty(providerId) || providerId == -1) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("no_provider_found"));
                    }
                    receiptForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
                    receiptForm.setFkTypeTargetId(providerId);
                }
            } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                receiptForm.setFkTypeKey(TableEnum.BUSINESS_CHANNEL_ACC.key);
                Result<Long> result = iSaleCenterClient.getStudentAccommodationId(targetId);
                if (result.isSuccess() && result.getData() != null) {
                    receiptForm.setFkTypeTargetId(result.getData());
                }
            } else if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                receiptForm.setFkTypeKey(TableEnum.BUSINESS_CHANNEL_INS.key);
                Result<Long> result = iSaleCenterClient.getStudentInsuranceId(targetId);
                if (result.isSuccess() && result.getData() != null) {
                    receiptForm.setFkTypeTargetId(result.getData());
                }
            } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                receiptForm.setFkTypeKey(ProjectKeyEnum.M_STUDENT.key);
                Result<Long> result = iSaleCenterClient.getServiceFeeStudentIdsById(targetId);
                if (result.isSuccess() && result.getData() != null) {
                    receiptForm.setFkTypeTargetId(result.getData());
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type"));
            }
            if (receiptForm.getFkTypeTargetId() == null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            receiptForm.setFkBankAccountId(1L);
            receiptForm.setSummary(quickReceiptFormDto.getSummary());
            receiptForm.setNumBank("HKR20220425");
            receiptForm.setFkCurrencyTypeNum(currencyTypeNum);
            receiptForm.setAmount(receivedAmount);
            receiptForm.setExchangeRate(quickReceiptFormDto.getExchangeRate());
            receiptForm.setServiceFee(receivedFee);
            receiptForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(currencyTypeNum, "HKD"));
            receiptForm.setAmountHkd(receivedAmount.multiply(receiptForm.getExchangeRateHkd()));
            receiptForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(currencyTypeNum, "CNY"));
            receiptForm.setAmountRmb(receivedAmount.multiply(receiptForm.getExchangeRateRmb()));
            receiptForm.setSettlementStatus(0);
            receiptForm.setStatus(1);
            receiptForm.setGmtCreate(createTime);
            receiptForm.setGmtCreateUser(createUser);
            receiptFormMapper.insert(receiptForm);
            if (GeneralTool.isNotEmpty(receiptForm)) {
                receiptForm.setNumSystem(GetStringUtils.getReceiptFormNum(receiptForm.getId()));
                receiptFormMapper.updateById(receiptForm);
            }
                /*
                    创建收款单和发票绑定
                 */
            ReceiptFormInvoice rfi = new ReceiptFormInvoice();
            if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                InvoiceTarget invoice = invoiceTargetMapper.selectOne(Wrappers.<InvoiceTarget>lambdaQuery().eq(InvoiceTarget::getFkTypeKey, ProjectKeyEnum.BUSINESS_CHANNEL_INS.key)
                        .eq(InvoiceTarget::getFkTypeTargetId, receiptForm.getFkTypeTargetId()).eq(InvoiceTarget::getGmtCreateUser, "admin").lt(InvoiceTarget::getFkInvoiceId, 0));
                if (null != invoice) {
                    rfi.setFkInvoiceId(invoice.getFkInvoiceId());
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
                }
            } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                rfi.setFkInvoiceId(-3L);
            } else {
                rfi.setFkInvoiceId(-1L);
            }
            rfi.setFkReceiptFormId(receiptForm.getId());
            rfi.setGmtCreate(createTime);
            rfi.setGmtCreateUser(createUser);
            receiptFormInvoiceMapper.insert(rfi);
            log.info("绑定收款单ID{},发票ID{}", receiptForm.getId(), -1L);

            log.info("插入应付计划Id={},收款单Id={}", receivablePlanId, receiptForm.getId());
            ReceiptFormItem receiptFormItem = new ReceiptFormItem();
            receiptFormItem.setFkReceiptFormId(receiptForm.getId());
            receiptFormItem.setFkReceivablePlanId(receivablePlanId);
            receiptFormItem.setAmountReceipt(receivedAmount);
            receiptFormItem.setServiceFee(quickReceiptFormDto.getReceivedFee());
            BigDecimal finalExchangeRate = receiptForm.getExchangeRate();
            receiptFormItem.setExchangeRateReceivable(finalExchangeRate);
            receiptFormItem.setAmountReceivable(receivedAmount
                    .add(receivedFee).multiply(finalExchangeRate));
            receiptFormItem.setAmountExchangeRate(new BigDecimal(0));
            receiptFormItem.setExchangeRateHkd(exchangeRateService.getRateByCurrency(currencyTypeNum, "HKD"));
            receiptFormItem.setAmountHkd(receivedAmount.add(receivedFee).multiply(receiptFormItem.getExchangeRateHkd()));
            receiptFormItem.setExchangeRateRmb(exchangeRateService.getRateByCurrency(currencyTypeNum, "CNY"));
            receiptFormItem.setAmountRmb(receivedAmount.add(receivedFee).multiply(receiptFormItem.getExchangeRateRmb()));
            receiptFormItem.setGmtCreate(createTime);
            receiptFormItem.setGmtCreateUser(createUser);
            receiptFormItemMapper.insert(receiptFormItem);
            return SaveResponseBo.ok();
        }
        return SaveResponseBo.error();
    }

    /**
     * Author Cream
     * Description : 快速创建收款单
     * Date 2022/4/27 10:32
     * Params: QuickReceiptFormVo quickReceiptFormVo
     * Return SaveResponseBo
     */
    public void doQuickCreateReceiptForms(List<QuickReceiptFormDto> quickReceiptFormDtos) {
        if (GeneralTool.isEmpty(quickReceiptFormDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Set<String> currencyTypeNums = quickReceiptFormDtos.stream().map(QuickReceiptFormDto::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String,BigDecimal> hkdRate = Maps.newHashMap();
        Map<String,BigDecimal> cnyRate = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            for (String currencyTypeNum : currencyTypeNums) {
                BigDecimal hkd = exchangeRateService.getRateByCurrency(currencyTypeNum, "HKD");
                hkdRate.put(currencyTypeNum,hkd);
                BigDecimal cny = exchangeRateService.getRateByCurrency(currencyTypeNum, "CNY");
                cnyRate.put(currencyTypeNum,cny);
            }
        }
        for (QuickReceiptFormDto quickReceiptFormDto : quickReceiptFormDtos) {
            Long receivablePlanId = quickReceiptFormDto.getReceivablePlanId();
            if (GeneralTool.isEmpty(receivablePlanId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
            }
            Result<ReceivablePlanVo> planDtoResult = iSaleCenterClient.getReceivablePlanById(receivablePlanId);
            if (planDtoResult.isSuccess() && GeneralTool.isNotEmpty(planDtoResult.getData())) {
                ReceivablePlanVo planDto = planDtoResult.getData();
    //            String planCurrency = planDto.getFkCurrencyTypeNum();
                String currencyTypeNum = quickReceiptFormDto.getFkCurrencyTypeNum();
                Long companyId = planDto.getFkCompanyId();
                BigDecimal receivedAmount = quickReceiptFormDto.getReceivedAmount();
                BigDecimal receivedFee = quickReceiptFormDto.getReceivedFee();
                utilService.setCreateInfo(quickReceiptFormDto);
                Date createTime = quickReceiptFormDto.getGmtCreate();
                String createUser = quickReceiptFormDto.getGmtCreateUser();
                ReceiptForm receiptForm = new ReceiptForm();
                receiptForm.setFkCompanyId(companyId);
                Long targetId = quickReceiptFormDto.getFkTypeTargetId();
                if (TableEnum.INSTITUTION_PROVIDER.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    receiptForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
                    receiptForm.setFkTypeTargetId(quickReceiptFormDto.getFkTypeTargetId());
                } else if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    Result<StudentOfferItem> detail = iSaleCenterClient.getStudentOfferItemById(quickReceiptFormDto.getFkTypeTargetId());
                    if (detail.isSuccess() && GeneralTool.isNotEmpty(detail.getData())) {
                        StudentOfferItem data = detail.getData();
                        Long providerId = data.getFkInstitutionProviderId();
//                        if (GeneralTool.isEmpty(providerId) || providerId == -1) {
//                            throw new GetServiceException(LocaleMessageUtils.getMessage("no_provider_found"));
//                        }
                        receiptForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
                        receiptForm.setFkTypeTargetId(providerId);
                    }
                } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    receiptForm.setFkTypeKey(TableEnum.BUSINESS_CHANNEL_ACC.key);
                    Result<Long> result = iSaleCenterClient.getStudentAccommodationId(targetId);
                    if (result.isSuccess() && result.getData() != null) {
                        receiptForm.setFkTypeTargetId(result.getData());
                    }
                } else if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    receiptForm.setFkTypeKey(TableEnum.BUSINESS_CHANNEL_INS.key);
                    Result<Long> result = iSaleCenterClient.getStudentInsuranceId(targetId);
                    if (result.isSuccess() && result.getData() != null) {
                        receiptForm.setFkTypeTargetId(result.getData());
                    }
                } else if (TableEnum.SALE_STUDENT_SERVICE_FEE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    receiptForm.setFkTypeKey(ProjectKeyEnum.M_STUDENT.key);
                    Result<Long> result = iSaleCenterClient.getServiceFeeStudentIdsById(targetId);
                    if (result.isSuccess() && result.getData() != null) {
                        receiptForm.setFkTypeTargetId(result.getData());
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type"));
                }
                if (receiptForm.getFkTypeTargetId() == null) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
                }
                receiptForm.setSettlementStatus(1);
                receiptForm.setFkBankAccountId(1L);
                receiptForm.setSummary(quickReceiptFormDto.getSummary());
                receiptForm.setNumBank("HKR20220425");
                receiptForm.setFkCurrencyTypeNum(currencyTypeNum);
                receiptForm.setAmount(receivedAmount);
                receiptForm.setExchangeRate(quickReceiptFormDto.getExchangeRate());
                receiptForm.setServiceFee(receivedFee);
                receiptForm.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(currencyTypeNum))?hkdRate.get(currencyTypeNum):new BigDecimal("1.0000"));
                receiptForm.setAmountHkd(receivedAmount.multiply(receiptForm.getExchangeRateHkd()));
                receiptForm.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(currencyTypeNum))?cnyRate.get(currencyTypeNum):new BigDecimal("1.0000"));
                receiptForm.setAmountRmb(receivedAmount.multiply(receiptForm.getExchangeRateRmb()));
                receiptForm.setStatus(1);
                receiptForm.setGmtCreate(createTime);
                receiptForm.setGmtCreateUser(createUser);
                receiptFormMapper.insert(receiptForm);
                if (GeneralTool.isNotEmpty(receiptForm)) {
                    receiptForm.setNumSystem(GetStringUtils.getReceiptFormNum(receiptForm.getId()));
                    receiptFormMapper.updateById(receiptForm);
                }
                    /*
                        创建收款单和发票绑定
                     */
                ReceiptFormInvoice rfi = new ReceiptFormInvoice();
                if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    InvoiceTarget invoice = invoiceTargetMapper.selectOne(Wrappers.<InvoiceTarget>lambdaQuery().eq(InvoiceTarget::getFkTypeKey, ProjectKeyEnum.BUSINESS_CHANNEL_INS.key)
                            .eq(InvoiceTarget::getFkTypeTargetId, receiptForm.getFkTypeTargetId()).eq(InvoiceTarget::getGmtCreateUser, "admin").lt(InvoiceTarget::getFkInvoiceId, 0));
                    if (null != invoice) {
                        rfi.setFkInvoiceId(invoice.getFkInvoiceId());
                    } else {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
                    }
                } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(quickReceiptFormDto.getFkTypeKey())) {
                    rfi.setFkInvoiceId(-3L);
                } else {
                    rfi.setFkInvoiceId(-1L);
                }
                rfi.setFkReceiptFormId(receiptForm.getId());
                rfi.setGmtCreate(createTime);
                rfi.setGmtCreateUser(createUser);
                receiptFormInvoiceMapper.insert(rfi);
                log.info("绑定收款单ID{},发票ID{}", receiptForm.getId(), -1L);

                log.info("插入应付计划Id={},收款单Id={}", receivablePlanId, receiptForm.getId());
                ReceiptFormItem receiptFormItem = new ReceiptFormItem();
                receiptFormItem.setFkReceiptFormId(receiptForm.getId());
                receiptFormItem.setFkReceivablePlanId(receivablePlanId);
                receiptFormItem.setAmountReceipt(receivedAmount);
                receiptFormItem.setServiceFee(quickReceiptFormDto.getReceivedFee());
                BigDecimal finalExchangeRate = receiptForm.getExchangeRate();
                receiptFormItem.setExchangeRateReceivable(finalExchangeRate);
                receiptFormItem.setAmountReceivable(receivedAmount
                        .add(receivedFee).multiply(finalExchangeRate));
                receiptFormItem.setAmountExchangeRate(BigDecimal.ZERO);
                receiptFormItem.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(currencyTypeNum))?hkdRate.get(currencyTypeNum):new BigDecimal("1.0000"));
                receiptFormItem.setAmountHkd(receivedAmount.add(receivedFee).multiply(receiptFormItem.getExchangeRateHkd()));
                receiptFormItem.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(currencyTypeNum))?cnyRate.get(currencyTypeNum):new BigDecimal("1.0000"));
                receiptFormItem.setAmountRmb(receivedAmount.add(receivedFee).multiply(receiptFormItem.getExchangeRateRmb()));
                receiptFormItem.setGmtCreate(createTime);
                receiptFormItem.setGmtCreateUser(createUser);
                receiptFormItemMapper.insert(receiptFormItem);
            }
        }
    }

    /**
     * Author Cream
     * Description : //校验发票是否合收款单绑定
     * Date 2023/4/21 14:29
     * Params:
     * Return
     */
    @Override
    public Boolean checkReceiptInvoiceMappingExist(Long invoiceId) {
        if (Objects.isNull(invoiceId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<ReceiptFormInvoice> receiptFormInvoices = receiptFormInvoiceMapper.selectList(Wrappers.<ReceiptFormInvoice>lambdaQuery().eq(ReceiptFormInvoice::getFkInvoiceId, invoiceId));
        return GeneralTool.isNotEmpty(receiptFormInvoices);
    }

    /**
     * 批量找平
     * @param balancingReceiptFormDtos
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo batchBalancingReceiptForm(List<BalancingReceiptFormDto> balancingReceiptFormDtos) {
        //当为0时，不需要做找平处理
        balancingReceiptFormDtos = balancingReceiptFormDtos.stream().filter(b -> !BigDecimal.ZERO.equals(b.getDiffReceivableAmount())).collect(Collectors.toList());

        List<QuickReceiptFormDto> quickReceiptFormDtos = BeanCopyUtils.copyListProperties(balancingReceiptFormDtos, QuickReceiptFormDto::new, (balancingReceiptFormDto, quickReceiptFormDto) -> {
            quickReceiptFormDto.setReceivablePlanId(balancingReceiptFormDto.getId());
            quickReceiptFormDto.setReceivedFee(balancingReceiptFormDto.getDiffReceivableAmount());
            quickReceiptFormDto.setExchangeRate(BigDecimal.ONE);
            quickReceiptFormDto.setSummary("【批量找平】手续费找平");
        });

        //快速补单找平
        doQuickCreateReceiptForms(quickReceiptFormDtos);

        return ResponseBo.ok();
    }
}
