package com.get.financecenter.service;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.financecenter.dto.AgentSettlementDto;
import com.get.financecenter.vo.AgentSettlementItemVo;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.AgentSettlementBatchExportDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.salecenter.vo.CommissionSummaryBatchItemDetailVo;
import com.get.salecenter.vo.CommissionSummaryBatchItemVo;
import com.get.salecenter.vo.CommissionSummaryBatchVo;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.CancelFinancialConfirmationSettlementDto;
import com.get.salecenter.dto.CommissionSummaryBatchDetailDto;
import com.get.salecenter.dto.CommissionSummaryBatchDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.PayablePlanSettlementBatchExchangeDto;
import com.get.salecenter.dto.SettlementAgentAccountUpdateDto;
import com.get.salecenter.dto.SubmitFinancialSettlementSummaryDto;
import com.get.salecenter.dto.SubmitSettlementDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 代理结算佣金业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/12/21 11:17
 */
public interface AgencyCommissionSettlementService {
    /**
     * 代理佣金结算列表
     *
     * @Date 11:19 2021/12/21
     * <AUTHOR>
     */
    ListResponseBo agentSettlementList(SearchBean<AgentSettlementQueryDto> page);

    /**
     * 代理佣金结算子项列表
     *
     * @Date 11:19 2021/12/21
     * <AUTHOR>
     */
    AgentSettlementItemVo itemDatas(AgentSettlementDto agentSettlementDto);


    /**
     * 佣金结算第四步导出
     * @param commissionSummaryDto
     * @param response
     */
    void agentSettlementListFourthStepExport(CommissionSummaryDto commissionSummaryDto, HttpServletResponse response);

    /**
     * 代理结算导出
     * @param agentSettlementVo
     */
    void agentSettlementListExport(AgentSettlementQueryDto agentSettlementVo);

    /**
     * 第三步佣金结算总额表导出
     * @param agentSettlementVo
     */
    void agentSettlementGrossAmountExport(AgentSettlementQueryDto agentSettlementVo);

    /**
     * 代理佣金结算提交
     *
     * @Date 10:34 2021/12/22
     * <AUTHOR>
     */
    void submitSettlement(List<SubmitSettlementDto> submitSettlementDtoList);

    /**
     * 更新结算标记
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    Boolean updateSettlementAgentAccount(List<SettlementAgentAccountUpdateDto> settlementAgentAccountUpdateDtoList);


    /**
     * 代理附件校验
     *
     * @Date 12:23 2022/12/13
     * <AUTHOR>
     */
    String checkAgentAttachment(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList);

    /**
     * 批量下载对账单
     *
     * @return
     * @Date 14:33 2022/3/23
     * <AUTHOR>
     */
    void batchDownloadAgentReconciliationExcel(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList, HttpServletResponse response);


    /**
     * 提交代理确认结算
     *
     * @param agentSettlementBatchExportVoList
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    void agentConfirmSettlement(List<AgentSettlementBatchExportDto> agentSettlementBatchExportVoList);

    /**
     * 取消代理确认结算 (第三步取消按钮)
     *
     * @Date 14:25 2023/2/21
     * <AUTHOR>
     */
    void cancelAgentConfirmSettlement(List<CancelSettlementDto> cancelSettlementVoList);


    ListResponseBo<CompanyConfigInfoDto> getSettlementConfigKey(String configKey);

    /**
     * 修改实际支付金额
     *
     * @Date 17:28 2022/4/2
     * <AUTHOR>
     */
    void updateInstallmentAmountActual(List<InstallmentAmountActualUpdateDto> installmentAmountActualUpdateDtos);


    /**
     * 代理佣金结算取消
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    void cancelSettlement(List<CancelSettlementDto> cancelSettlementVoList);

    /**
     * 财务确认代理佣金结算
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    Boolean financeConfirmSettlement(List<Long> payablePlanIdList);

    /**
     * 应付计划编辑详情回显
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    StudentPlanVo financePlanDetails(Long planId);

    /**
     * 批量编辑应付计划
     *
     * @return
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    void batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList);

    /**
     * 财务佣金汇总列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    ListResponseBo<CommissionSummaryVo> commissionSummary(SearchBean<CommissionSummaryDto> page);

    /**
     * 提交财务结算汇总
     *
     * @param submitFinancialSettlementSummaryDto
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    void submitFinancialSettlementSummary(List<SubmitFinancialSettlementSummaryDto> submitFinancialSettlementSummaryDto);

    /**
     * 取消财务确认结算
     *
     * @Date 10:22 2021/12/27
     * <AUTHOR>
     */
    void cancelFinancialConfirmationSettlement(List<CancelFinancialConfirmationSettlementDto> cancelFinancialConfirmationSettlementDtos);

    /**
     * 财务佣金汇总批次列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    List<CommissionSummaryBatchVo> commissionSummaryBatchList(CommissionSummaryBatchDto commissionSummaryBatchDto, SearchBean<CommissionSummaryBatchDto> page);

    /**
     * 财务佣金汇总批次子项列表
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList(CommissionSummaryBatchDto commissionSummaryBatchDto);

    /**
     * 财务佣金汇总批次子项列表详情回显
     *
     * @Date 15:42 2021/12/24
     * <AUTHOR>
     */
    CommissionSummaryBatchItemDetailVo commissionSummaryBatchItemDetail(CommissionSummaryBatchDetailDto commissionSummaryBatchDetailDto);

    /**
     * 保存结算汇总表汇率
     *
     * @param payablePlanSettlementBatchExchangeDtos
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    void saveExchangeRate(List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos);

    void saveIsExchangeInput(List<PayablePlanSettlementBatchExchangeDto> payablePlanSettlementBatchExchangeDtos);

    /**
     * 保存结算汇总表汇率并导出Excel
     *
     * @param
     * @Date 10:10 2021/12/28
     * <AUTHOR>
     */
    void commissionSummaryBatchItemExcel(CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response);

    /**
     * 保存结算汇总表汇率并导出ifile
     *
     * @param
     * @Date 10:10 2022/03/28
     * <AUTHOR>
     */
    void commissionSummaryBatchItemIfile(CommissionSummaryBatchDto commissionSummaryBatchDto, HttpServletResponse response);


    /**
     * 自动生成付款单
     *
     * @param autoGeneratePaymentDto
     * @Date 14:48 2021/12/28
     * <AUTHOR>
     */
    void autoGeneratePayment(AutoGeneratePaymentDto autoGeneratePaymentDto);

    /**
     * 绑定支付流水号
     *
     * @Date 16:56 2021/12/28
     * <AUTHOR>
     */
    void bindPaymentSerialNumber(BindPaymentSerialNumberDto bindPaymentSerialNumberDto);

    /**
     * 代理对账单Excel导出
     *
     * @Date 17:21 2022/1/5
     * <AUTHOR>
     */
    void agentStatementExcelExport(AgentStatementExcelExportDto agentStatementExcelExportDto, HttpServletResponse response);

    /**
     * 财务佣金汇总批次取消（第五步取消）
     *
     * @Date 16:41 2022/4/19
     * <AUTHOR>
     */
    void cancelFinancialBatchSettlement(List<CancelFinancialBatchSettlementDto> cancelFinancialBatchSettlementDtos);

    /**
     * 财务结算汇总锁定代理(第四步锁定代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    void financialSettlementAgentLocking(List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos);

    /**
     * 财务结算汇总解锁代理(第四步解锁代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    void financialSettlementAgentUnlocking(List<FinancialSettlementAgentLockingDto> financialSettlementAgentLockingDtos);

    /**
     * 删除佣金结算（佣金第一二三步专用）
     *
     * @Date 16:38 2022/5/23
     * <AUTHOR>
     */
    void deleteSettlement(List<DeleteSettlementDeleteDto> deleteSettlementDeleteDtoList);

    /**
     * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    void deleteFinancialSettlementSummary(List<DeleteFinancialSettlementSummaryDeleteDto> deleteFinancialSettlementSummaryDeleteDtoList);

    /**
     * 待结算标记
     *
     * @Date 18:11 2024/1/17
     * <AUTHOR>
     */
    void pendingSettlementMark(PendingSettlementMarkDto pendingSettlementMarkDto);

    /**
     * 查询银行账户佣金结算状态
     * @param accountId
     * @return
     */
    Integer getAccountCommissionSettlementStatus(Long accountId);

    /**
     * 修改未结算佣金的银行币种标记
     * @param accountId
     * @param fkCurrencyTypeNum
     * @return
     */
    Boolean updateCommissionSettlementAccountCurrencyTypeNum(Long accountId, String fkCurrencyTypeNum);

    /**
     * 佣金结算标记 true:已有佣金结算 false:无佣金结算数据
     * @param accountId
     * @return
     */
    Boolean getCommissionSettlementAccountInfo(Long accountId);

    //    /**
//     * 代理对账单确认列表
//     *
//     * @Date 11:15 2021/12/21
//     * <AUTHOR>
//     */
//    List<AgencyStatementDto> agencyStatementDatas(SearchBean<AgencyStatementVo> page) ;
}
