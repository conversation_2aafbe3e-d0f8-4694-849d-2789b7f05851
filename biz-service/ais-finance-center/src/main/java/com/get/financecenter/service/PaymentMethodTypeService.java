package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.PaymentMethodTypeDto;
import com.get.financecenter.vo.PaymentMethodTypeVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 付款方式类型
 */
public interface PaymentMethodTypeService {

    /**
     * 查询付款方式类型
     * @param paymentMethodTypeDto
     * @param page
     * @return
     */
    List<PaymentMethodTypeVo> getPaymentMethodTypes(@Valid PaymentMethodTypeDto paymentMethodTypeDto, Page page);

    /**
     * 批量添加付款方式类型
     * @param paymentMethodTypeDtos
     */
    void batchAddPaymentMethodType(List<PaymentMethodTypeDto> paymentMethodTypeDtos);

    void updatePaymentMethodType(PaymentMethodTypeDto paymentMethodTypeDto);

    void deletePaymentMethodType(Long id);

    void sort(List<Long> ids);

    void movingOrder(Integer start, Integer end);
}

