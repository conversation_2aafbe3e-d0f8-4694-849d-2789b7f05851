package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.BankAccountVo;
import com.get.financecenter.entity.BankAccount;
import com.get.financecenter.dto.BankAccountDto;
import com.get.financecenter.dto.query.BankAccountQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/21 9:56
 * @verison: 1.0
 * @description:
 */
public interface IBankAccountService extends BaseService<BankAccount> {
    /**
     * @return com.get.salecenter.vo.BankAccountDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    BankAccountVo findBankAccountById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [bankAccountVos]
     * <AUTHOR>
     */
    void batchAdd(List<BankAccountDto> bankAccountDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.BankAccountDto
     * @Description :修改
     * @Param [bankAccountVo]
     * <AUTHOR>
     */
    BankAccountVo updateBankAccount(BankAccountDto bankAccountDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.BankAccountDto>
     * @Description :列表
     * @Param [bankAccountVo, page]
     * <AUTHOR>
     */
    List<BankAccountVo> getBankAccounts(BankAccountQueryDto bankAccountVo, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [bankAccountVos]
     * <AUTHOR>
     */
    void movingOrder(List<BankAccountDto> bankAccountDtos);

    /**
     * @return void
     * @Description :是否激活
     * @Param [bankAccountId, isActive]
     * <AUTHOR>
     */
    void isActive(Long bankAccountId, Boolean isActive);

    /**
     * 银行账户下拉框
     *
     * @param
     * @return
     */
    List<BankAccountVo> getBankAccountSelect(Long fkCompanyId);

    /**
     * 银行名称
     *
     * @param
     * @return
     */
    String getBankAccountNameById(Long id);


    /**
     * 根据银行账户ids获取名称map
     *
     * @param ids
     * @return
     */
    Map<Long, String> getBankAccountNameByIds(Set<Long> ids);
}
