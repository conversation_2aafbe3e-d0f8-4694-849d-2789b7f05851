package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.financecenter.dto.ExpenseClaimAgentContentDto;
import com.get.financecenter.vo.ExpenseClaimAgentContentVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 代理关联费用报销
 */
public interface ExpenseClaimAgentContentService {

    List<ExpenseClaimAgentContentVo> getExpenseClaimAgentContents(@Valid ExpenseClaimAgentContentDto expenseClaimAgentContentDto, Page page);

    void batchAdd(ExpenseClaimAgentContentDto expenseClaimAgentContentDto);

    ExpenseClaimAgentContentVo update(ExpenseClaimAgentContentDto expenseClaimAgentContentDto);

    void delete(Long id);

    void batchDelete(List<Long> ids);

    void sort(List<Long> ids);

    List<BaseSelectEntity> getExpenseClaimAgentContentList();

    void movingOrder(Integer start, Integer end);
}

