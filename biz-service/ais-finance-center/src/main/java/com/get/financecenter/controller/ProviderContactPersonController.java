package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.vo.ProviderContactPersonVo;
import com.get.financecenter.service.IProviderContactPersonService;
import com.get.financecenter.dto.ProviderContactPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/25 15:14
 * @verison: 1.0
 * @description: 供应商-联系人管理控制器
 */
@Api(tags = "供应商-联系人管理")
@RestController
@RequestMapping("finance/providerContactPerson")
public class ProviderContactPersonController {
    @Resource
    private IProviderContactPersonService providerContactPersonService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderContactPersonDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/供应商-联系人管理/供应商-联系人详情")
    @GetMapping("/{id}")
    public ResponseBo<ProviderContactPersonVo> detail(@PathVariable("id") Long id) {
        ProviderContactPersonVo data = providerContactPersonService.findProviderContactPersonById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [providerContactPersonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/供应商-联系人管理/新增供应商-联系人")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ProviderContactPersonDto.Add.class)  ProviderContactPersonDto providerContactPersonDto) {
        return SaveResponseBo.ok(providerContactPersonService.addProviderContactPerson(providerContactPersonDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/供应商-联系人管理/删除供应商-联系人")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        providerContactPersonService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderContactPersonDto>
     * @Description :修改信息
     * @Param [providerContactPersonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/供应商-联系人管理/更新供应商-联系人")
    @PostMapping("update")
    public ResponseBo<ProviderContactPersonVo> update(@RequestBody @Validated(ProviderContactPersonDto.Update.class) ProviderContactPersonDto providerContactPersonDto) {
        return UpdateResponseBo.ok(providerContactPersonService.updateProviderContactPerson(providerContactPersonDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ProviderContactPersonDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/供应商-联系人管理/查询供应商-联系人")
    @PostMapping("datas")
    public ResponseBo<ProviderContactPersonVo> datas(@RequestBody SearchBean<ProviderContactPersonDto> page) {
        List<ProviderContactPersonVo> datas = providerContactPersonService.getProviderContactPersons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
}
