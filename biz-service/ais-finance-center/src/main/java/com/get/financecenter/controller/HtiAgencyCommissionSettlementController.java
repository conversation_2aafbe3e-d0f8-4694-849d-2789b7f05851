package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.SettlementCommissionNoticeDto;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.vo.SettlementCommissionNoticeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 代理结算佣金
 *
 * <AUTHOR>
 * @date 2021/12/20 15:37
 */
@Api(tags = "代理佣金结算管理")
@RestController
@RequestMapping("finance/agencyCommissionSettlement/hti")
public class HtiAgencyCommissionSettlementController {

    @Resource
    private HtiAgencyCommissionSettlementService htiAgencyCommissionSettlementService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "财务佣金汇总列表第四步导出")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/佣金结算/财务佣金汇总列表第四步导出")
    @PostMapping("agentSettlementListFourthStepExportHti")
    public void agentSettlementListFourthStepExport(@RequestBody CommissionSummaryDto commissionSummaryDto, HttpServletResponse response){
        htiAgencyCommissionSettlementService.agentSettlementListFourthStepExport(commissionSummaryDto,response);
    }


    /**
     * 根据应付计划ids 获取结算标记
     *
     * @Date 12:07 2022/1/11
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getSettlementMarkByPayablePlanIds")
    public Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(@RequestBody List<Long> payablePlanIds) {
        return htiAgencyCommissionSettlementService.getSettlementMarkByPayablePlanIds(payablePlanIds);
    }

    @ApiOperation(value = "更新结算通知信息")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/佣金结算/更新结算通知信息")
    @PostMapping("updateSettlementNotificationInformation")
    public ResponseBo<SettlementCommissionNoticeVo> updateSettlementNotificationInformation(@RequestBody @Validated SettlementCommissionNoticeDto settlementCommissionNoticeDto) {
        htiAgencyCommissionSettlementService.updateSettlementNotificationInformation(settlementCommissionNoticeDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "结算通知信息详情")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/佣金结算/结算通知信息详情")
    @PostMapping("settlementNotificationInformationDetails/{id}")
    public ResponseBo<SettlementCommissionNoticeVo> settlementNotificationInformationDetails(@PathVariable("id") Long id) {
        return new ResponseBo<>(htiAgencyCommissionSettlementService.settlementNotificationInformationDetails(id));
    }


}
