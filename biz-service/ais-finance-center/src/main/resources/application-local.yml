#服务器端口
server:
  port: 8084

#数据源配置
spring:
  datasource:
    url: ${get.datasource.test.url}
    username: ${get.datasource.test.username}
    password: ${get.datasource.test.password}
    #url: *****************************************************************************************************************************
    #username: root
    #password: GETTEST_ROOT@AJL3W03D


# 汇率第三方接口id key
exchangeRate:
  secretId: AKID7F0rz8PQrCtFiR7BLq3IwDqQzGBl3il14I0G
  secretKey: c14m3z425z55cw4trAUo9gjcCMspyp75rc5oVOpc
