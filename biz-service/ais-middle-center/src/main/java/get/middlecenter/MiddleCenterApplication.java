package get.middlecenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableGetFeign
@SpringCloudApplication
@EnableAsync
public class MiddleCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_MIDDLE_CENTER, MiddleCenterApplication.class, args);
    }

}