package get.middlecenter.component;

import com.get.aismiddle.dto.UploadMediaAndAttachedRequestDto;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.partnercenter.dto.MediaAndAttachedDto;
import com.get.partnercenter.feign.IPartnerCenterClient;
import com.get.partnercenter.vo.MediaAndAttachedVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * partner中心上传组件类
 */
@Slf4j
@Component("partnerUploadAbstractHelper")
public class PartnerUploadAbstractHelper extends UploadAbstractHelper {

    @Resource
    private IPartnerCenterClient partnerCenterClient;

    @Override
    public void upload(UploadMediaAndAttachedRequestDto uploadMediaAndAttachedRequestDto) {
        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
        try {
            BeanUtils.copyProperties(uploadMediaAndAttachedRequestDto, mediaAndAttachedDto);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
        mediaAndAttachedDto.setGmtCreate(new Date());
        Result<List<MediaAndAttachedVo>> result = partnerCenterClient.addMediaAndAttachedList(Collections.singletonList(mediaAndAttachedDto));
        if (Result.isNotSuccess(result)) {
            throw new GetServiceException(result.getMessage());
        }
    }

}
