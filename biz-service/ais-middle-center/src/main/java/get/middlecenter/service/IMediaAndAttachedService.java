package get.middlecenter.service;

import com.get.aismiddle.dto.UploadMediaAndAttachedRequestDto;
import com.get.aismiddle.dto.UploadRequestDto;
import com.get.filecenter.dto.FileDto;

import java.util.List;

public interface IMediaAndAttachedService {

    /**
     * 上传文件
     *
     * @param uploadRequestDto
     * @return
     */
    List<FileDto> upload(UploadRequestDto uploadRequestDto);

    /**
     * 保存文件
     * @param uploadMediaAndAttachedRequest
     */
    void uploadMediaAndAttached(List<UploadMediaAndAttachedRequestDto> uploadMediaAndAttachedRequest);

}
