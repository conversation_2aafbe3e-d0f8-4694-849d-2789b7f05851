package get.middlecenter.service.impl;

import com.get.aismiddle.dto.UploadMediaAndAttachedRequestDto;
import com.get.aismiddle.dto.UploadRequestDto;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import get.middlecenter.component.UploadAbstractHelper;
import get.middlecenter.service.IMediaAndAttachedService;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class MediaAndAttachedServiceImpl implements IMediaAndAttachedService {
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource(name = "uploadHelperMap")
    private Map<String, UploadAbstractHelper> uploadHelperMap;

    @Override
    public List<FileDto> upload(UploadRequestDto uploadRequestDto) {
        if (GeneralTool.isEmpty(uploadRequestDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }

        //上传
        List<FileDto> files;
        Result<List<FileDto>> result;
        if (uploadRequestDto.getIsPrivateBucket()) {
            //私有桶
            result = fileCenterClient.uploadPrivateBucketFile(uploadRequestDto.getFiles(), uploadRequestDto.getServiceName(), uploadRequestDto.getGmtCreateUser(), uploadRequestDto.getPrefix());
        } else {
            //公有桶
            result = fileCenterClient.uploadHtiPublicFile(uploadRequestDto.getFiles(), uploadRequestDto.getServiceName(), uploadRequestDto.getGmtCreateUser(), uploadRequestDto.getPrefix());
        }
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            files = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(result.getMessage());
        }
        return files;
    }

    /**
     * 保存文件
     * @param uploadMediaAndAttachedRequestList
     */
    @Override
    public void uploadMediaAndAttached(List<UploadMediaAndAttachedRequestDto> uploadMediaAndAttachedRequestList) {
        //插入附件表
        for (UploadMediaAndAttachedRequestDto uploadMediaAndAttachedRequestDto : uploadMediaAndAttachedRequestList) {
            uploadHelperMap.get(uploadMediaAndAttachedRequestDto.getServiceName()).upload(uploadMediaAndAttachedRequestDto);
        }
    }

}
