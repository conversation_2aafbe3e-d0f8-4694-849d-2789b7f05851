package get.middlecenter.config;

import com.get.common.consts.LoggerModulesConsts;
import get.middlecenter.component.SaleUploadAbstractHelper;
import get.middlecenter.component.UploadAbstractHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 上传工厂Config
 */
@Configuration
public class UploadFactoryConfiguration {

    /**
     * 销售中心 上传组件类
     */
    @Resource(name = "saleUploadAbstractHelper")
    private SaleUploadAbstractHelper saleUploadAbstractHelper;

    @Bean(name = "uploadHelperMap")
    public Map<String, UploadAbstractHelper> payHelperMap() {
        Map<String, UploadAbstractHelper> payHelperMap = new HashMap<>();
        payHelperMap.put(LoggerModulesConsts.SALECENTER, saleUploadAbstractHelper);
        return payHelperMap;
    }
}
