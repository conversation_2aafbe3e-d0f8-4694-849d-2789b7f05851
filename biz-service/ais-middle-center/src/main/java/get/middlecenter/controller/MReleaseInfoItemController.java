package get.middlecenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.aismiddle.dto.ReleaseInfoItemDto;
import com.get.aismiddle.vo.ReleaseInfoItemVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import get.middlecenter.service.MReleaseInfoItemService;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发版信息子项
 */
@RestController
@RequestMapping("middle/releaseInfo")
public class MReleaseInfoItemController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private MReleaseInfoItemService mReleaseInfoItemService;

    @ApiOperation(value = "根据父项id查询所有子项数据", notes = "根据父项id查询所有子项数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息子项管理/查询")
    @GetMapping("getReleaseInfoItem")
    public ResponseBo<List<ReleaseInfoItemVo>> getReleaseInfoItemByReleaseInfoId(@RequestParam Long releaseInfoId) {
        return new ResponseBo<>(mReleaseInfoItemService.getAllReleaseInfoItemByReleaseInfoId(releaseInfoId));
    }

    @ApiOperation(value = "修改数据", notes = "修改数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = "中台中心/发版信息子项管理/修改")
    @PostMapping("edit")
    public ResponseBo edit(@RequestBody ReleaseInfoItemDto releaseInfoItemDto) {
        mReleaseInfoItemService.editReleaseInfoItem(releaseInfoItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "添加数据", notes = "添加数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = "中台中心/发版信息子项管理/添加")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ReleaseInfoItemDto releaseInfoItemDto) {
        mReleaseInfoItemService.insert(releaseInfoItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "根据id查询权限Key", notes = "根据id查询权限Key")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息子项管理/查询")
    @GetMapping("getResourceKeysById")
    public ResponseBo getResourceKeysById(@RequestParam Long id) {
        return new ResponseBo<>(mReleaseInfoItemService.getResourceKeysById(id));

    }

    @ApiOperation(value = "批量删除数据", notes = "批量删除数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.DELETE, description = "中台中心/发版信息子项管理/删除")
    @PostMapping("delete")
    public ResponseBo delete(@RequestBody List<Long> ids) {
        mReleaseInfoItemService.deleteBatch(ids);
        return ResponseBo.ok();
    }

}

