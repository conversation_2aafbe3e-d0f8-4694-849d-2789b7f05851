package get.middlecenter.controller;

import get.middlecenter.common.Result;
import get.middlecenter.common.ResultUtils;
import get.middlecenter.service.DemoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/api")
public class DemoController {

    @Resource
    private DemoService demoService;;
    @PostMapping("/demo")
    public Result<String> demo(@RequestHeader("X-Nonce") String nonce) {
        String resault = demoService.demo(nonce);
        return ResultUtils.success(resault);
    }

    @PostMapping("/interface1")
    public Result<String> interface1() {
        return ResultUtils.success("转账成功");
    }
}