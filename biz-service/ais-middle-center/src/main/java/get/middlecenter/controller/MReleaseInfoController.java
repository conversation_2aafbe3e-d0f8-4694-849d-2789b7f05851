package get.middlecenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.aismiddle.dto.ReleaseInfoAndItemDto;
import com.get.aismiddle.dto.ReleaseInfoSearchDto;
import com.get.aismiddle.vo.ReleaseInfoAndItemVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import get.middlecenter.service.MReleaseInfoService;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发版信息
 */
@RestController
@RequestMapping("middle/releaseInfo")
public class MReleaseInfoController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private MReleaseInfoService mReleaseInfoService;

    @ApiOperation(value = "平台类型下拉", notes = "平台类型下拉")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/平台类型下拉")
    @GetMapping("getPlatformTypeDropDown")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getPlatformTypeDropDown() {
        return new ListResponseBo<>(mReleaseInfoService.getPlatformTypeDropDown());
    }


    @ApiOperation(value = "分页查询所有数据", notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/查询")
    @PostMapping("list")
    public ResponseBo<ReleaseInfoAndItemVo> selectAll(@RequestBody SearchBean<ReleaseInfoSearchDto> page) {
        List<ReleaseInfoAndItemVo> datas = mReleaseInfoService.getReleaseInfoAndItem(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "根据id查询详细数据", notes = "根据id查询详细数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ReleaseInfoAndItemVo> getDetailedInformationById(@RequestBody Long id) {
        return new ResponseBo<>(mReleaseInfoService.getDetailedInformationById(id));
    }

    @ApiOperation(value = "修改数据", notes = "修改数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = "中台中心/发版信息项管理/修改")
    @PostMapping("edit")
    public ResponseBo edit(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.editReleaseInfoAndItem(releaseInfoAndItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "新增数据", notes = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = "中台中心/发版信息项管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {
       mReleaseInfoService.addReleaseInfoAndItem(releaseInfoAndItemDto);
       return ResponseBo.ok();
    }

    @ApiOperation(value = "删除数据", notes = "删除数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.DELETE, description = "中台中心/发版信息项管理/删除")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam Long id) {
        mReleaseInfoService.deleteReleaseInfoAndItem(id);
        return ResponseBo.ok();
    }


}

