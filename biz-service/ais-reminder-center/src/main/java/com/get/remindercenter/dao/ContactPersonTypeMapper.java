package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.entity.RemindContactPersonType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ContactPersonTypeMapper extends BaseMapper<RemindContactPersonType> {


    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insert(RemindContactPersonType record);

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(RemindContactPersonType record);

    /**
     * @return java.lang.Integer
     * @Description :
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}