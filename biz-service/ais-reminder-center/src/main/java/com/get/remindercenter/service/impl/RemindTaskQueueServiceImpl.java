package com.get.remindercenter.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.*;
import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.dto.MailDto;
import com.get.remindercenter.dto.SmsDto;
import com.get.remindercenter.entity.*;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.SendMessageUtils;
import com.get.remindercenter.utils.SmsUtils;
import com.get.remindercenter.vo.RemindTaskQueueVo;
import com.get.remindercenter.vo.StaffContractRemindVo;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 12:35
 * Date: 2021/11/15
 * Description:提醒任务执行队列管理业务实现类
 */
@Service
@Slf4j
public class RemindTaskQueueServiceImpl extends BaseServiceImpl<EmailSenderQueueMapper, EmailSenderQueue > implements RemindTaskQueueService {

    @Resource
    private UtilService utilService;
    @Resource
    private RemindTaskQueueMapper remindTaskQueueMapper;
    @Resource
    private RemindTaskMapper remindTaskMapper;
    @Resource
    private EmailTestSendMapper emailTestSendMapper;
    @Resource
    private RemindTemplateMapper remindTemplateMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private SendMessageUtils sendMessageUtils;
    @Resource
    private SystemEmailAccountMapper systemEmailAccountMapper;
    @Resource
    private InvalidEmailMapper invalidEmailMapper;
    @Resource
    private BatchSendingEmailMapper batchSendingEmailMapper;
    @Resource
    private UnsubscribeEmailMapper unsubscribeEmailMapper;
    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;
    @Resource
    private GetRedis getRedis;
    @Value("${spring.mail.financeMail}")
    private String defaultFinanceEmail;
    //是否禁止发送邮件 true 发送  false禁止发送
    @Value("${emailSwitch}")
    private boolean emailSwitch;
    @Value("${spring.mail.username}")
    private String from;
    @Resource
    private JavaMailSender javaMailSender;

    @Resource
    private IRocKetMqCenterClient iRocKetMqCenterClient;

    /**
     * @Description: 新增队列任务
     * @Author: Jerry
     * @Date:12:36 2021/11/15
     */
    @Override
    public void add(RemindTaskQueue remindTaskQueue) {
        utilService.updateUserInfoToEntity(remindTaskQueue);
        remindTaskQueueMapper.insert(remindTaskQueue);
    }


    /**
     * @Description: 根据提醒任务id删除队列任务
     * @Author: Jerry
     * @Date:12:37 2021/11/15
     */
    @Override
    public void delete(Long fkRemindTaskId, Date optTime) {
        LambdaQueryWrapper<RemindTaskQueue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RemindTaskQueue::getFkRemindTaskId, fkRemindTaskId);
        if (GeneralTool.isNotEmpty(optTime)) {
            lambdaQueryWrapper.le(RemindTaskQueue::getOptTime, optTime);
        }
        remindTaskQueueMapper.delete(lambdaQueryWrapper);
    }

    /**
     * @Description: 根据主键id删除队列消息
     * @Author: Jerry
     * @Date:15:48 2021/12/2
     */
    @Override
    public void deleteById(Long id) {
        remindTaskQueueMapper.deleteById(id);
    }


    /**
     * @Description: 根据提醒任务id和提醒方式删除队列任务
     * @Author: Jerry
     * @Date:17:24 2021/11/25
     */
    @Override
    public void deleteByRemindMethod(Long fkRemindTaskId, Date optTime, String remindMethod) {
//        Example example = new Example(RemindTaskQueue.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkRemindTaskId",fkRemindTaskId).andEqualTo("remindMethod",remindMethod);
//        if(GeneralTool.isNotEmpty(optTime)){
//            criteria.andLessThanOrEqualTo("optTime",optTime);
//        }

        LambdaQueryWrapper<RemindTaskQueue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RemindTaskQueue::getFkRemindTaskId, fkRemindTaskId);
        lambdaQueryWrapper.eq(RemindTaskQueue::getRemindMethod, remindMethod);
        if (GeneralTool.isNotEmpty(optTime)) {
            lambdaQueryWrapper.le(RemindTaskQueue::getOptTime, optTime);
        }
        remindTaskQueueMapper.delete(lambdaQueryWrapper);
    }

    /**
     * @Description: 批量删除
     * @Author: Jerry
     * @Date:15:39 2021/11/25
     */
    @Override
    public void batchDelete(Set<Long> fkRemindTaskIds, Date optTime) {
//        Example example = new Example(RemindTaskQueue.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkRemindTaskId",fkRemindTaskIds);
//        if(GeneralTool.isNotEmpty(optTime)){
//            criteria.andLessThanOrEqualTo("optTime",optTime);
//        }
        LambdaQueryWrapper<RemindTaskQueue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(RemindTaskQueue::getFkRemindTaskId, fkRemindTaskIds);
        if (GeneralTool.isNotEmpty(optTime)) {
            lambdaQueryWrapper.le(RemindTaskQueue::getOptTime, optTime);
        }
        remindTaskQueueMapper.delete(lambdaQueryWrapper);
    }

    /**
     * @Description: 获取需要执行的任务
     * @Author: Jerry
     * @Date:14:16 2021/11/15
     */
    @Override
    public List<RemindTaskQueue> getRemindTaskQueues() {
        //获取当前时间大于执行任务时间的数据
        Date nowDate = new Date();
        List<RemindTaskQueue> remindTaskQueues = remindTaskQueueMapper.getRemindTaskQueues(nowDate);
        return GeneralTool.isEmpty(remindTaskQueues) ? new ArrayList<>() : remindTaskQueues;
    }

    /**
     * @Description: 获取系统内消息
     * @Author: Jerry
     * @Date:15:36 2021/12/2
     */
    @Override
    public List<RemindTaskQueueVo> getSystemMessage() {
        //获取当前时间大于执行任务时间的数据
        Date nowDate = new Date();
        //获取当前登陆人
        Long fkStaffId = SecureUtil.getStaffId();
        List<RemindTaskQueueVo> systemMessage = remindTaskQueueMapper.getSystemMessage(nowDate, fkStaffId);
        if (GeneralTool.isEmpty(systemMessage)) {
            return Collections.emptyList();
        }
        List<Long> tableIds = systemMessage.stream().map(RemindTaskQueueVo::getFkTableId).collect(Collectors.toList());
        Result<List<StudentOfferItemVo>> result = saleCenterClient.getStudentOfferItemByStudentOfferItemStepId(tableIds);
        List<StudentOfferItemVo> studentOfferItemVos = new ArrayList<>();
        if (result.isSuccess()) {
            studentOfferItemVos = result.getData();
        }
        if (GeneralTool.isNotEmpty(studentOfferItemVos)) {
            Map<Long, List<StudentOfferItemVo>> listMap = studentOfferItemVos.stream().collect(Collectors.groupingBy(StudentOfferItemVo::getId));
            for (RemindTaskQueueVo remindTaskQueueDto : systemMessage) {
                if (TableEnum.SALE_STUDENT_OFFER_ITEM_STEP.key.equals(remindTaskQueueDto.getFkTableName())) {
                    //申请步骤的提醒
                    studentOfferItemVos = listMap.get(remindTaskQueueDto.getFkTableId());
                    if (GeneralTool.isNotEmpty(studentOfferItemVos)) {
                        remindTaskQueueDto.setOfferItemId(studentOfferItemVos.get(0).getId());
                        remindTaskQueueDto.setOfferId(studentOfferItemVos.get(0).getFkStudentOfferId());
                        remindTaskQueueDto.setStudentId(studentOfferItemVos.get(0).getFkStudentId());
                    }
                }
            }
        }
        return systemMessage;
    }


    /**
     * @Description: 线程执行任务
     * @Author: Jerry
     * @Date:16:28 2021/11/15
     */
    @Override
//    @Async("remindTaskQueueExecutor")
    public void  performTasks(Long remindTaskId, RemindTaskQueue remindTaskQueue) {
        String remindMethod = remindTaskQueue.getRemindMethod();
        String msg = null;
        try {
            log.info("执行任务开始----------------执行任务id：" + remindTaskId);
            RemindTask remindTask = remindTaskMapper.selectById(remindTaskId);
            if (GeneralTool.isEmpty(remindTask)) {
                log.error("任务不存在");
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_does_not_exist"));
            }
            if (GeneralTool.isEmpty(remindTask.getStatus()) || !remindTask.getStatus().equals(1)) {
                log.error("任务未开启");
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_not_open"));
            }
            //任务标题
            String taskTitle = remindTask.getTaskTitle();
            //任务备注
            String taskRemark = remindTask.getTaskRemark();
            //任务链接
            String taskLink = remindTask.getTaskLink();
            //任务开始时间
            Date startTime = remindTask.getStartTime();
            //任务结束时间
            Date endTime = remindTask.getEndTime();
            //模板格式
            String enName = remindTask.getLanguageCode();
            //请假理由
            String leaveReason = null;
            //请假时间
            String leaveDays = null;
            //请假类型
            String leaveType = null;
            //部门名称
            String departmentName = null;
            //办公室名称
            String officeName = null;
            String fkRemindEventTypeKey = remindTask.getFkRemindEventTypeKey();
            if (TableEnum.ACT_HI_TASKINST.key.equals(remindTask.getFkTableName()) && "WORKFLOW_LEAVE_FORM".equals(fkRemindEventTypeKey)) {
                //工作流的内容
                Result<ActHiTaskInstVo> result = workflowCenterClient.getActHiTaskInstDtoAndLeaveFormMessage(remindTask.getFkTableId());
                log.error("发送邮件工作流调用返回信息：{}", result.getMessage());
                if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                    log.error("执行任务id={}，返回的工作流为空。", remindTaskId);
                } else {
                    log.info("执行任务id={}，返回的工作流详情：{}", remindTaskId, GeneralTool.toJson(result.getData()));
                }
                ActHiTaskInstVo actHiTaskInstVo = result.getData();
                if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(actHiTaskInstVo.getWorkFlowType())) {
                    leaveReason = actHiTaskInstVo.getLeaveReason();
                    leaveDays = actHiTaskInstVo.getLeaveDays();
                    leaveType = actHiTaskInstVo.getLeaveType();
                    departmentName = actHiTaskInstVo.getDepartmentName();
                    if (GeneralTool.isEmpty(actHiTaskInstVo.getOfficeName())) {
                        officeName = "";
                    } else {
                        officeName = actHiTaskInstVo.getOfficeName();
                    }
                    startTime = actHiTaskInstVo.getStartDay();
                    endTime = actHiTaskInstVo.getEndDay();
                }
            }

            if (GeneralTool.isEmpty(remindTask.getFkStaffId())) {
                log.error("任务接收人为空");
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_receiver_is_empty"));
            }
            //人员信息
            StaffVo staffVo = null;
            Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(remindTask.getFkStaffId());
            if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                staffVo = staffDtoResult.getData();
            }
            if (GeneralTool.isEmpty(staffVo)) {
                //发送代理
                if (ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG.key.equals(fkRemindEventTypeKey)) {
                    //发送代理联系人
                    log.info("发送代理联系人");
                    //Map<String, String> contactPersonEmailMap = saleCenterClient.getContactPersonEmailMap(remindTask.getFkTableId());
                    //发送人
                    StudentOfferItemSendEmailVo contactPersonEmailStaff = saleCenterClient.getContactPersonEmailStaff(remindTask.getFkTableId());
                    //发送代理联系人
                    sendEmailToContactPerson(contactPersonEmailStaff, remindTask, remindTaskQueue);
                    return;
                } else {
                    log.error("人员信息为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("staff_is_null"));
                }
            }
            String userName = staffVo.getName();
            if (ProjectExtraEnum.REMIND_METHOD_NONE.key.equals(Integer.valueOf(remindMethod))) {
                //暂不处理
                //删除该任务及以前的过期任务
                deleteByRemindMethod(remindTaskId, remindTaskQueue.getOptTime(), remindMethod);
            } else if (ProjectExtraEnum.REMIND_METHOD_EMAIL.key.equals(Integer.valueOf(remindMethod))) {
                msg = "邮箱";
                if (GeneralTool.isEmpty(staffVo.getEmail())) {
                    log.error("人员邮箱信息为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_is_empty"));
                }
                //邮箱模板
//                Example example = new Example(RemindTemplate.class);
//                example.createCriteria().andEqualTo("fkRemindEventTypeKey",fkRemindEventTypeKey);
                List<RemindTemplate> remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, fkRemindEventTypeKey));
                if (GeneralTool.isEmpty(remindTemplates)) {
                    log.error("邮箱模板不存在，需要配置邮箱模板");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
                }
                String emailTemplate = null;
                if(GeneralTool.isNotEmpty(enName)&&enName.equals("en")){
                    emailTemplate =  remindTemplates.get(0).getEmailTemplateEn();
                }else {
                    emailTemplate =  remindTemplates.get(0).getEmailTemplate();
                }


                emailTemplate = doReplaceTemplate(remindTask, emailTemplate);
                if (GeneralTool.isEmpty(emailTemplate)) {
                    log.error("邮箱模板内容为空，需要配置邮箱模板");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
                }
                //替换邮箱字符内容
                emailTemplate = emailTemplate.replace("#{staffName}", staffVo.getName());
                if (GeneralTool.isNotEmpty(taskLink)) {
                    emailTemplate = emailTemplate.replace("#{taskLink}", taskLink);
                } else {
                    emailTemplate = emailTemplate.replace("#{taskLink}", "");
                }
                if (GeneralTool.isNotEmpty(taskRemark)) {
                    emailTemplate = emailTemplate.replace("#{taskRemark}", taskRemark);
                } else {
                    emailTemplate = emailTemplate.replace("#{taskRemark}", "");
                }
                if (GeneralTool.isNotEmpty(leaveReason)) {
                    emailTemplate = emailTemplate.replace("#{leaveReason}", leaveReason);
                } else {
                    emailTemplate = emailTemplate.replace("#{leaveReason}", "");
                }
                if (GeneralTool.isNotEmpty(leaveDays)) {
                    emailTemplate = emailTemplate.replace("#{leaveDays}", leaveDays);
                }
                if (GeneralTool.isNotEmpty(leaveType)) {
                    emailTemplate = emailTemplate.replace("#{leaveType}", leaveType);
                }
                if (GeneralTool.isNotEmpty(departmentName)) {
                    emailTemplate = emailTemplate.replace("#{departmentName}", departmentName);
                }
                if (GeneralTool.isNotEmpty(officeName)) {
                    emailTemplate = emailTemplate.replace("#{officeName}", officeName);
                } else {
                    emailTemplate = emailTemplate.replace("#{officeName}", "");
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                emailTemplate = emailTemplate.replace("#{startTime}", sdf.format(startTime));
                //学习计划特殊处理
                if (ProjectKeyEnum.STUDENT_ACCEP_OFFER_EMAIL_REMIND_KEY.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.STUDENT_PAY_DEPOSIT_REMIND_KEY.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.STUDENT_ACCEP_OFFER_EMAIL_REMIND_EDG_KEY.key.equals(fkRemindEventTypeKey)
                        || ProjectKeyEnum.STUDENT_PAY_DEPOSIT_REMIND_EDG_KEY.key.equals(fkRemindEventTypeKey)) {
                    //如果是最后一次发送提醒，需要添加字符【今日截止】
                    if (remindTaskQueue.getOptTime().equals(remindTask.getEndTime())) {
                        if(enName.equals("en")){
                            emailTemplate = emailTemplate.replace("#{taskTitle}", "[Due Today]" + taskTitle);
                        }else {
                            emailTemplate = emailTemplate.replace("#{taskTitle}", "【今日截止】" + taskTitle);
                        }

                    } else {
                        emailTemplate = emailTemplate.replace("#{taskTitle}", taskTitle);
                    }
                    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isNotEmpty(endTime)) {
                        emailTemplate = emailTemplate.replace("#{endTime}", sdf1.format(endTime));
                    }
                } else {
                    if (endTime == null) {
                        emailTemplate = emailTemplate.replace("<span class=\"end\">结束时间：#{endTime}</span>", "");
                    } else {
                        emailTemplate = emailTemplate.replace("#{endTime}", sdf.format(endTime));
                    }
                    emailTemplate = emailTemplate.replace("#{taskTitle}", taskTitle);
                }

                if (ProjectKeyEnum.WORKFLOW_STUDENT_OFFER.key.equals(fkRemindEventTypeKey)) {
                    emailTemplate = doGetStudentOfferTemplate(emailTemplate, remindTask.getFkTableId());
                }

                if (ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key.equals(fkRemindEventTypeKey)) {
                    emailTemplate = doGetStaffContractExpireTemplate(emailTemplate, remindTask.getFkTableId());
                }

                try {
                    //随机抽取1到5秒进行阻塞，防止同一秒内多次发送
                    int num = (int) (Math.random() * 5 + 1);
                    Thread.sleep(1000L * num);
                } catch (Exception e) {
                    log.error("报错发送内容:" + emailTemplate);
                }
                log.info("发送内容:" + emailTemplate);

                if (getRedis.setNx(CacheKeyConstants.SEND_EMAIL_LOCK_KEY + remindTaskId, 1, 60L)) {
                    //发送邮件
//                    if (fkRemindEventTypeKey.equals(ProjectKeyEnum.DEFER_ENTRANCE_EMAIL_REMIND_KEY.key)) {
//                        String financeEmail;
//                        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_FINANCE_EMAIL.key).getData();
//                        if (GeneralTool.isNotEmpty(configDto)&&GeneralTool.isNotEmpty(configDto.getValue1())){
//                            JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//                            String geaFinanceEmail = jsonObject.getString("GEA");
//                            String iaeFinanceEmail = jsonObject.getString("IAE");
//                            if (staffVo.getFkCompanyId().equals(3L)){
//                                financeEmail = iaeFinanceEmail;
//                            }else {
//                                financeEmail = geaFinanceEmail;
//                            }
//                            if (GeneralTool.isNotEmpty(financeEmail)) {
//                                //延迟入学提醒
//                                sendMessageUtils.sendMail(taskTitle, emailTemplate, financeEmail);
//                            }
//                        }else {
//                            sendMessageUtils.sendMail(taskTitle, emailTemplate, defaultFinanceEmail);
//                        }
//                    }else
                    if (ProjectKeyEnum.STUDENT_ACCEP_OFFER_EMAIL_REMIND_KEY.key.equals(fkRemindEventTypeKey)
                            || ProjectKeyEnum.STUDENT_PAY_DEPOSIT_REMIND_KEY.key.equals(fkRemindEventTypeKey)
                            || ProjectKeyEnum.STUDENT_ACCEP_OFFER_EMAIL_REMIND_EDG_KEY.key.equals(fkRemindEventTypeKey)
                            || ProjectKeyEnum.STUDENT_PAY_DEPOSIT_REMIND_EDG_KEY.key.equals(fkRemindEventTypeKey)) {
                        //使用自己邮箱发送给自己
                        sendOfferItemRemind(staffVo, taskTitle, emailTemplate, remindTask, remindTaskQueue);
                    } else if (ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key.equals(fkRemindEventTypeKey)) {
                        Boolean isOnDuty = remindTaskQueueMapper.getStaffContractIsOnDuty(remindTask.getFkTableId());
                        if (GeneralTool.isNotEmpty(isOnDuty) && isOnDuty) {
                            List<String> emails = Lists.newArrayList();
                            Long companyId = remindTaskQueueMapper.getStaffContractCompanyId(remindTask.getFkTableId());
                            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key, 2).getData();
                            String configValue2 = companyConfigMap.get(companyId);
                            emails = new ArrayList<>(JSON.parseArray(configValue2, String.class));

//                            ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key).getData();
//                            if (GeneralTool.isNotEmpty(configDto)){
//                                String value2 = configDto.getValue2();
//                                JSONObject jsonObject = JSON.parseObject(value2);
//                                if (companyId == 2L){
//                                    JSONArray gea = jsonObject.getJSONArray("GEA");
//                                    emails = gea.toJavaList(String.class);
//                                } else if (companyId == 3L){
//                                    JSONArray iae = jsonObject.getJSONArray("IAE");
//                                    emails = iae.toJavaList(String.class);
//                                } else {
//                                    JSONArray other = jsonObject.getJSONArray("OTHER");
//                                    emails = other.toJavaList(String.class);
//                                }
//                            }
                            if (GeneralTool.isNotEmpty(emails)) {
                                for (String email : emails) {
                                    Thread.sleep(1000L * 3);
                                    sendMessageUtils.sendMail(taskTitle, emailTemplate, email);
                                }
                            }
                        }
                    } else {
                        sendMessageUtils.sendMail(taskTitle, emailTemplate, staffVo.getEmail());
                    }

                    log.info("发送邮件成功----------------任务id：" + remindTaskId);
                    log.info("发送邮件成功----------------队列id：" + remindTaskQueue.getId());

                    //删除该任务及以前的过期任务
                    deleteByRemindMethod(remindTaskId, remindTaskQueue.getOptTime(), remindMethod);
                } else {
                    log.error("发送邮件失败，发送频繁----------------任务id：" + remindTaskId);
                }
            } else if (ProjectExtraEnum.REMIND_METHOD_MESSAGE.key.equals(Integer.valueOf(remindMethod))) {
                msg = "短信";
                //手机号码
                String mobile = staffVo.getMobile();
                if (GeneralTool.isEmpty(mobile)) {
                    log.error("人员手机号码为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_empty"));
                }
                //手机区号
                String mobileAreaCode = staffVo.getMobileAreaCode();
                if (GeneralTool.isEmpty(mobileAreaCode)) {
                    log.error("人员手机区号为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("phone_area_code_is_empty"));
                }
                //短信模板参数
                Map<String, Object> maps = new HashMap<>(16);
                maps.put("user_name", userName);
                maps.put("task_title", taskTitle);
                maps.put("task_remark", taskRemark);
                String areaCode852 = "852";
                //默认国内短信
                String tplId = "240203";
                //国际短信
                if (areaCode852.equals(mobileAreaCode)) {
                    tplId = "12120";
                }
                try {
                    //随机抽取1到5秒进行阻塞，防止同一秒内多次发送
                    int num = (int) (Math.random() * 5 + 1);
                    Thread.sleep(1000L * num);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                String result = SmsUtils.sendSms(mobile, mobileAreaCode, tplId, maps);
                if (GeneralTool.isEmpty(result)) {
                    log.error("发送短信失败，返回结果为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("result_null"));
                }
                JSONObject resultObject = JSONObject.parseObject(result);
                if (!"0".equals(resultObject.getString("error_code"))) {
                    log.error("发送短信失败，异常信息为：" + resultObject.getString("reason"));
                    throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send") + resultObject.getString("reason"));
                }
                log.info("发送短信成功----------------任务id：" + remindTaskId);
                //删除该任务及以前的过期任务
                deleteByRemindMethod(remindTaskId, remindTaskQueue.getOptTime(), remindMethod);
            }
        } catch (Exception e) {
            log.error("发送" + msg + "异常：", e);
            //记录错误日志，并且错误次数+1
            Integer tryTimes = GeneralTool.isEmpty(remindTaskQueue.getTryTimes()) ? 0 : remindTaskQueue.getTryTimes();
            ++tryTimes;
            remindTaskQueue.setTryTimes(tryTimes);
            remindTaskQueue.setTryError(e.getMessage());
            remindTaskQueue.setGmtModified(new Date());
            remindTaskQueue.setGmtModifiedUser(remindTaskQueue.getGmtCreateUser());
            remindTaskQueueMapper.updateByPrimaryKey(remindTaskQueue);
        }
    }

    private String doGetStaffContractExpireTemplate(String emailTemplate, Long fkTableId) throws IllegalAccessException {
        StaffContractRemindVo staffContractRemindDto = remindTaskMapper.getStaffContractRemindDto(fkTableId);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        staffContractRemindDto.setTitle("【" + staffContractRemindDto.getStaffFullName() + "】员工到期合同提醒，合同到期日：" + simpleDateFormat.format(staffContractRemindDto.getContractEndDate()));

        Class<? extends StaffContractRemindVo> clazz = staffContractRemindDto.getClass();
        Map<String, Object> map = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);  // 设置允许访问私有属性
            String fieldName = field.getName();
            Object fieldValue = field.get(staffContractRemindDto);
            if ("class java.util.Date".equals(field.getGenericType().toString())) {
                String format = simpleDateFormat.format(ReflectUtil.getFieldValue(staffContractRemindDto, field));
                map.put(field.getName(), format);
            } else {
                map.put(fieldName, fieldValue);
            }
        }
        for (String key : map.keySet()) {
            emailTemplate = emailTemplate.replace("#{" + key + "}", GeneralTool.isNotEmpty(map.get(key)) ? map.get(key).toString() : "");
        }
        return emailTemplate;
    }

    private void sendOfferItemRemind(StaffVo staffVo, String taskTitle, String emailTemplate, RemindTask remindTask, RemindTaskQueue remindTaskQueue) {
        //是否禁止发送邮件 true 发送  false禁止发送
        if (!emailSwitch) {
            return;
        }
        //true 项目成员名义发送   false  系统默认邮箱发送
        Boolean isSenderAdmin = true;

        //查询发件人邮箱
        StudentOfferItemSendEmailVo contactPersonEmailStaffDto = saleCenterClient.getContactPersonEmailStaff(remindTask.getFkTableId());

        //步骤不符则不发送邮件
        //支付押金截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        //接受Offer截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        if ((remindTask.getFkRemindEventTypeKey().equals("STUDENT_ACCEP_OFFER")
                || remindTask.getFkRemindEventTypeKey().equals("STUDENT_ACCEP_OFFER_ENG"))
                && (!contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(14L)
//                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(5L)
        )) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        } else if ((remindTask.getFkRemindEventTypeKey().equals("STUDENT_PAY_DEPOSIT")
                || remindTask.getFkRemindEventTypeKey().equals("STUDENT_PAY_DEPOSIT_ENG"))
                && (!contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(14L)
        )) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        }
        //步骤不符则不发送邮件
        //达到过Offer Accepted则不发
        if (contactPersonEmailStaffDto.getIsStepOfferAccepted() || contactPersonEmailStaffDto.getIsStudentArrivedOs()) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        }

        //检验员工邮箱信息
        if (GeneralTool.isEmpty(contactPersonEmailStaffDto)) {
            //密码为空，需要填写密码
            StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaffDto.getStudentOfferItemStaffEmailDto();
            if (GeneralTool.isEmpty(studentOfferItemStaffEmailDto) || GeneralTool.isEmpty(studentOfferItemStaffEmailDto.getUserEmail()) || GeneralTool.isEmpty(studentOfferItemStaffEmailDto.getPassword())) {
                log.info("密码为空，需要填写密码,员工" + staffVo.getName());
                isSenderAdmin = true;
            }
        }
        if (isSenderAdmin) {
            sendMessageUtils.sendMail(taskTitle, emailTemplate, staffVo.getEmail());
            log.info("发送内部邮件(系统默认发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId());
        } else {
            try {
                StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaffDto.getStudentOfferItemStaffEmailDto();
                String password = AESUtils.Decrypt(studentOfferItemStaffEmailDto.getPassword(), AESConstant.AESKEY);
                if (studentOfferItemStaffEmailDto.getUserEmail().contains("@qq.com") || studentOfferItemStaffEmailDto.getUserEmail().contains("@ht-international.net")) {
                    // 邮件相关配置
                    String host = "smtp.exmail.qq.com";
                    int port = 465;
                    String username = studentOfferItemStaffEmailDto.getUserEmail();

                    // 邮件内容
                    String from = staffVo.getEmail();
                    String to = staffVo.getEmail();
                    String subject = taskTitle;
                    String body = emailTemplate;

                    // 配置邮件服务器
                    Properties props = new Properties();
                    props.put("mail.smtp.host", host);
                    props.put("mail.smtp.port", port);
                    props.put("mail.smtp.auth", "true");
                    props.put("mail.smtp.ssl.enable", "true");

                    // 创建会话
                    Session session = Session.getInstance(props);

                    try {
                        // 创建邮件
                        Message message = new MimeMessage(session);
                        message.setFrom(new InternetAddress(from));
                        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
                        message.setSubject(subject);
//                            message.setText(body);
                        message.setContent(body, "text/html; charset=utf-8");

                        // 发送邮件
                        Transport transport = session.getTransport("smtp");
                        transport.connect(host, username, password);
                        transport.sendMessage(message, message.getAllRecipients());
                        transport.close();
                        log.info("发送邮件成功(项目成员内部发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + staffVo.getEmail());
                        log.info("发送邮件成功(项目成员内部发送)----------------队列id：" + remindTaskQueue.getId());
                    } catch (MessagingException e) {
                        log.info("发送邮件失败(项目成员内部发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId());
                        isSenderAdmin = true;
                    }
                } else {
                    MailDto mailVo = new MailDto();
                    mailVo.setDefaultEncoding("utf-8");
                    if (GeneralTool.isNotEmpty(studentOfferItemStaffEmailDto.getUserEmail())) {
                        if (studentOfferItemStaffEmailDto.getUserEmail().contains("@geaworld.org")) {
                            mailVo.setHost("smtp.qiye.aliyun.com");
                        } else if (studentOfferItemStaffEmailDto.getUserEmail().contains("@geteducation.org")) {
                            mailVo.setHost("smtp.exmail.qq.com");
                        }
                    } else {
                        mailVo.setHost("smtp.qiye.aliyun.com");
                    }
                    mailVo.setPort(465);
                    mailVo.setProtocol("smtps");
                    mailVo.setUserName(studentOfferItemStaffEmailDto.getUserEmail());
                    if (GeneralTool.isNotEmpty(password)) {
                        mailVo.setPassword(password);
                    }
                    mailVo.setTitle(taskTitle);
                    mailVo.setToEmail(staffVo.getEmail());
                    mailVo.setTemplate(emailTemplate);
                    mailVo.setFlag(true);
                    customSendMailByMailVo(mailVo);
                    log.info("发送邮件成功(项目成员内部发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + staffVo.getEmail());
                    log.info("发送邮件成功(项目成员内部发送)----------------队列id：" + remindTaskQueue.getId());
                }
            } catch (Exception e) {
                isSenderAdmin = true;
            } finally {
                if (isSenderAdmin) {
                    sendMessageUtils.sendMail(taskTitle, emailTemplate, staffVo.getEmail());
                    log.info("发送内部邮件(系统默认发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + staffVo.getEmail());

                }
            }
        }

    }


    private void sendEmailToContactPerson(StudentOfferItemSendEmailVo contactPersonEmailStaff, RemindTask remindTask, RemindTaskQueue remindTaskQueue) {
        //是否禁止发送邮件 true 发送  false禁止发送
        if (!emailSwitch) {
            return;
        }


        //步骤不符则不发送邮件
        //支付押金截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        //接受Offer截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        if ((remindTask.getFkRemindEventTypeKey().equals("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE")
                || remindTask.getFkRemindEventTypeKey().equals("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG"))
                && (!contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(14L)
//                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(5L)
        )) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        } else if ((remindTask.getFkRemindEventTypeKey().equals("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE")
                || remindTask.getFkRemindEventTypeKey().equals("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG"))
                && (!contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(14L)
        )) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        }
        //步骤不符则不发送邮件
        //达到过Offer Accepted则不发
        if (contactPersonEmailStaff.getIsStepOfferAccepted() || contactPersonEmailStaff.getIsStudentArrivedOs()) {
            remindTaskQueueMapper.deleteById(remindTaskQueue.getId());
            if (remindTask.getEndTime().equals(remindTaskQueue.getOptTime())) {
                remindTask.setStatus(0);
                remindTaskMapper.updateById(remindTask);
            }
            return;
        }

        //模板设置
        String emailTemplate = setEmailTemplate(remindTask, remindTaskQueue);

        //是否拒收
        if (!contactPersonEmailStaff.getIsRejectEmail()) {

            //true 项目成员名义发送   false  系统默认邮箱发送
            Boolean isSenderAdmin = false;

            //校验发送人和接收人邮箱地址
            if (GeneralTool.isNotEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto())) {
                if (GeneralTool.isEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto().getUserEmail())) {
                    sendEmailErrorFromAdmin(1, remindTask, remindTask.getTaskRemark(), null, remindTaskQueue, contactPersonEmailStaff, null);
                    isSenderAdmin = true;
                } else if (GeneralTool.isEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto().getPassword())) {
                    sendEmailErrorFromAdmin(2, remindTask, remindTask.getTaskRemark(), null, remindTaskQueue, contactPersonEmailStaff, null);
                    isSenderAdmin = true;
                }
            }

            if (!isSenderAdmin) {
                StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaff.getStudentOfferItemStaffEmailDto();
                String toAgentEmail = "";
                List<String> ccAgentEmailList = new ArrayList<>();

                for (int i = 0; i < contactPersonEmailStaff.getContactPersonEmailDtos().size(); i++) {
                    StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto = contactPersonEmailStaff.getContactPersonEmailDtos().get(i);
                    if (0 == i) {
                        toAgentEmail = contactPersonEmailDto.getAgentEmail();
                    } else {
                        ccAgentEmailList.add(contactPersonEmailDto.getAgentEmail());
                    }
                }

//                for (StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto : contactPersonEmailStaff.getContactPersonEmailDtos()) {
                if (studentOfferItemStaffEmailDto.getUserEmail().contains("@qq.com") || studentOfferItemStaffEmailDto.getUserEmail().contains("@ht-international.net")) {
                    // 邮件相关配置
                    String host = "smtp.exmail.qq.com";
                    int port = 465;
                    String username = studentOfferItemStaffEmailDto.getUserEmail();
                    String password = studentOfferItemStaffEmailDto.getPassword();
                    try {
                        password = AESUtils.Decrypt(password, AESConstant.AESKEY);
                    } catch (Exception e) {
//                        log.error("邮箱密码解析失败！studentOfferItemId::id=" + remindTask.getFkTableId());
//                        sendEmailErrorFromAdmin(2, remindTask, remindTask.getTaskRemark(), null, remindTaskQueue, contactPersonEmailStaff, toAgentEmail);
//                        //发送失败，使用系统默认邮箱再发送一次
//                        sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
//                        log.info("系统默认邮箱发送邮件成功----------------任务id：" + remindTaskQueue.getFkRemindTaskId());
//                        log.info("系统默认邮箱发送邮件成功----------------队列id：" + remindTaskQueue.getId());
//                        //删除该任务及以前的过期任务
//                        deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
//                        //return;
                        throw  new GetServiceException("邮箱密码解析失败！studentOfferItemId::id=" + remindTask.getFkTableId());
                    }

                    // 邮件内容
                    String from = studentOfferItemStaffEmailDto.getUserEmail();
                    String to = toAgentEmail;
                    String subject = remindTask.getTaskTitle();
                    String body = emailTemplate;

                    // 配置邮件服务器
                    Properties props = new Properties();
                    props.put("mail.smtp.host", host);
                    props.put("mail.smtp.port", port);
                    props.put("mail.smtp.auth", "true");
                    props.put("mail.smtp.ssl.enable", "true");

                    // 创建会话
                    Session session = Session.getInstance(props);

                    try {
                        // 创建邮件
                        Message message = new MimeMessage(session);
                        message.setFrom(new InternetAddress(from));
                        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
                        if (GeneralTool.isNotEmpty(ccAgentEmailList)) {
                            for (String ccAddress : ccAgentEmailList) {
                                message.addRecipients(Message.RecipientType.CC, InternetAddress.parse(ccAddress));
                            }
                        }
                        message.setSubject(subject);
//                            message.setText(body);
                        message.setContent(body, "text/html; charset=utf-8");

                        // 发送邮件
                        Transport transport = session.getTransport("smtp");
                        transport.connect(host, username, password);
                        transport.sendMessage(message, message.getAllRecipients());
                        transport.close();
                        log.info("发送邮件成功(项目成员发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + to);
                        log.info("发送邮件成功(项目成员发送)----------------队列id：" + remindTaskQueue.getId());
                        //删除该任务及以前的过期任务
                        deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                    } catch (MessagingException e) {
                            //记录
                            log.info("【项目成员名义发送邮件失败】"+e.getMessage());
                            sendEmailErrorFromAdmin(3, remindTask, remindTask.getTaskRemark(), e, remindTaskQueue,contactPersonEmailStaff,toAgentEmail);
                            //发送失败，使用系统默认邮箱再发送一次
                            sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
                            log.info("系统默认邮箱重发送邮件成功----------------任务id：" + remindTaskQueue.getFkRemindTaskId()+"发送至邮箱：" + to);
                            log.info("系统默认邮箱重发送邮件成功----------------队列id：" + remindTaskQueue.getId());
                            //删除该任务及以前的过期任务
                            deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                            log.info("项目成员名义发送邮件失败提示信息1----------------队列id：" + remindTaskQueue.getId());
                            throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send_email_on_behalf_of_project_member")+e.getMessage());


                    }
                } else
                {
                    MailDto mailVo = new MailDto();
                    mailVo.setDefaultEncoding("utf-8");
                    if (GeneralTool.isNotEmpty(studentOfferItemStaffEmailDto.getUserEmail())) {
                        if (studentOfferItemStaffEmailDto.getUserEmail().contains("@geaworld.org")) {
                            mailVo.setHost("smtp.qiye.aliyun.com");
                        } else if (studentOfferItemStaffEmailDto.getUserEmail().contains("@geteducation.org")) {
                            mailVo.setHost("smtp.exmail.qq.com");
                        }
                    } else {
                        mailVo.setHost("smtp.qiye.aliyun.com");
                    }
                    mailVo.setPort(465);
                    mailVo.setProtocol("smtps");
                    mailVo.setUserName(studentOfferItemStaffEmailDto.getUserEmail());
                    String password = studentOfferItemStaffEmailDto.getPassword();
                    String decrypt = "";
                    try {
                        decrypt = AESUtils.Decrypt(password, AESConstant.AESKEY);
                    } catch (Exception e) {
                        log.error("邮箱密码解析失败！studentOfferItemId::id=" + remindTask.getFkTableId());
                        sendEmailErrorFromAdmin(2, remindTask, remindTask.getTaskRemark(), null, remindTaskQueue, contactPersonEmailStaff, toAgentEmail);
                        //发送失败，使用系统默认邮箱再发送一次
                        sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
                        log.info("系统默认邮箱发送邮件成功----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + toAgentEmail);
                        log.info("系统默认邮箱发送邮件成功----------------队列id：" + remindTaskQueue.getId());
                        //删除该任务及以前的过期任务
                        deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                        //return;
                        throw new GetServiceException(LocaleMessageUtils.getMessage("email_password_resolution_failed")+"studentOfferItemId::id=" + remindTask.getFkTableId());
                    }
                    if (GeneralTool.isNotEmpty(decrypt)) {
                        mailVo.setPassword(decrypt);
                    }
                    mailVo.setTitle(remindTask.getTaskTitle());
//                    String toEmail = contactPersonEmailDto.getAgentEmail();
//                    if (GeneralTool.isNotEmpty(toEmail)){
//                        String[] toEmails = toEmail.split(";");
//                        mailVo.setToEmails(toEmails);
//                    }
                    mailVo.setToEmail(toAgentEmail);
                    if (GeneralTool.isNotEmpty(ccAgentEmailList)) {
                        mailVo.setCcEmails(ccAgentEmailList.toArray(new String[0]));
                    }
                    mailVo.setTemplate(emailTemplate);
                    mailVo.setFlag(true);
                    try {
                        customSendMailByMailVo(mailVo);
                        log.info("发送邮件成功(项目成员发送)----------------任务id：" + remindTaskQueue.getFkRemindTaskId());
                        log.info("发送邮件成功(项目成员发送)----------------队列id：" + remindTaskQueue.getId());
                        //删除该任务及以前的过期任务
                        deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                    } catch (Exception e) {
//                        //记录
                        log.info("【项目成员名义发送邮件失败】" + e.getMessage());
                        sendEmailErrorFromAdmin(3, remindTask, remindTask.getTaskRemark(), e, remindTaskQueue, contactPersonEmailStaff, toAgentEmail);
                        //发送失败，使用系统默认邮箱再发送一次
                        sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
                        log.info("系统默认邮箱重发送邮件成功----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + toAgentEmail);
                        log.info("系统默认邮箱重发送邮件成功----------------队列id：" + remindTaskQueue.getId());
                        //删除该任务及以前的过期任务
                        deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                        log.info("项目成员名义发送邮件失败提示信息2----------------队列id：" + remindTaskQueue.getId());
                        throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send_email_on_behalf_of_project_member") + e.getMessage());
                    }
                }
            } else {
                for (StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto : contactPersonEmailStaff.getContactPersonEmailDtos()) {
                    log.info("方案联系人邮箱:" + contactPersonEmailDto.getAgentEmail());
                    sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, contactPersonEmailDto.getAgentEmail());
                    log.info("发送邮件成功----------------任务id：" + remindTaskQueue.getFkRemindTaskId() + "发送至邮箱：" + contactPersonEmailDto.getAgentEmail());
                    log.info("发送邮件成功----------------队列id：" + remindTaskQueue.getId());
                    //删除该任务及以前的过期任务
                    deleteByRemindMethod(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue.getOptTime(), remindTaskQueue.getRemindMethod());
                }
            }
        } else {
            log.info("itemId:" + remindTask.getFkTableId() + "代理：" + contactPersonEmailStaff.getFkAgentName() + "拒收邮箱");
            //备注且设置任务重试次数超过3
            remindTaskQueue.setTryError("代理拒收");
            remindTaskQueue.setTryTimes(3);
            this.remindTaskQueueMapper.updateById(remindTaskQueue);
        }
    }

    private String setEmailTemplate(RemindTask remindTask, RemindTaskQueue remindTaskQueue) {
        List<RemindTemplate> remindTemplates = new ArrayList<>();
        if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.key)) {
            remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.key));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.key)) {
            remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.key));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG.key)) {
            remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.key));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG.key)) {
            remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.key));
        }
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!remindTask.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }


        emailTemplate = doReplaceTemplate(remindTask, emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.key)) {
            emailTemplate = emailTemplate.replace("#{endTime}", "支付押金截止时间：" + sdf.format(remindTask.getEndTime()));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.key)) {
            emailTemplate = emailTemplate.replace("#{endTime}", "接受Offer截止时间：" + sdf.format(remindTask.getEndTime()));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG.key)) {
            emailTemplate = emailTemplate.replace("#{endTime}", "Offer Acceptance Deadline：" + sdf.format(remindTask.getEndTime()));
        } else if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG.key)) {
            emailTemplate = emailTemplate.replace("#{endTime}", "Deposit Deadline：" + sdf.format(remindTask.getEndTime()));
        }
        if (GeneralTool.isNotEmpty(remindTask.getTaskRemark())) {
            emailTemplate = emailTemplate.replace("#{taskRemark}", remindTask.getTaskRemark());
        } else {
            emailTemplate = emailTemplate.replace("#{taskRemark}", "");
        }

        //如果是最后一次发送提醒，需要添加字符【今日截止】
        if (remindTaskQueue.getOptTime().equals(remindTask.getEndTime())) {
            if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG.key)
                    || remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG.key)) {
                emailTemplate = emailTemplate.replace("#{taskTitle}", "[Due Today]" + remindTask.getTaskTitle());
            } else {
                emailTemplate = emailTemplate.replace("#{taskTitle}", "【今日截止】" + remindTask.getTaskTitle());
            }
        } else {
            emailTemplate = emailTemplate.replace("#{taskTitle}", remindTask.getTaskTitle());
        }
        List<Long> ids = new ArrayList<>();
        ids.add(remindTask.getFkTableId());
        List<StudentOfferItemVo> offerItemDtos = saleCenterClient.getStudentByOfferItemIds(ids);
        //设置名字
        if (remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG.key)
                || remindTask.getFkRemindEventTypeKey().equals(ProjectKeyEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG.key)) {
            emailTemplate = emailTemplate.replace("#{dearName}", offerItemDtos.get(0).getFkStudentName());
        } else {
            if (GeneralTool.isNotEmpty(offerItemDtos) && GeneralTool.isNotEmpty(offerItemDtos.get(0).getFkStudentName())) {
                emailTemplate = emailTemplate.replace("#{dearName}", offerItemDtos.get(0).getFkStudentName());
            } else {
                emailTemplate = emailTemplate.replace("#{dearName}", "");
            }
        }

        return emailTemplate;
    }

    //错误邮件通知
    private void sendEmailErrorFromAdmin(Integer errorEnum, RemindTask remindTask, String emailTemplate, Exception e, RemindTaskQueue remindTaskQueue, StudentOfferItemSendEmailVo contactPersonEmailStaff, String email) {
        StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaff.getStudentOfferItemStaffEmailDto();
        StringBuilder sb = new StringBuilder();
        sb.append("项目成员：").append(studentOfferItemStaffEmailDto.getStaffName());
        if (GeneralTool.isNotEmpty(studentOfferItemStaffEmailDto.getUserEmail())) {
            sb.append("项目成员邮箱：").append(studentOfferItemStaffEmailDto.getUserEmail());
        }
        if (GeneralTool.isNotEmpty(email)) {
            sb.append("代理联系人邮箱：").append(email);
        } else {
            List<StudentOfferItemSendEmailVo.ContactPersonEmailDto> contactPersonEmailDtos = contactPersonEmailStaff.getContactPersonEmailDtos();
            for (StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto : contactPersonEmailDtos) {
                sb.append("代理联系人邮箱：").append(contactPersonEmailDto.getAgentEmail());
            }
        }
//        if (errorEnum == 1) {
//            log.info("itemId:" + remindTask.getFkTableId() + sb.toString());
//            sendMessageUtils.sendMail("【项目成员名义发送邮件失败通知】项目成员邮件地址没有填写", sb.toString() + emailTemplate, contactPersonEmailStaff.getErrorEmail());
//        } else if (errorEnum == 2) {
//            log.info("itemId:" + remindTask.getFkTableId() + "【项目成员名义发送邮件失败通知】项目成员邮箱密码没有填写或密码解析错误" + sb.toString());
//            sendMessageUtils.sendMail("【项目成员名义发送邮件失败通知】项目成员邮箱密码没有填写或密码解析错误", sb.toString() + remindTask.getTaskRemark(), contactPersonEmailStaff.getErrorEmail());
//        } else if (errorEnum == 3) {
//            if (e.getMessage().contains("535")) {
//                log.info("itemId:" + remindTask.getFkTableId() + "【项目成员名义发送邮件失败通知】发送失败" + ",项目成员:" + studentOfferItemStaffEmailDto.getStaffName() + ",项目成员邮箱:" + studentOfferItemStaffEmailDto.getUserEmail() + e.getMessage());
//                sendMessageUtils.sendMail("【项目成员名义发送邮件失败通知】项目成员邮箱密码错误", sb.toString() + e.getMessage() + remindTask.getTaskRemark(), contactPersonEmailStaff.getErrorEmail());
//            } else {
//                log.info("itemId:" + remindTask.getFkTableId() + "【项目成员名义发送邮件失败通知】发送失败" + ",项目成员:" + studentOfferItemStaffEmailDto.getStaffName() + e.getMessage());
//                sendMessageUtils.sendMail("【项目成员名义发送邮件失败通知】发送失败", sb.toString() + e.getMessage() + remindTask.getTaskRemark(), contactPersonEmailStaff.getErrorEmail());
//            }
//
//        }
    }


    private String doGetStudentOfferTemplate(String emailTemplate, Long fkTableId) {
        Result<ActHiTaskInstVo> result = workflowCenterClient.getActHiTaskInstDtoAndStudentOffer(fkTableId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            ActHiTaskInstVo actHiTaskInstVo = result.getData();
            if (GeneralTool.isNotEmpty(actHiTaskInstVo.getMap())) {
                Map<String, String> map = actHiTaskInstVo.getMap();
                for (String key : map.keySet()) {
                    emailTemplate = emailTemplate.replace("#{" + key + "}", GeneralTool.isNotEmpty(map.get(key)) ? map.get(key) : "");
                }
            }
        }
        return emailTemplate;
    }

    @Override
    public Boolean sendSms(SmsDto smsVo) {
        if (GeneralTool.isEmpty(smsVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        String result = SmsUtils.sendSms(smsVo.getPhoneNumber(), smsVo.getPhoneArea(), smsVo.getTplId(), smsVo.getParamMaps());
        if (GeneralTool.isEmpty(result)) {
            log.error("发送短信失败，返回结果为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("result_null"));
        }
        JSONObject resultObject = JSONObject.parseObject(result);
        if (!"0".equals(resultObject.getString("error_code"))) {
            log.error("发送短信失败，异常信息为：" + resultObject.getString("reason"));
            throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send") + resultObject.getString("reason"));
        }
        log.info("发送短信成功----------------");
        return true;
    }

    private String doReplaceTemplate(RemindTask remindTask, String emailTemplate) {
        ProjectKeyEnum[] templateKeyEnums = ProjectKeyEnum.SPECIAL_REMINDER_TEMPLATE_KEY;
        for (ProjectKeyEnum templateKeyEnum : templateKeyEnums) {
            if (templateKeyEnum.name().equals(remindTask.getFkRemindEventTypeKey())) {
                String taskRemark = remindTask.getTaskRemark();
                if (taskRemark.contains("<div style=\"display:none;\">")) {
                    taskRemark = taskRemark.replace("<div style=\"display:none;\">", "");
                }
                if (taskRemark.contains("</div>")) {
                    taskRemark = taskRemark.replace("</div>", "");
                }
                Map map = JSON.parseObject(taskRemark, Map.class);
                Set keySet = map.keySet();
                for (Object keyName : keySet) {
                    emailTemplate = emailTemplate.replace("#{" + keyName.toString() + "}", map.get(keyName).toString());
                }
            }
        }
        return emailTemplate;
    }


    /**
     * @Description: 线程发送消息
     * @Author: Jerry
     * @Date:9:47 2021/11/18
     */
    @Override
    public void sendMessage() {
        sendMessageUtils.sendMail("123", "123", "<EMAIL>");
    }

    @Override
    public void sendMessageTest() {
        sendMessageUtils.sendMailTest();
    }

    @Override
    public void sendMessageCustom() {
        String subjectText = "召集令！海外学生公寓预定火热进行中";
        //从数据库查找email并发送指定的邮件
        String context = "<b>尊敬的老师您好！</b><br><br>赶紧联系Hti留洋小窝，预定海外学生公寓吧！"
                + "<br><br>Hti留洋小窝海外留学生公寓预定平台现已开放英国、澳大利亚、美国、加拿大、新西兰（奥克兰）、爱尔兰、德国、葡萄牙、西班牙、中国香港的预定， 目前学生集中的热门城市已出现多地房源紧张的状况，如：杜伦、布里斯托、南安、格拉、悉尼、珀斯等，预计5月份将有更多地方满租，如有学生基本定校，还请尽早扫描下方二维码联系咱们的公寓服务老师，注册成为「留洋小窝」渠道合作方，学生可享受公寓早鸟价格及更多优惠政策！如果学生成功预订，您也将获得金额可观的公寓预订及租金支付佣金！<br>" +
                "<br><br><img src=\"https://iae-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com/email/9DF8D0784F5F1366.jpg\">";

        List<EmailTestSend> emailTestSends = emailTestSendMapper.selectList(Wrappers.<EmailTestSend>lambdaQuery().eq(EmailTestSend::getStatus, 0));
        if (GeneralTool.isNotEmpty(emailTestSends) && emailTestSends.size() > 0) {
            for (EmailTestSend emailTestSend : emailTestSends) {
                String email = emailTestSend.getEmail();
                Boolean result = sendMessageUtils.sendMessageCustom(email, subjectText, context);
                if (result) {
                    emailTestSend.setStatus(1);
                    emailTestSendMapper.updateById(emailTestSend);
                }
                //休息1秒
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                }
            }
        }


    }

    @Override
    public boolean sendMail(String title, String template, String toEmail, String ccEmail) {
        return sendMessageUtils.sendMail(title, template, toEmail, new String[]{ccEmail});
    }

//    @Override
//    public void sendEmail(String title, String typeKey,Long fkStaffId,String taskRemark) {
//        RemindTemplate template = remindTemplateMapper.selectOne(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, typeKey));
//        if (template == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("template_is_empty"));
//        }
//        String emailTemplate = template.getEmailTemplate();
//        Result<StaffVo> staff = permissionCenterClient.getStaffById(fkStaffId);
//        if (staff.isSuccess() && staff.getData() != null) {
//            if (StringUtils.isNotBlank(emailTemplate)) {
//                if (taskRemark.contains("<div style=\"display:none;\">")) {
//                    taskRemark = taskRemark.replace("<div style=\"display:none;\">", "");
//                }
//                if (taskRemark.contains("</div>")) {
//                    taskRemark = taskRemark.replace("</div>", "");
//                }
//                Map<String, String> map = JSON.parseObject(taskRemark, Map.class);
//                map.put("taskTitle", title);
//                StaffVo staffDto = staff.getData();
//                map.put("modifyUser",staffDto.getName()+"（"+staffDto.getNameEn()+"）" + "【" +staffDto.getDepartmentName() + "】");
//                map.put("modifyTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
//                Set<String> keySet = map.keySet();
//                for (String keyName : keySet) {
//                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", map.get(keyName));
//                }
//                String financeEmail;
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_FINANCE_EMAIL.key).getData();
//                if (GeneralTool.isNotEmpty(configDto) && GeneralTool.isNotEmpty(configDto.getValue1())) {
//                    JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//                    String geaFinanceEmail = jsonObject.getString("OTHER");
//                    String iaeFinanceEmail = jsonObject.getString("IAE");
//                    if (staff.getData().getFkCompanyId().equals(3L)) {
//                        financeEmail = iaeFinanceEmail;
//                    } else {
//                        financeEmail = geaFinanceEmail;
//                    }
//                    if (GeneralTool.isNotEmpty(financeEmail)) {
//                        sendMessageUtils.sendMail(title, emailTemplate, financeEmail);
//                    }
//                } else {
//                    sendMessageUtils.sendMail(title, emailTemplate, defaultFinanceEmail);
//                }
//            }
//        }
//    }

    @Override
    public void batchSendEmail(List<Map<String, String>> list, String typeKey) {
        if (list.isEmpty()) {
            return;
        }
        RemindTemplate template = remindTemplateMapper.selectOne(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, typeKey));
        if (template == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("template_is_empty"));
        }
        for (Map<String, String> map : list) {
            String emailTemplate = template.getEmailTemplate();
            Set<String> keySet = map.keySet();
            for (String keyName : keySet) {
                if (GeneralTool.isNotEmpty(map.get(keyName))) {
                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", map.get(keyName));
                } else {
                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", "");
                }
            }
            sendMessageUtils.sendMail(map.get("title"), emailTemplate, map.get("email"));
        }
    }


    @Override
    public void batchSendEnEmail(List<Map<String, String>> list, String typeKey,String version) {
        if (list.isEmpty()) {
            return;
        }
        RemindTemplate template = remindTemplateMapper.selectOne(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, typeKey));
        if (template == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("template_is_empty"));
        }
        String emailTemplate =null;
        if(version.equals("en")){
             emailTemplate = template.getEmailTemplateEn();
        }
        //

        String taskRemark = list.get(0).get("taskRemark");
        try {
            taskRemark = new String(Base64.getDecoder().decode(taskRemark), "UTF-8");
            list.get(0).put("taskRemark",taskRemark);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        for (Map<String, String> map : list) {
            Set<String> keySet = map.keySet();
            for (String keyName : keySet) {
                if (GeneralTool.isNotEmpty(map.get(keyName))) {
                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", map.get(keyName));
                } else {
                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", "");
                }
            }
            sendMessageUtils.sendMail(map.get("title"), emailTemplate, map.get("email"));
        }
    }



    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @Override
    public boolean customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String toEmail, String ccEmail, String template, boolean flag) {
        return sendMessageUtils.customSendMail(defaultEncoding, host, port, protocol, userName, password, title, toEmail, ccEmail, template, flag);
    }

    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @Override
    public boolean customSendMailByMailVo(MailDto mailVo) {
        //传的收件人和抄送人多个 使用第一个构造方法
        if (GeneralTool.isNotEmpty(mailVo.getToEmails()) && GeneralTool.isNotEmpty(mailVo.getCcEmails())) {
            return sendMessageUtils.customSendMail(mailVo.getDefaultEncoding(), mailVo.getHost(), mailVo.getPort(), mailVo.getProtocol(), mailVo.getUserName(), mailVo.getPassword(), mailVo.getTitle(), mailVo.getToEmails(), mailVo.getCcEmails(), mailVo.getTemplate(), mailVo.isFlag());
        } else {
            return sendMessageUtils.customSendMail(mailVo.getDefaultEncoding(), mailVo.getHost(), mailVo.getPort(), mailVo.getProtocol(), mailVo.getUserName(), mailVo.getPassword(), mailVo.getTitle(), mailVo.getToEmail(), mailVo.getCcEmail(), mailVo.getTemplate(), mailVo.isFlag());
        }
    }

    /**
     * 发送给员工邮件
     *
     * @param title
     * @param typeKey
     * @param fkStaffId
     * @param taskRemark
     */
    @Override
    public Boolean sendEmailToStaff(String title, String typeKey, Long fkStaffId, String taskRemark) {
        RemindTemplate template = remindTemplateMapper.selectOne(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, typeKey));
        if (template == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("template_is_empty"));
        }
        String emailTemplate = template.getEmailTemplate();
        Result<StaffVo> staff = permissionCenterClient.getStaffById(fkStaffId);
        if (staff.isSuccess() && staff.getData() != null) {
            if (StringUtils.isNotBlank(emailTemplate)) {
//                if (taskRemark.contains("<div style=\"display:none;\">")) {
//                    taskRemark = taskRemark.replace("<div style=\"display:none;\">", "");
//                }
//                if (taskRemark.contains("</div>")) {
//                    taskRemark = taskRemark.replace("</div>", "");
//                }
                Map<String, String> map = JSON.parseObject(taskRemark, Map.class);
                Set<String> keySet = map.keySet();
                for (String keyName : keySet) {
                    emailTemplate = emailTemplate.replace("#{" + keyName + "}", map.get(keyName));
                }
                sendMessageUtils.sendMail(title, emailTemplate, staff.getData().getEmail());
            }
        }
        return true;
    }

    @Override
    @Async
    public void batchSendEmailCustom(MailDto mailVo) {
        if (GeneralTool.isEmpty(mailVo) || GeneralTool.isEmpty(mailVo.getToEmails())) {
            return;
        }

        log.info("batchSendEmailCustomSize:{}", mailVo.getToEmails().length);

        List<SystemEmailAccount> systemEmailAccounts = systemEmailAccountMapper.selectList(Wrappers.lambdaQuery(SystemEmailAccount.class).eq(SystemEmailAccount::getIsActive, 1)
                .eq(SystemEmailAccount::getType, 0));

        List<String> toEmailList = Arrays.asList(mailVo.getToEmails());
        for (String toEmail : mailVo.getToEmails()) {
            mailVo.setToEmail(toEmail);
            sendMessageUtils.sendMail(mailVo.getTitle(), mailVo.getTemplate(), mailVo.getToEmail());
        }
//
//        List<InvalidEmail> invalidEmails = invalidEmailMapper.selectList(Wrappers.lambdaQuery(InvalidEmail.class));
//        if (GeneralTool.isNotEmpty(invalidEmails)){
//            toEmailList.removeAll(invalidEmails.stream().map(InvalidEmail::getEmail).collect(Collectors.toList()));
//        }
//
//        List<List<String>> lists = Lists.partition(toEmailList, systemEmailAccounts.size());
//        for (int i = 0; i < lists.size(); i++) {
//            MailDto mailVosend = BeanUtil.copy(mailVo, MailDto.class);
//            mailVosend.setDefaultEncoding("utf-8");
//            mailVosend.setHost("smtp.qiye.aliyun.com");
//            mailVosend.setPort(465);
//            mailVosend.setProtocol("smtps");
//            mailVosend.setUserName(systemEmailAccounts.get(i).getEmailAccount());
//            mailVosend.setPassword(systemEmailAccounts.get(i).getEmailPassword());
//            sendMessageUtils.sendMailCustom(mailVosend,lists.get(i),ProjectKeyEnum.SEND_NEWS_AGENT_EMAIL.key);
//        }
    }

    /**
     * 阿里云新闻邮件推送
     *
     * @return
     */
    @Async
    public Boolean aliyunSendMailNew(AliyunSendMailDto aliyunSendMailVo) {
        List<String> toEmailList = new ArrayList<>(Arrays.asList(aliyunSendMailVo.getToEmails()));
        List<InvalidEmail> invalidEmails = invalidEmailMapper.selectList(Wrappers.lambdaQuery(InvalidEmail.class));
        if (GeneralTool.isNotEmpty(invalidEmails)) {
            toEmailList.removeAll(invalidEmails.stream().map(InvalidEmail::getEmail).collect(Collectors.toList()));
        }
        //已发送
        List<BatchSendingEmail> batchSendingEmails = batchSendingEmailMapper.selectList(Wrappers.<BatchSendingEmail>lambdaQuery().eq(BatchSendingEmail::getFkTableName, TableEnum.NEWS.key).eq(BatchSendingEmail::getFkTableId, aliyunSendMailVo.getNewsId()));
        if (GeneralTool.isNotEmpty(batchSendingEmails)) {
            toEmailList.removeAll(batchSendingEmails.stream().map(BatchSendingEmail::getEmail).collect(Collectors.toList()));
        }
        //退订
        List<String> unsubscribeEmailList = unsubscribeEmailMapper.getUnsubscribeEmail(aliyunSendMailVo.getType());
        if (GeneralTool.isNotEmpty(unsubscribeEmailList)) {
            toEmailList.removeAll(unsubscribeEmailList);
        }
        return sendMessageUtils.aliyunSendMail(toEmailList, aliyunSendMailVo.getTitle(), aliyunSendMailVo.getTemplate(), aliyunSendMailVo.getNewsId());
    }

    @Override
    public Boolean sendNewsEmail(AliyunSendMailDto aliyunSendMailVo) {
        return sendMessageUtils.sendNewsEmail(aliyunSendMailVo.getTargetEmail(), aliyunSendMailVo.getTitle(), aliyunSendMailVo.getTemplate(), aliyunSendMailVo.getNewsId(), aliyunSendMailVo.getSenderEmail());
    }

    @Override
    public Long aliyunAddTag(String tagName) {
        return sendMessageUtils.aliyunAddTag(tagName);
    }


    /**
     * 发送系统邮件
     * @param emailSystemMQMessageDto
     * @return
     */
    @Override
    public Boolean sendSystemMail(EmailSystemMQMessageDto emailSystemMQMessageDto) {
        if (!emailSwitch) {
            return true;
        }
        EmailSenderQueue emailSenderQueue = emailSenderQueueMapper.selectById(emailSystemMQMessageDto.getEmailSenderQueueId());
        emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
        utilService.setUpdateInfo(emailSenderQueue);

        String message = emailSystemMQMessageDto.getContent();
        try {
            // 尝试进行Base64解码
            message = new String(Base64.getDecoder().decode(emailSystemMQMessageDto.getContent()), StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            // 如果不是有效的Base64编码内容，则保留原始内容
//            log.info("原始内容====" + message);
        }

        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail);
        try {
            helper.setTo(emailSystemMQMessageDto.getToEmail());
            if (GeneralTool.isNotEmpty(emailSystemMQMessageDto.getCcEmail())) {
                helper.setCc(emailSystemMQMessageDto.getCcEmail());
            }
            helper.setSubject(emailSystemMQMessageDto.getTitle());
            helper.setFrom(from);
            helper.setText(message, true);
            //helper.setText(message, "UTF-8", true);
            javaMailSender.send(mail);
        } catch (MessagingException e) {
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        emailSenderQueue.setOperationStatus(2);
        emailSenderQueue.setEmailFrom(from);
        emailSenderQueueMapper.updateById(emailSenderQueue);
        return true;
    }

    /**
     * 发送自定义邮件
     * @param emailCustomMQMessageDto
     * @return
     */
    @Override
    public Boolean sendCustomMail(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        if (!emailSwitch) {
            return true;
        }
        EmailSenderQueue emailSenderQueue = emailSenderQueueMapper.selectById(emailCustomMQMessageDto.getEmailSenderQueueId());
        emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
        utilService.setUpdateInfo(emailSenderQueue);

        try {
             JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
            javaMailSenderImpl.setDefaultEncoding(emailCustomMQMessageDto.getDefaultEncoding());
            javaMailSenderImpl.setHost(emailCustomMQMessageDto.getHost());
            javaMailSenderImpl.setPort(emailCustomMQMessageDto.getPort());
            javaMailSenderImpl.setProtocol(emailCustomMQMessageDto.getProtocol());
            javaMailSenderImpl.setUsername(emailCustomMQMessageDto.getUserName());
            javaMailSenderImpl.setPassword(emailCustomMQMessageDto.getPassword());
            MimeMessage mail = javaMailSenderImpl.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);
            helper.setTo(emailCustomMQMessageDto.getToEmail());
            if (GeneralTool.isNotEmpty(emailCustomMQMessageDto.getCcEmail())) {
                helper.setCc(emailCustomMQMessageDto.getCcEmail());
            }
            helper.setSubject(emailCustomMQMessageDto.getTitle());
            helper.setFrom(emailCustomMQMessageDto.getUserName());
            helper.setText(emailCustomMQMessageDto.getContent(), emailCustomMQMessageDto.getIsHtml());
            javaMailSenderImpl.send(mail);
        } catch (MessagingException e) {
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
            e.printStackTrace();
            log.error("邮件发送出现异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        emailSenderQueue.setOperationStatus(2);
        emailSenderQueue.setEmailFrom(emailCustomMQMessageDto.getUserName());
        emailSenderQueueMapper.updateById(emailSenderQueue);
        return true;
    }

}
