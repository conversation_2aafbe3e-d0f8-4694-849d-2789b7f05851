package com.get.remindercenter.utils;

import com.get.core.tool.utils.GeneralTool;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件模板引擎工具类
 * 提供动态模板变量替换功能
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
public class EmailTemplateEngine {

    /**
     * 模板变量正则表达式：匹配 ${variable} 格式
     */
    private static final Pattern TEMPLATE_VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    /**
     * 处理邮件模板，替换其中的变量
     *
     * @param template  邮件模板内容
     * @param variables 变量Map，key为变量名，value为变量值
     * @return 处理后的模板内容
     */
    public static String processTemplate(String template, Map<String, String> variables) {
        if (GeneralTool.isEmpty(template)) {
            log.warn("邮件模板内容为空");
            return template;
        }

        if (GeneralTool.isEmpty(variables)) {
            log.warn("模板变量为空，返回原模板");
            return template;
        }

        String result = template;
        
        try {
            Matcher matcher = TEMPLATE_VARIABLE_PATTERN.matcher(template);
            
            while (matcher.find()) {
                String variableName = matcher.group(1); // 获取变量名（不包含${}）
                String variableValue = variables.get(variableName);
                
                if (variableValue != null) {
                    // 替换模板中的变量
                    String variablePattern = "\\$\\{" + Pattern.quote(variableName) + "\\}";
                    result = result.replaceAll(variablePattern, Matcher.quoteReplacement(variableValue));
                    log.debug("替换变量: {} = {}", variableName, variableValue);
                } else {
                    log.warn("模板变量 {} 在参数Map中不存在，保留原变量", variableName);
                }
            }
            
            log.info("模板处理完成，替换了 {} 个变量", variables.size());
            
        } catch (Exception e) {
            log.error("模板处理异常: {}", e.getMessage(), e);
            return template; // 发生异常时返回原模板
        }
        
        return result;
    }

    /**
     * 处理邮件标题，替换其中的变量
     *
     * @param title     邮件标题
     * @param variables 变量Map
     * @return 处理后的标题
     */
    public static String processTitle(String title, Map<String, String> variables) {
        return processTemplate(title, variables);
    }

    /**
     * 验证模板中的变量是否都有对应的值
     *
     * @param template  模板内容
     * @param variables 变量Map
     * @return 缺失的变量数组，如果没有缺失则返回空数组
     */
    public static String[] validateTemplateVariables(String template, Map<String, String> variables) {
        if (GeneralTool.isEmpty(template)) {
            return new String[0];
        }

        java.util.List<String> missingVariables = new java.util.ArrayList<>();
        
        Matcher matcher = TEMPLATE_VARIABLE_PATTERN.matcher(template);
        while (matcher.find()) {
            String variableName = matcher.group(1);
            if (GeneralTool.isEmpty(variables) || !variables.containsKey(variableName)) {
                missingVariables.add(variableName);
            }
        }
        
        return missingVariables.toArray(new String[0]);
    }

    /**
     * 获取模板中所有的变量名
     *
     * @param template 模板内容
     * @return 变量名数组
     */
    public static String[] extractTemplateVariables(String template) {
        if (GeneralTool.isEmpty(template)) {
            return new String[0];
        }

        java.util.Set<String> variables = new java.util.HashSet<>();
        
        Matcher matcher = TEMPLATE_VARIABLE_PATTERN.matcher(template);
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }
        
        return variables.toArray(new String[0]);
    }

}