package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.entity.RemindEventType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface RemindEventTypeMapper extends BaseMapper<RemindEventType> {
//    int insert(RemindEventType record);

    int insertSelective(RemindEventType record);

//    int updateByPrimaryKeySelective(RemindEventType record);
//
//    int updateByPrimaryKey(RemindEventType record);


    /**
     * @Description: 获取最大排序值
     * @Author: Jerry
     * @Date:11:51 2021/11/12
     */
    Integer getMaxViewOrder();
}