package com.get.remindercenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.remindercenter.entity.RemindTemplate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface RemindTemplateMapper extends BaseMapper<RemindTemplate> {
    int insert(RemindTemplate record);

    int insertSelective(RemindTemplate record);

    int updateByPrimaryKeySelective(RemindTemplate record);

    int updateByPrimaryKeyWithBLOBs(RemindTemplate record);

    int updateByPrimaryKey(RemindTemplate record);
}