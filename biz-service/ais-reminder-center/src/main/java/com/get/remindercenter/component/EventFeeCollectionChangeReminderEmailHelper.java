package com.get.remindercenter.component;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.annotion.IgnoreRemind;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.EventFeeCollectionChangeReminderDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.entity.EventBill;
import com.get.salecenter.entity.EventSummary;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.EventBillVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("eventFeeCollectionChangeReminderEmailHelper")
public class EventFeeCollectionChangeReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;



    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        StaffVo staffVo = null;
        try {
            EventFeeCollectionChangeReminderDto eventFeeCollectionChangeReminderDto = assembleEmailData(emailSenderQueue);
            String template = setEmailTemplate(eventFeeCollectionChangeReminderDto);
            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
            emailSystemMQMessageDto.setTitle(emailSenderQueue.getEmailTitle());
            emailSystemMQMessageDto.setContent(template);
            StringJoiner emailsCombined = new StringJoiner(", ");

            for (String id : eventFeeCollectionChangeReminderDto.getStaffEmailSet()){
                try {

                    Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(id));
                    if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                        staffVo = staffDtoResult.getData();
                    }
                    emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                        emailsCombined.add(staffVo.getEmail());
                    }
                } catch (Exception e){
                    // 记录发送失败的邮箱
                    String failedEmail = staffVo != null && staffVo.getEmail() != null ? staffVo.getEmail() : "staffId:" + staffVo.getId();
                    emailSenderQueue.setErrorMessage("失败邮箱: " + failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  staffVo.getId(), failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("EventFeeCollectionChangeReminderEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

    }

    @Override
    public EventFeeCollectionChangeReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        EventFeeCollectionChangeReminderDto reminderDto = new EventFeeCollectionChangeReminderDto();
        //EventBillVo eventBillVo = saleCenterClient.getEventBillById(emailSenderQueue.getFkTableId()).getData();
        Map<String, String> parsedMap = null;
        Long companyId = null;
        if(GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())){
            ObjectMapper mapper = new ObjectMapper();
            try {
                parsedMap = mapper.readValue(emailSenderQueue.getEmailParameter(), Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String  staffIdList = parsedMap.get("staffIdList");
            if (staffIdList != null && staffIdList.startsWith("[") && staffIdList.endsWith("]")) {
                staffIdList = staffIdList.substring(1, staffIdList.length() - 1);
            }
            List<String> list = Arrays.asList(staffIdList.split("\\s*,\\s*"));
            reminderDto.setStaffEmailSet(list);
            companyId = Long.valueOf(parsedMap.get("companyId"));

        }
        if(GeneralTool.isEmpty(companyId)){
            companyId = 3L;
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(companyId);
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        reminderDto.setLanguageCode(versionValue2);

        reminderDto.setMap(parsedMap);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(EventFeeCollectionChangeReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

        /**
         * 获取所有公司
         * @return
         */
        private Map<Long, String> getCompanyMap() {
            Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
            //初始为5的map
            Map<Long, String> companyMap = new HashMap<>(5);
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(com.get.permissioncenter.vo.tree.CompanyTreeVo::getId, com.get.permissioncenter.vo.tree.CompanyTreeVo::getShortName));
            }
            return companyMap;
        }

    private <T>Map<String, String> getTemplateFieldMap(T object) {
        Map<String, String> map = new HashMap<>(20);
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");

        Field[] fields = ReflectUtil.getFieldsDirectly(object.getClass(), false);
        for (Field field : fields) {
            IgnoreRemind IgnoreRemind = field.getAnnotation(IgnoreRemind.class);
            if (null != IgnoreRemind) {
                continue;
            }
            if (GeneralTool.isNotEmpty(ReflectUtil.getFieldValue(object,field))){
                //如果是date类型
                if ("class java.util.Date".equals(field.getGenericType().toString())){
                    String format = fmt.format(ReflectUtil.getFieldValue(object, field));
                    map.put(field.getName(),format);
                }else {
                    map.put(field.getName(),ReflectUtil.getFieldValue(object,field).toString());
                }
            }else {
                map.put(field.getName(),"");
            }
        }
        return map;
    }
}
