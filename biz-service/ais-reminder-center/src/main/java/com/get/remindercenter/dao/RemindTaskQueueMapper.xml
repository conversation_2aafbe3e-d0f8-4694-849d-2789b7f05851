<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.remindercenter.dao.RemindTaskQueueMapper">
  <insert id="insert" parameterType="com.get.remindercenter.entity.RemindTaskQueue">
    insert into m_remind_task_queue (id, fk_remind_task_id, remind_method, 
      opt_time, try_times, try_error, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkRemindTaskId,jdbcType=BIGINT}, #{remindMethod,jdbcType=VARCHAR}, 
      #{optTime,jdbcType=TIMESTAMP}, #{tryTimes,jdbcType=INTEGER}, #{tryError,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.remindercenter.entity.RemindTaskQueue">
    insert into m_remind_task_queue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkRemindTaskId != null">
        fk_remind_task_id,
      </if>
      <if test="remindMethod != null">
        remind_method,
      </if>
      <if test="optTime != null">
        opt_time,
      </if>
      <if test="tryTimes != null">
        try_times,
      </if>
      <if test="tryError != null">
        try_error,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkRemindTaskId != null">
        #{fkRemindTaskId,jdbcType=BIGINT},
      </if>
      <if test="remindMethod != null">
        #{remindMethod,jdbcType=VARCHAR},
      </if>
      <if test="optTime != null">
        #{optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tryTimes != null">
        #{tryTimes,jdbcType=INTEGER},
      </if>
      <if test="tryError != null">
        #{tryError,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.remindercenter.entity.RemindTaskQueue">
    update m_remind_task_queue
    <set>
      <if test="fkRemindTaskId != null">
        fk_remind_task_id = #{fkRemindTaskId,jdbcType=BIGINT},
      </if>
      <if test="remindMethod != null">
        remind_method = #{remindMethod,jdbcType=VARCHAR},
      </if>
      <if test="optTime != null">
        opt_time = #{optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tryTimes != null">
        try_times = #{tryTimes,jdbcType=INTEGER},
      </if>
      <if test="tryError != null">
        try_error = #{tryError,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.remindercenter.entity.RemindTaskQueue">
    update m_remind_task_queue
    set fk_remind_task_id = #{fkRemindTaskId,jdbcType=BIGINT},
      remind_method = #{remindMethod,jdbcType=VARCHAR},
      opt_time = #{optTime,jdbcType=TIMESTAMP},
      try_times = #{tryTimes,jdbcType=INTEGER},
      try_error = #{tryError,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getRemindTaskQueues" resultType="com.get.remindercenter.entity.RemindTaskQueue">
    select m1.id, m1.fk_remind_task_id, m1.remind_method, m1.opt_time, m1.try_times, m1.try_error,
    m1.gmt_create,m1.gmt_modified,m1.gmt_create_user,m1.gmt_modified_user
    from m_remind_task_queue m1 join (SELECT
    max(opt_time) as opt_time,fk_remind_task_id
    FROM
    m_remind_task_queue where
    unix_timestamp(#{nowDate}) <![CDATA[>= ]]> unix_timestamp(opt_time)
    and IFNULL(try_times,0) <![CDATA[ < ]]> 3 and remind_method != 3
    group by fk_remind_task_id ) m2 on m1.fk_remind_task_id = m2.fk_remind_task_id and m1.opt_time = m2.opt_time
  </select>
  <select id="getSystemMessage" resultType="com.get.remindercenter.vo.RemindTaskQueueVo">
    select m1.id, m1.fk_remind_task_id, m1.remind_method, m1.opt_time, m1.try_times, m1.try_error,
    m1.gmt_create,m1.gmt_modified,m1.gmt_create_user,m1.gmt_modified_user,mrt.task_title AS taskTitle,mrt.task_remark AS taskRemark,
    mrt.fk_table_name,mrt.fk_db_name,mrt.fk_table_id
    from m_remind_task_queue m1 join (SELECT
    max(opt_time) as opt_time,fk_remind_task_id
    FROM
    m_remind_task_queue
    group by fk_remind_task_id ) m2 on m1.fk_remind_task_id = m2.fk_remind_task_id and m1.opt_time = m2.opt_time
    join m_remind_task mrt on mrt.id = m1.fk_remind_task_id where mrt.fk_staff_id = #{fkStaffId} and
    unix_timestamp(#{nowDate}) <![CDATA[>= ]]> unix_timestamp(m1.opt_time) and m1.remind_method = 3
  </select>
    <select id="getStaffContractCompanyId" resultType="java.lang.Long">
      SELECT
        b.fk_company_id
      FROM
        ais_permission_center.m_staff_contract a
          LEFT JOIN ais_permission_center.m_staff b on a.fk_staff_id = b.id
      where a.id = #{fkTableId}
    </select>
    <select id="getStaffContractIsOnDuty" resultType="java.lang.Boolean">
      SELECT
        b.is_on_duty
      FROM
        ais_permission_center.m_staff_contract a
          LEFT JOIN ais_permission_center.m_staff b on a.fk_staff_id = b.id
      where a.id = #{fkTableId}
    </select>
</mapper>