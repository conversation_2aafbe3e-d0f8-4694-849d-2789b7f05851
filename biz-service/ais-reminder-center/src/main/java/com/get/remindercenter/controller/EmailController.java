package com.get.remindercenter.controller;

import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.remindercenter.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 邮件管理类
 */
@Api(tags = "邮件管理")
@RestController
@RequestMapping("reminder/email")
public class EmailController {

    @Resource
    private EmailService emailService;

    @ApiOperation(value = "推广邮件退订", notes = "type发送类型：1Hubs/2市场")
    @VerifyLogin(IsVerify = false)
    @GetMapping("/promotionUnsubscribe")
    public ResponseBo promotionUnsubscribe(@RequestParam("email") String email, @RequestParam("type") Integer type) throws Exception {
        emailService.promotionUnsubscribe(email, type);
        return ResponseBo.ok();
    }

}
