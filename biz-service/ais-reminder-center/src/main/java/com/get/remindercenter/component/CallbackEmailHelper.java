package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.ApplicationStepAdmittedEmailDto;
import com.get.remindercenter.dto.CallbackReminderDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.ClientVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("callbackEmailHelper")
@Slf4j
public class CallbackEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;


    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            //组装数据
            CallbackReminderDto callbackReminderDto   = assembleEmailData(emailSenderQueue);
            //获取接收人邮箱
            //Long staffId = permissionCenterClient.getStaffSupervisorIdByStaffId(studyPlanSameSchoolCourseReminderDto.getStaffId()).getData();
            Long  staffId = callbackReminderDto.getStaffId();
            StaffVo staffVo = permissionCenterClient.getStaffById(staffId).getData();
            if (GeneralTool.isNotEmpty(staffVo)) {
                //设置邮件模板
                String template = setEmailTemplate(callbackReminderDto);
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(callbackReminderDto.getId());
                emailSystemMQMessageDto.setTitle(callbackReminderDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
                //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                emailSenderQueue.setEmailTo(staffVo.getEmail());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo,staffVo.getEmail());  // 只更新 emailTo 字段
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            }
        }catch (Exception e){
            log.error("CallbackEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public CallbackReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        ClientVo clientVo = null;
        //ClientVo clientVo = clientService.findClientById(clientEventRemindDto.getFkClientId());
        if (GeneralTool.isEmpty(clientVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
        String link = configVo.getValue1();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String taskLink = link + "/sales-center_repository-management_repository-detail/"
                + clientVo.getId()
                + "?tabtype=event&clientId="
                + clientVo.getId();
        Map<String, String> map = new HashMap<>();
        map.put("taskLink", taskLink);
        map.put("name", clientVo.getName());
        map.put("num", clientVo.getNum());
        map.put("startTime",sdf.format(emailSenderQueue.getOperationTime()));
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = null;
        if (GeneralTool.isNotEmpty(clientVo.getFkCompanyId())) {
            versionValue2 = versionConfigMap.get(clientVo.getFkCompanyId());
        }else {
            versionValue2 = "zh";
        }

        String title = null;
        if(!versionValue2.equals("en")){
            title ="【"+clientVo.getName()+"】预约回访";
        }else {
            title ="["+clientVo.getName()+"] Schedule a follow-up visit";
        }
        CallbackReminderDto reminderDto = new CallbackReminderDto();
        reminderDto.setMap(map);
        reminderDto.setEmailTitle(title);
        reminderDto.setLanguageCode(versionValue2);
        String emailTo = emailSenderQueue.getEmailTo();
        reminderDto.setStaffId(Long.valueOf(emailTo));
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(CallbackReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.FOLLOW_UP_APPOINTMENT.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

}
