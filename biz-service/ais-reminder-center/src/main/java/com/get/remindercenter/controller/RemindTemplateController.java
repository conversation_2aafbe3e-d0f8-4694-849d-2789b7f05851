package com.get.remindercenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.remindercenter.vo.RemindTemplateVo;
import com.get.remindercenter.service.RemindTemplateService;
import com.get.remindercenter.dto.RemindTemplateListDto;
import com.get.remindercenter.dto.RemindTemplateUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:10
 * Date: 2021/11/12
 * Description:提醒模板管理控制器
 */
@Api(tags = "提醒模板管理控制器")
@RestController
@RequestMapping("reminder/remindTemplate")
public class RemindTemplateController {

    @Resource
    private RemindTemplateService remindTemplateService;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:35 2021/11/12
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/提醒模板管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<RemindTemplateVo> datas(@RequestBody SearchBean<RemindTemplateListDto> page) {
        List<RemindTemplateVo> datas = remindTemplateService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:35 2021/11/12
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.ADD, description = "提醒中心/提醒模板管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(RemindTemplateUpdateDto.Add.class) RemindTemplateUpdateDto remindTemplateUpdateDto) {
        remindTemplateService.add(remindTemplateUpdateDto);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/提醒模板管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(RemindTemplateUpdateDto.Update.class) RemindTemplateUpdateDto remindTemplateUpdateDto) {
        remindTemplateService.update(remindTemplateUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DETAIL, description = "提醒中心/提醒模板管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<RemindTemplateVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(remindTemplateService.detail(id));
    }

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DELETE, description = "提醒中心/提醒模板管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        remindTemplateService.delete(id);
        return DeleteResponseBo.ok();
    }
}
