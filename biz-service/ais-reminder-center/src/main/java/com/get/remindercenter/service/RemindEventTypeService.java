package com.get.remindercenter.service;

import com.get.common.result.Page;
import com.get.remindercenter.vo.RemindEventTypeVo;
import com.get.remindercenter.dto.RemindEventTypeListDto;
import com.get.remindercenter.dto.RemindEventTypeUpdateDto;

import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:37
 * Date: 2021/11/12
 * Description:提醒事件类型管理业务层
 */
public interface RemindEventTypeService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    List<RemindEventTypeVo> datas(RemindEventTypeListDto remindEventTypeListDto, Page page);

    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void add(RemindEventTypeUpdateDto remindEventTypeUpdateDto);

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void update(RemindEventTypeUpdateDto remindEventTypeUpdateDto);

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    RemindEventTypeVo detail(Long id);

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void delete(Long id);


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void movingOrder(List<Long> ids);
}
