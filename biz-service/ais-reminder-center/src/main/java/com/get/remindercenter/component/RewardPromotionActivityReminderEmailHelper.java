package com.get.remindercenter.component;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.EventFeeCollectionChangeReminderDto;
import com.get.remindercenter.dto.RewardPromotionActivityReminderDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.EventBillVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component("rewardPromotionActivityReminderEmailHelper")
public class RewardPromotionActivityReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            RewardPromotionActivityReminderDto rewardPromotionActivityReminderDto = assembleEmailData(emailSenderQueue);
            //获取接收人邮箱
            StaffVo staffVo = permissionCenterClient.getStaffById(rewardPromotionActivityReminderDto.getStaffId()).getData();
        if (GeneralTool.isNotEmpty(staffVo)) {
            //设置邮件模板
            String template = setEmailTemplate(rewardPromotionActivityReminderDto);
            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(rewardPromotionActivityReminderDto.getId());
            emailSystemMQMessageDto.setTitle(rewardPromotionActivityReminderDto.getEmailTitle());
            emailSystemMQMessageDto.setContent(template);
            emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
            //emailSystemMQMessageDto.setToEmail("<EMAIL>");
            remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
            emailSenderQueue.setEmailTo(staffVo.getEmail());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo,staffVo.getEmail());  // 只更新 emailTo 字段
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }
    }catch (Exception e){
        log.error("StudyPlanSameSchoolCourseEmailHelper error:{}", e);
        emailSenderQueue.setErrorMessage(e.getMessage());
        emailSenderQueue.setOperationStatus(-1);
        emailSenderQueueMapper.updateById(emailSenderQueue);
    }
    }

    @Override
    public RewardPromotionActivityReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        RewardPromotionActivityReminderDto reminderDto = new RewardPromotionActivityReminderDto();
        //EventBillVo eventBillVo = saleCenterClient.getEventBillById(emailSenderQueue.getFkTableId()).getData();
        Map<String, String> parsedMap = null;
        if(GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())){
            Map<String, String> convertedMap = JSON.parseObject(emailSenderQueue.getEmailParameter(), new TypeReference<Map<String, String>>() {});

            ObjectMapper mapper = new ObjectMapper();
            try {
                parsedMap = mapper.readValue(emailSenderQueue.getEmailParameter(), Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String  staffIdL = parsedMap.get("staffId");
            if(GeneralTool.isNotEmpty(staffIdL)){
                reminderDto.setStaffId(Long.valueOf(staffIdL));
            }
        }
        StaffVo staffVo = null;
        if(GeneralTool.isNotEmpty(reminderDto.getStaffId())){
           staffVo = permissionCenterClient.getStaffById(reminderDto.getStaffId()).getData();
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        reminderDto.setLanguageCode(versionValue2);

        reminderDto.setMap(parsedMap);
        //插入标题
//        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
//            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
//            emailSenderQueueMapper.updateById(emailSenderQueue);
//        }
        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(RewardPromotionActivityReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.REWARD_PROMOTION_ACTIVITY_REMINDER.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

}
