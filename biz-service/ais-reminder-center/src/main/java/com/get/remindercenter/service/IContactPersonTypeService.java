package com.get.remindercenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.remindercenter.vo.ContactPersonTypeVo;
import com.get.remindercenter.dto.ContactPersonTypeDto;
import com.get.remindercenter.vo.ContactPersonTypeVo;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/22 10:49
 * @verison: 1.0
 * @description: 联系人类型管理接口
 */
public interface IContactPersonTypeService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContactPersonTypeVo findContactPersonTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [contactPersonTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(ValidList<ContactPersonTypeDto> contactPersonTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param contactPersonTypeDto
     * @return
     */
    ContactPersonTypeVo updateContactPersonType(ContactPersonTypeDto contactPersonTypeDto);

    /**
     * 列表
     *
     * @param contactPersonTypeDto
     * @param page
     * @return
     */
    List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeDto contactPersonTypeDto, Page page);


    /**
     * 列表不分页
     *
     * @return
     * @
     */
    List<ContactPersonTypeVo> getContactPersonTypes();


    /**
     * 上移下移
     *
     * @param contactPersonTypeDtos
     * @return
     */
    void movingOrder(List<ContactPersonTypeDto> contactPersonTypeDtos);


}
