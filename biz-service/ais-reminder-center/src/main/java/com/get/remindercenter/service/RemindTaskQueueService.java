package com.get.remindercenter.service;

import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.vo.AliyunSendMailVo;
import com.get.remindercenter.vo.RemindTaskQueueVo;
import com.get.remindercenter.entity.RemindTaskQueue;
import com.get.remindercenter.dto.MailDto;
import com.get.remindercenter.dto.SmsDto;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;


import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:37
 * Date: 2021/11/12
 * Description:提醒任务执行队列管理业务层
 */
public interface RemindTaskQueueService {

    /**
     * @Description: 新增队列任务
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void add(RemindTaskQueue remindTaskQueue);

    /**
     * @Description: 根据提醒任务id删除队列任务
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void delete(Long fkRemindTaskId, Date optTime);

    /**
     * @Description: 根据主键id删除队列消息
     * @Author: Jerry
     * @Date:15:47 2021/12/2
     */
    void deleteById(Long id);


    /**
     * @Description: 根据提醒任务id和提醒方式删除队列任务
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void deleteByRemindMethod(Long fkRemindTaskId, Date optTime, String remindMethod);

    /**
     * @Description: 批量删除
     * @Author: Jerry
     * @Date:15:38 2021/11/25
     */
    void batchDelete(Set<Long> fkRemindTaskIds, Date optTime);

    /**
     * @Description: 获取需要执行的任务
     * @Author: Jerry
     * @Date:14:16 2021/11/15
     */
    List<RemindTaskQueue> getRemindTaskQueues();

    /**
     * @Description: 获取系统内消息
     * @Author: Jerry
     * @Date:15:35 2021/12/2
     */
    List<RemindTaskQueueVo> getSystemMessage();


    /**
     * @Description: 线程执行任务
     * @Author: Jerry
     * @Date:16:28 2021/11/15
     */
    void performTasks(Long remindTaskId, RemindTaskQueue remindTaskQueue);


    /**
     * 发送短信
     * @param smsDto
     */
    Boolean sendSms(SmsDto smsDto);
    /**
     * @Description: 线程发送消息
     * @Author: Jerry
     * @Date:9:47 2021/11/18
     */
    void sendMessage();


    void sendMessageTest();

    void sendMessageCustom();

    /**
     * 发送邮件
     *
     * @Date 10:40 2022/8/22
     * <AUTHOR>
     */
    boolean sendMail(String title, String template, String toEmail, String ccEmail);

    /**
     * 发送邮件
     * @param title
     * @param typeKey
     * @param fkStaffId
     */
//    void sendEmail(String title, String typeKey,Long fkStaffId,String taskRemark);

    /**
     * 批量发送邮件(模板)
     * @param list
     * @param typeKey
     */
    void batchSendEmail(List<Map<String,String>> list,String typeKey);


    void batchSendEnEmail(List<Map<String,String>> list,String typeKey,String version);

    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    boolean customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String toEmail, String ccEmail, String template, boolean flag);

    /**
     * 自定义发送邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    boolean customSendMailByMailVo(MailDto mailDto);

    /**
     * 发送给员工邮件
     *
     * @param title
     * @param typeKey
     * @param fkStaffId
     * @param taskRemark
     */
    Boolean sendEmailToStaff(String title, String typeKey, Long fkStaffId, String taskRemark);

    /**
     * 批量发送邮件(不抛异常)
     * @param mailDto
     */
    void batchSendEmailCustom(MailDto mailDto);

    /**
     * 阿里云邮件推送
     *
     * @return
     */
    Boolean aliyunSendMailNew(AliyunSendMailDto aliyunSendMailVo);

    Long aliyunAddTag(String tagName);

    /**
     * 定时任务发送新闻邮件
     */
    Boolean sendNewsEmail(AliyunSendMailDto aliyunSendMailVo);


    /**
     * 发送系统邮件
     * @param emailSystemMQMessageDto
     * @return
     */
    Boolean sendSystemMail(EmailSystemMQMessageDto emailSystemMQMessageDto);

    /**
     * 发送自定义邮件
     * @param emailCustomMQMessageDto
     * @return
     */
    Boolean sendCustomMail(EmailCustomMQMessageDto emailCustomMQMessageDto);
}
