//package com.get.remindercenter.utils;
//
//import org.mybatis.generator.api.IntrospectedColumn;
//import org.mybatis.generator.api.IntrospectedTable;
//import org.mybatis.generator.api.dom.java.CompilationUnit;
//import org.mybatis.generator.api.dom.java.Field;
//import org.mybatis.generator.api.dom.java.FullyQualifiedJavaType;
//import org.mybatis.generator.api.dom.java.TopLevelClass;
//import org.mybatis.generator.internal.DefaultCommentGenerator;
//import org.mybatis.generator.internal.util.StringUtility;
//
//import java.util.Properties;
//
//
//public class CommentGenerator extends DefaultCommentGenerator {
//    private boolean addRemarkComments = false;
//    private static final String EXAMPLE_SUFFIX="Example";
//    private static final String API_MODEL_PROPERTY_FULL_CLASS_NAME="io.swagger.annotations.ApiModelProperty";
//
//    /**
//     * 设置用户配置的参数
//     */
//    @Override
//    public void addConfigurationProperties(Properties properties) {
//        super.addConfigurationProperties(properties);
//        this.addRemarkComments = StringUtility.isTrue(properties.getProperty("addRemarkComments"));
//    }
//    /**
//     * 给字段添加注释
//     */
//    @Override
//    public void addFieldComment(Field field, IntrospectedTable introspectedTable,
//                                IntrospectedColumn introspectedColumn) {
//        String remarks = introspectedColumn.getRemarks();
//        String actualColumnName = introspectedColumn.getActualColumnName();
//
//        //根据参数和备注信息判断是否添加备注信息
//        if(addRemarkComments&& StringUtility.stringHasValue(remarks)){
//            //数据库中特殊字符需要转义
//            if(remarks.contains("\"")){
//                remarks = remarks.replace("\"","'");
//            }
//            addFieldJavaDoc(field, remarks);
//            //给model的字段添加swagger注解
//            field.addJavaDocLine("@ApiModelProperty(value = \""+remarks+"\")");
//            field.addJavaDocLine("@Column(name = \""+actualColumnName+"\")");
//        }
//    }
//
//
//    @Override
//    public void addModelClassComment(TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
//        String tableName = introspectedTable.getAliasedFullyQualifiedTableNameAtRuntime();
//        //添加导入的包
//        topLevelClass.addImportedType("lombok.Data");
//        topLevelClass.addImportedType("javax.persistence.Column");
//        topLevelClass.addImportedType("javax.persistence.Table");
//        //添加导入的注解
//        topLevelClass.addAnnotation("@Data");
//        topLevelClass.addAnnotation("@Table(name = \""+tableName+"\")");
//    }
//
//    /**
//     * 给model的字段添加注释
//     */
//    private void addFieldJavaDoc(Field field, String remarks) {
//        //文档注释开始
//        field.addJavaDocLine("/**");
//        //获取数据库字段的备注信息
//        field.addJavaDocLine(" * "+remarks);
//        field.addJavaDocLine(" */");
//    }
//
//    @Override
//    public void addJavaFileComment(CompilationUnit compilationUnit) {
//        super.addJavaFileComment(compilationUnit);
//        //只在model中添加swagger注解类的导入
//        if(!compilationUnit.isJavaInterface()&&!compilationUnit.getType().getFullyQualifiedName().contains(EXAMPLE_SUFFIX)){
//            compilationUnit.addImportedType(new FullyQualifiedJavaType(API_MODEL_PROPERTY_FULL_CLASS_NAME));
//        }
//    }
//
//}