package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.ReminderCenterConstant;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.AcceptOfferDeadlineReminderDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习计划相同学校课程提醒
 */
@Slf4j
@Component("studyPlanSameSchoolCourseEmailHelper")
public class StudyPlanSameSchoolCourseEmailHelper extends EmailAbstractHelper {


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            //组装数据
            StudyPlanSameSchoolCourseReminderDto studyPlanSameSchoolCourseReminderDto = assembleEmailData(emailSenderQueue);
            //获取接收人邮箱
            //Long staffId = permissionCenterClient.getStaffSupervisorIdByStaffId(studyPlanSameSchoolCourseReminderDto.getStaffId()).getData();
            Long  staffId = studyPlanSameSchoolCourseReminderDto.getStaffId();
            StaffVo staffVo = permissionCenterClient.getStaffById(staffId).getData();
            if (GeneralTool.isNotEmpty(staffVo)) {
                //设置邮件模板
                String template = setEmailTemplate(studyPlanSameSchoolCourseReminderDto);
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(studyPlanSameSchoolCourseReminderDto.getId());
                emailSystemMQMessageDto.setTitle(studyPlanSameSchoolCourseReminderDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
                //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                emailSenderQueue.setEmailTo(staffVo.getEmail());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo,staffVo.getEmail());  // 只更新 emailTo 字段
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            }
        }catch (Exception e){
            log.error("StudyPlanSameSchoolCourseEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    /**
     * 组装数据
     * @param emailSenderQueue
     * @return
     */
    @Override
    public StudyPlanSameSchoolCourseReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        StudyPlanSameSchoolCourseReminderDto reminderDto = new StudyPlanSameSchoolCourseReminderDto();
        //获取申请计划
        StudentOfferItem studentOfferItem = saleCenterClient.getStudentOfferItemById(emailSenderQueue.getFkTableId()).getData();
        //获取学生信息
        Student student = saleCenterClient.getStudentById(studentOfferItem.getFkStudentId()).getData();
        String id = emailSenderQueue.getEmailParameter();
        if(GeneralTool.isNotEmpty(id)){
            reminderDto.setStaffId(Long.valueOf(id));
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(student.getFkCompanyId());
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        reminderDto.setLanguageCode(versionValue2);
        Map<String, String> map = new HashMap<>();
        //fkAreaCountryName
        AreaCountry country = institutionCenterClient.getCountryById(studentOfferItem.getFkAreaCountryId()).getData();
        Institution institution = institutionCenterClient.getInstitutionById(studentOfferItem.getFkInstitutionId()).getData();
        InstitutionCourseVo courseVo = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId()).getData();

        map.put("fkAreaCountryName", institutionCenterClient.getCountryChnNameById(studentOfferItem.getFkAreaCountryId()).getData());
        //fkInstitutionName
        map.put("fkInstitutionName", institutionCenterClient.getInstitutionName(studentOfferItem.getFkInstitutionId()).getData());
        //fkCourseName
        map.put("fkCourseName", institutionCenterClient.getCourseNameById(studentOfferItem.getFkInstitutionCourseId()).getData());

        map.put("studentName", student.getName());
        if(versionValue2.equals("en")){
            map.put("fkAreaCountryName", country.getName());
            map.put("fkInstitutionName", institution.getName());
            map.put("fkCourseName", courseVo.getName());
            reminderDto.setEmailTitle(student.getName() + ", Reminder for creating similar application plans");
        }else {
           String name = student.getName() + "（" + student.getFirstName() + student.getLastName() + "）";
            reminderDto.setEmailTitle( name+ "， 存在创建相似申请计划提醒");
        }
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        reminderDto.setMap(map);
        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(StudyPlanSameSchoolCourseReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.STUDY_PLAN_SAME_SCHOOL_COURSE_REMINDER.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }


}
