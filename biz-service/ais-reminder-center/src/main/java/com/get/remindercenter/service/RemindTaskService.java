package com.get.remindercenter.service;

import com.get.common.result.Page;
import com.get.remindercenter.vo.RemindTaskVo;
import com.get.remindercenter.vo.RemindTaskListVo;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.RemindTaskListDto;
import com.get.remindercenter.dto.RemindTaskUpdateDto;

import java.util.List;
import java.util.Set;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:37
 * Date: 2021/11/12
 * Description:提醒任务管理业务层
 */
public interface RemindTaskService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    List<RemindTaskVo> datas(RemindTaskListDto remindTaskListDto);

    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void add(RemindTaskUpdateDto remindTaskUpdateDto);


    /**
     * @Description: 批量新增任务
     * @Author: Jerry
     * @Date:13:00 2021/11/18
     */
    Boolean batchAdd(List<RemindTaskDto> remindTaskDtos);


    /**
     * @Description: 批量新增任务
     * @Author: Jerry
     * @Date:13:00 2021/11/18
     */
    Boolean batchAddTask(List<RemindTaskDto> remindTaskDtos);



    Boolean batchUpdateTaskNew(List<RemindTaskDto> remindTaskDtos);

    Boolean batchDeleteTaskNew(List<RemindTaskDto> remindTaskDtos);



    /**
     * @Description: 批量修改任务
     * @Author: Jerry
     * @Date:15:30 2021/11/25
     */
    Boolean batchUpdate(List<RemindTaskDto> remindTaskDtos, String fkTableName, Long fkTableId);

    /**
     * @Description: 批量修改任务(根据fkRemindEventTypeKey)
     * @Author: Jerry
     * @Date:15:30 2021/11/25
     */
    Boolean batchUpdateNew(List<RemindTaskDto> remindTaskDtos);

    Boolean batchUpdatebatchImport(List<RemindTaskDto> remindTaskDtos);


    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void update(RemindTaskUpdateDto remindTaskUpdateDto);

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    RemindTaskVo detail(Long id);

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    void delete(Long id);

    /**
     * @Description: 获取任务总数
     * @Author: Jerry
     * @Date:16:48 2021/11/16
     */
    List<ReminderTaskCountVo> getTaskCount();

    /**
     * @Description: 批量修改任务(支持表的多个Id)
     * @Author: Jerry
     * @Date:17:40 2021/12/1
     */
    Boolean batchUpdateByTableIds(List<RemindTaskDto> remindTaskDtos, String fkTableName, Set<Long> fkTableIds);

    Boolean batchDeleteByTableId(String fkTableName, Long fkTableId, List<String> fkRemindEventTypeKeys);

    List<RemindTaskListVo> getRemindTaskDatas(RemindTaskListDto data, Page page);

    Boolean deleteEmailQueueByTableId(RemindTaskDto remindTaskDto);
}
