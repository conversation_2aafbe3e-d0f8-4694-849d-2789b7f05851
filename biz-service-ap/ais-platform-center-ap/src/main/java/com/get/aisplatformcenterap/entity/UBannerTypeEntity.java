package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName u_banner_type
 */
@TableName(value ="u_banner_type")
@Data
public class UBannerTypeEntity extends BaseEntity implements Serializable {


    @ApiModelProperty("公司Id")
    private Long fkCompanyId;


    @ApiModelProperty("所属产品Id")
    private Long fkPlatformId;


    @ApiModelProperty("平台应用code")
    private String fkPlatformCode;


    @ApiModelProperty("类型名称英文(拼音)名")
    private String typeName;


    @ApiModelProperty("类型key,枚举类型key")
    private String typeKey;


    @ApiModelProperty("位置:长")
    private Integer height;

    @ApiModelProperty("位置:宽")
    private Integer width;


    @ApiModelProperty("排序,倒序,数字由大小排列")
    private Integer viewOrder;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}