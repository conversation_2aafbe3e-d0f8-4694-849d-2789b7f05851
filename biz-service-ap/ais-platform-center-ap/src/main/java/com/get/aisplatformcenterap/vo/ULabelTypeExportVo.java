package com.get.aisplatformcenterap.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ULabelTypeExportVo {


    /**
     * 标签ID
     */
    @ApiModelProperty(value = "标签ID")
    private String labelId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "类型名称")
    private String labelName;

    /**
     * 标签关键字
     */
    @ApiModelProperty(value = "类型系统Key")
    private String labelKey;

    /**
     * 类型描述
     */
    @ApiModelProperty(value = "标签描述")
    private String labelRemark;


    @ApiModelProperty(value = "标签类型ID")
    private Long id;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private  String typeName;

    /**
     * 类型Key
     */
    @ApiModelProperty(value = "类型系统Key")
    private String typeKey;

    /**
     * 类型描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;








}
