package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息记录
 * @TableName m_message
 */
@TableName(value ="m_message")
@Data
public class MMessageEntity extends BaseEntity implements Serializable {

    @ApiModelProperty("公司Id")
    private Long fkCompanyId;
    @ApiModelProperty("平台应用Id")
    private Long fkPlatformId;
    @ApiModelProperty("平台应用CODE")
    private String fkPlatformCode;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("权重")
    private Integer weight;
    @ApiModelProperty("跳转方式 1自定义页面/2小程序页面")
    private Integer jumpMode;
    @ApiModelProperty("跳转url")
    private String jumpUrl;
    @ApiModelProperty("网页标题")
    private String webTitle;
    @ApiModelProperty("网页内容")
    private String webMetaDescription;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startTime;
    @ApiModelProperty("结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("上架状态 0已下架/1待上架/2 已上架")
    private Integer status;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}