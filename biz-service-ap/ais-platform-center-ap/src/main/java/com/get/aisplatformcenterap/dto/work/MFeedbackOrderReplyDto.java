package com.get.aisplatformcenterap.dto.work;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MFeedbackOrderReplyDto {

    @NotNull(message = "反馈工单Id为空")
    @ApiModelProperty("反馈工单Id")
    private Long fkFeedbackOrderId;


    @ApiModelProperty("回复内容")
    @NotNull(message = "回复内容为空")
    private String reply;





}
