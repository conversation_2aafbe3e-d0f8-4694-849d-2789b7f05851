package com.get.aisplatformcenterap.feign;

import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.constant.AppCenterConstant;
import com.get.common.result.SearchBean;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;
import java.util.Set;

@FeignClient(
        value = AppCenterConstant.APPAISPLATFORMCENTER
)
public interface IPlatformCenterClient {
    String API_PREFIX = "/feign";

    /**
     * 根据名称模糊搜索用户ids
     */
    String GET_USER_IDS_BY_PARAM = API_PREFIX + "/get-user-ids-by-param";


    /**
     * feign调用 根据userid获取名称（微信昵称）
     */
    String GET_USER_NICK_NAMES_BY_USER_IDS = API_PREFIX + "/get-user-nick-names-by-user-ids";

    /**
     * 根据ids获取人员对应的城市名称
     */
    String GET_CITY_NAMES_BY_USER_IDS = API_PREFIX + "/get-city-names-by-user-ids";

    /**
     * feign调用 根据userid获取手机号
     */
    String GET_MOBILE_BY_USER_IDS = API_PREFIX + "/get-mobile-by-user-ids";

    /**
     * 根据名称模糊或者手机号搜索用户ids
     */
    String GET_USER_IDS_BY_NAME_OR_MOBILE = API_PREFIX + "/get-user-ids-by-name-or-mobile";

    /**
     * feign调用 根据userid获取对象
     */
    String GET_USER_INFO_DTO_BY_IDS = API_PREFIX + "/get-user-info-dto-by-ids";

    String GET_LABEL_INFO_BY_ID_AND_KEYWORD = API_PREFIX + "/get-label-info-by-id-and-keyword";


    /**
     * 根据名称模糊搜索用户ids
     * @param userName
     * @param fkAreaCityId
     * @param bdName
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_PARAM)
    Result<Set<Long>> getUserIdsByParam(@RequestParam(value = "userName", required = false) String userName,
                                        @RequestParam(value = "fkAreaCityId", required = false) Long fkAreaCityId,
                                        @RequestParam(value = "bdName", required = false) String bdName);


    /**
     * feign调用 根据userid获取名称（微信昵称）
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_NICK_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getUserNickNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 根据ids获取人员对应的城市名称
     * @param userIds
     * @return
     */
    @PostMapping(GET_CITY_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getCityNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * feign调用 根据userid获取手机号
     * @param userIds
     * @return
     */
    @PostMapping(GET_MOBILE_BY_USER_IDS)
    Result<Map<Long, String>> getMobileByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 根据名称模糊或者手机号搜索用户ids
     * @param userName
     * @param phoneNumber
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_NAME_OR_MOBILE)
    Result<Set<Long>> getUserIdsByNameOrMobile(@RequestParam(value = "userName", required = false) String userName,
                                               @RequestParam(value = "phoneNumber", required = false) String phoneNumber);

    /**
     * feign调用 根据userid获取对象
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_INFO_DTO_BY_IDS)
    Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(@RequestBody Set<Long> userIds);

    @PostMapping(GET_LABEL_INFO_BY_ID_AND_KEYWORD)
    Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(@RequestBody SearchBean<AgentLabelDto> page);
}
