package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:29
 */

@Data
@TableName("m_feedback_order") 
public class MFeedbackOrderEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("反馈工单Id")
  private Long id;
 

  @ApiModelProperty("公司Id")
  private Long fkCompanyId;
 

  @ApiModelProperty("平台应用Id")
  private Long fkPlatformId;
 

  @ApiModelProperty("平台应用CODE")
  private String fkPlatformCode;
 

  @ApiModelProperty("平台应用对应的创建用户Id，PARTNER=fk_partner_user_id，AIS=fk_staff_id")
  private Long fkPlatformCreateUserId;
 

  @ApiModelProperty("系统资源Key")
  private String fkResourceKey;
 

  @ApiModelProperty("反馈工单号")
  private String num;
 

  @ApiModelProperty("反馈工单类型Id")
  private Long fkFeedbackOrderTypeId;
 

  @ApiModelProperty("标题")
  private String title;
 

  @ApiModelProperty("内容")
  private String message;
 

  @ApiModelProperty("状态：0待处理/1已回复/2已关闭/3系统自动关闭")
  private Integer status;
 

  @ApiModelProperty("关闭时间")
  private LocalDateTime closeTime;


 

}
