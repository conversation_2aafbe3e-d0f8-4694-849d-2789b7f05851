package com.get.aisplatformcenterap.feign;

import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.SearchBean;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IPlatformCenterClientFallBack implements IPlatformCenterClient{

    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(SearchBean<AgentLabelDto> page) {
        return null;
    }
}
