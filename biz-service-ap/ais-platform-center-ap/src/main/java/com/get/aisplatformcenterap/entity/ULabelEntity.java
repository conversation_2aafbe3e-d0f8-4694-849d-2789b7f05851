package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
@TableName(value ="u_label")
public class ULabelEntity extends BaseEntity {

    @ApiModelProperty(value = "标签类型Id")
    @Column(name = "fk_label_type_id")
    private Long fkLabelTypeId;

    @ApiModelProperty(value = "标签名称，各种语言，独立起对应的标签，对应不同公司区域关系")
    @Column(name = "label_name")
    private String labelName;

    @ApiModelProperty(value = "标签关键字")
    @Column(name = "label_key")
    private String labelKey;

    @ApiModelProperty(value = "标签关键字")
    @Column(name = "icon名称")
    private String iconName;

    @ApiModelProperty(value = "颜色code")
    @Column(name = "color")
    private String color;

    @ApiModelProperty(value = "描述")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
