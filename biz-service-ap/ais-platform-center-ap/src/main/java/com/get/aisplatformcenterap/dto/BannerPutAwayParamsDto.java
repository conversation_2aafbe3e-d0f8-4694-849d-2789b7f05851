package com.get.aisplatformcenterap.dto;

import com.get.aisplatformcenterap.entity.MBannerEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class BannerPutAwayParamsDto extends MBannerEntity {
    @NotNull(message = "上下架ID不能为空")
    @ApiModelProperty("上下架ID")
    private Long[] ids;
    @NotNull(message = "上架/下架 类型不能为空")
    @ApiModelProperty("1下架/2上架")
    private int type;




}
