package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:36
 */

@Data
@TableName("m_feedback_order_reply") 
public class MFeedbackOrderReplyEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("反馈工单回复Id")
  private Long id;
 

  @ApiModelProperty("反馈工单Id")
  private Long fkFeedbackOrderId;
 

  @ApiModelProperty("回复员工Id（若发起者的回复，该字段为NULL）")
  private Long fkStaffId;
 

  @ApiModelProperty("回复内容")
  private String reply;
 

 

}
