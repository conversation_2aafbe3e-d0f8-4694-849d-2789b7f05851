package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:44
 */

@Data
@TableName("u_feedback_order_type") 
public class UFeedbackOrderTypeEntity  extends BaseEntity implements Serializable {

  @ApiModelProperty("反馈工单类型Id")
  private Long id;
 

  @ApiModelProperty("平台应用Id")
  private Long fkPlatformId;
 

  @ApiModelProperty("平台应用CODE")
  private String fkPlatformCode;
 

  @ApiModelProperty("类型名称")
  private String typeName;
 

  @ApiModelProperty("排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

 

}
