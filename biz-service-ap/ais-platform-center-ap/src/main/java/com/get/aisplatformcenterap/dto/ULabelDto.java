package com.get.aisplatformcenterap.dto;

import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

@Data
public class ULabelDto extends BaseEntity {

    /**
     * 标签类型Id
     */
    private Long fkLabelTypeId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签关键字
     */
    private String labelKey;

    /**
     * 颜色code
     */
    private String  color;

    /**
     * icon名称
     */
    private String iconName;

    private String remark;

    /**
     * 排序
     */
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    private Boolean isActive;
}
