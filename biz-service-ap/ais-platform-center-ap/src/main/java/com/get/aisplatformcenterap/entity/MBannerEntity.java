package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * banner后台管理
 * @TableName m_banner
 */
@TableName(value ="m_banner")
@Data
public class MBannerEntity extends BaseEntity implements Serializable {

    @ApiModelProperty("公司Id")
    private Long fkCompanyId;
    @ApiModelProperty("所属产品Id")
    private Long fkPlatformId;
    @ApiModelProperty("平台应用code")
    private String fkPlatformCode;
    @ApiModelProperty("位置Id")
    private Long fkBannerTypeId;
    @ApiModelProperty("主题名称")
    private String name;
    @ApiModelProperty("权重")
    private Integer weight;
    @ApiModelProperty("跳转方式 1自定义页面/2小程序页面/3仅展示图片")
    private Integer jumpMode;
    @ApiModelProperty("跳转url")
    private String jumpUrl;
    @ApiModelProperty("网页标题")
    private String webTitle;
    @ApiModelProperty("网页内容")
    private String webMetaDescription;
    @ApiModelProperty("上架状态 0已下架/1待上架/2 已上架")
    private Integer status;
    @ApiModelProperty("开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startTime;
    @ApiModelProperty("结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endTime;
    @ApiModelProperty("备注")
    private String remark;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}