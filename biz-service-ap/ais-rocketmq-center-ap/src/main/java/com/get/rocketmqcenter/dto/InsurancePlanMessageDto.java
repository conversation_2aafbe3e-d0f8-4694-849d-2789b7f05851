package com.get.rocketmqcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class InsurancePlanMessageDto {

    @ApiModelProperty(value = "应收类型关键字，枚举，如：m_student_offer_item")
    private String fkTypeKey;

    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "保险金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "应收费率%")
    private BigDecimal receivablePlanCommissionRate;

    @ApiModelProperty(value = "应付费率%")
    private BigDecimal payablePlanCommissionRate;


}
