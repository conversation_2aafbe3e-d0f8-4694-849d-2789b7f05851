package com.get.rocketmqcenter.feign;

import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSenderQueueDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@VerifyPermission(IsVerify = false)
public class RocketMqCenterClientFallBack implements IRocKetMqCenterClient{


    @Override
    public void getSystemSendEmail(EmailSystemMQMessageDto emailSystemMQMessageDto) {

    }

    @Override
    public void specifiedPersonSendEmail(EmailCustomMQMessageDto emailCustomMQMessageDto) {

    }

    @Override
    public void setSendEmailTaskToMq(EmailSenderQueueDto emailSenderQueueDto) {

    }
}
