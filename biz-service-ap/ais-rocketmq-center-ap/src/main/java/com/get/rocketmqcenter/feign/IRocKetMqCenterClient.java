package com.get.rocketmqcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSenderQueueDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
/**
 * MQ Feign接口类          fallback = RocKetMqCenterClientFallBack.class
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_ROCKETMQ_CENTER
)
public interface IRocKetMqCenterClient {

    String API_PREFIX = "/feign";


    /**
     * 获取系统发送邮件
     */
    String GET_SYSTEM_SEND_EMAIL = API_PREFIX + "/get-system-send-email";

    /**
     * 指定人发送接受人邮件
     */
    String SPECIFIED_PERSON_SEND_EMAIL = API_PREFIX + "/specified-person-send-email";

    /**
     * 发送邮件任务到MQ
     */
    String SEND_EMAIL_TASK_TO_MQ = API_PREFIX + "/send-email-task-to-mq";

    /**
     * 系统发送邮件
     */
    @PostMapping(GET_SYSTEM_SEND_EMAIL)
    void getSystemSendEmail(@RequestBody EmailSystemMQMessageDto emailSystemMQMessageDto);

    @PostMapping(SPECIFIED_PERSON_SEND_EMAIL)
    void specifiedPersonSendEmail(@RequestBody EmailCustomMQMessageDto emailCustomMQMessageDto );

    @PostMapping(SEND_EMAIL_TASK_TO_MQ)
    void setSendEmailTaskToMq(@RequestBody EmailSenderQueueDto emailSenderQueueDto);
}
