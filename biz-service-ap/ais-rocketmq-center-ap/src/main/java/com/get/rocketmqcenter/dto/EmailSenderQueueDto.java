package com.get.rocketmqcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 邮件任务  MQ队列
 */
@Data
public class EmailSenderQueueDto {


    @ApiModelProperty(value = "邮件任务队列id")
    private Long id;

    @ApiModelProperty(value = "模板类型Key")
    private String fkEmailTypeKey;

    @ApiModelProperty(value = "邮件标题（操作后可更新当次标题，方便定位邮件）")
    private String emailTitle;


    @ApiModelProperty(value = "邮件内容参数（不一定有，看业务场景）")
    private String emailParameter;

    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;

    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;

    @ApiModelProperty(value = "关联业务主键")
    private Long fkTableId;

    @ApiModelProperty(value = "执行状态：-1失败/0待执行/1已分发MQ/2成功")
    private Integer operationStatus;

    @ApiModelProperty(value = "执行时间（最后的执行时间）")
    private Date operationTime;

    @ApiModelProperty(value = "执行次数")
    private Integer operationCount;

    @ApiModelProperty(value = "错误信息，可累加update")
    private String errorMessage;

    @ApiModelProperty(value = "发件人邮箱")
    private String emailFrom;

    @ApiModelProperty(value = "收件人邮箱,英文分号分隔")
    private String emailTo;


}
