package com.get.rocketmqcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自定义邮件发送 MQ队列
 */
@Data
public class EmailCustomMQMessageDto {

    /**
     * 邮件发送器队列Id
     */
    private Long emailSenderQueueId;
    /**
     * 邮件默认编码（如 UTF-8），用于控制邮件内容、主题等文本的字符集。若不设置，默认使用系统编码。
     */
    private String defaultEncoding;
    /**
     * SMTP服务器地址（如 smtp.gmail.com），指定邮件发送服务的主机名。
     */
    private String host;
    /**
     * SMTP服务器端口（如 465 或 587），需与协议（SSL/TLS）匹配。
     */
    private Integer port;
    /**
     * 邮件协议（如 smtp 或 smtps），决定是否启用加密传输。
     */
    private String protocol;
    /**
     * 认证用户名（通常是发件人邮箱地址，如 <EMAIL>）。
     */
    private String userName;
    /**
     * 认证密码（邮箱密码或应用专用密码，如使用 OAuth2 需替换为令牌）。
     */
    private String password;
    /**
     * 邮件标题
     */
    private String title;

    /**
     * 主邮件接收人
     */
    private String[] toEmail;

    /**
     * 抄送人邮件地址，不可为空
     */
    private String[] ccEmail;
    /**
     * 邮件正文
     */
    private String content;
    /**
     * true:启用html  false:纯文本
     */
    private Boolean isHtml;

}
