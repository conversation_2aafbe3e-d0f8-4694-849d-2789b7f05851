package com.get.remindercenter.enums;

import com.get.core.tool.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件语言枚举类
 *
 * <AUTHOR>
 * @date 2025-01-18
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum EmailLanguageEnum {

    /**
     * 中文
     */
    CHINESE("zh", "中文", "Chinese"),

    /**
     * 英文
     */
    ENGLISH("en", "英文", "English");

    /**
     * 语言代码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String chineseMsg;

    /**
     * 英文描述
     */
    private final String englishMsg;

    /**
     * 邮件语言枚举映射表
     */
    private static final Map<String, EmailLanguageEnum> EMAIL_LANGUAGE_MAP = new HashMap<>();

    static {
        for (EmailLanguageEnum enumItem : EmailLanguageEnum.values()) {
            EMAIL_LANGUAGE_MAP.put(enumItem.getCode(), enumItem);
        }
    }

    /**
     * 根据语言代码获取对应的邮件语言枚举实例
     *
     * @param code 语言代码
     * @return 对应的邮件语言枚举实例，如果找不到则返回null
     */
    public static EmailLanguageEnum getEmailLanguageByCode(String code) {
        return EMAIL_LANGUAGE_MAP.get(code);
    }

    /**
     * 根据语言代码获取对应的邮件语言枚举实例，如果找不到则返回默认值（中文）
     *
     * @param code 语言代码
     * @return 对应的邮件语言枚举实例，找不到则返回中文
     */
    public static EmailLanguageEnum getEmailLanguageByCodeWithDefault(String code) {
        EmailLanguageEnum languageEnum = EMAIL_LANGUAGE_MAP.get(code);
        return languageEnum != null ? languageEnum : CHINESE;
    }

    /**
     * 判断是否为英文语言
     *
     * @param code 语言代码
     * @return true表示是英文，false表示不是英文
     */
    public static boolean isEnglish(String code) {
        return ENGLISH.getCode().equals(code);
    }

    /**
     * 判断是否为中文语言
     *
     * @param code 语言代码
     * @return true表示是中文，false表示不是中文
     */
    public static boolean isChinese(String code) {
        return CHINESE.getCode().equals(code);
    }

    /**
     * 根据公司配置值获取语言代码
     * 如果配置值为"en"则返回英文，否则返回中文（包括null和其他值）
     *
     * @param configValue 公司配置值
     * @return 语言代码
     */
    public static String getLanguageCodeByConfigValue(String configValue) {
        if (ENGLISH.getCode().equals(configValue)) {
            return ENGLISH.getCode();
        }
        return CHINESE.getCode();
    }

    /**
     * 根据公司配置值获取邮件语言枚举实例
     * 如果配置值为"en"则返回英文，否则返回中文（包括null和其他值）
     *
     * @param configValue 公司配置值
     * @return 邮件语言枚举实例
     */
    public static EmailLanguageEnum getEmailLanguageByConfigValue(String configValue) {
        if (ENGLISH.getCode().equals(configValue)) {
            return ENGLISH;
        }
        return CHINESE;
    }

    /**
     * 验证语言代码是否有效
     *
     * @param code 语言代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValidLanguageCode(String code) {
        if (StringUtil.isBlank(code)) {
            return false;
        }
        return EMAIL_LANGUAGE_MAP.containsKey(code);
    }

}