package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/26 12:23
 * @desciption:
 */
@Data
@TableName("m_system_email_account")
public class SystemEmailAccount extends BaseEntity {
    @ApiModelProperty(value = "0=推广邮箱账号")
    private Integer type;

    @ApiModelProperty(value = "邮件账号")
    private String emailAccount;

    @ApiModelProperty(value = "邮件密码")
    private String emailPassword;

    @ApiModelProperty(value = "是否激活，0否/1是")
    private Boolean isActive;
}
