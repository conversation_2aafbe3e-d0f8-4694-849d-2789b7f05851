package com.get.remindercenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.remindercenter.entity.RemindTaskQueue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 15:32
 * Date: 2021/12/2
 * Description:提醒队列返回类
 */
@Data
public class RemindTaskQueueVo  extends BaseVoEntity {
    /**
     * 提醒任务id
     */
    @ApiModelProperty(value = "提醒任务id")
    private Long fkRemindTaskId;
    /**
     * 提醒方式：0系统内/1邮件/2短信
     */
    @ApiModelProperty(value = "提醒方式：0系统内/1邮件/2短信")
    private String remindMethod;
    /**
     * 任务执行时间
     */
    @ApiModelProperty(value = "任务执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date optTime;
    /**
     * 错误尝试次数
     */
    @ApiModelProperty(value = "错误尝试次数")
    private Integer tryTimes;
    /**
     * 获取错误信息，累加update
     */
    @ApiModelProperty(value = "获取错误信息，累加update")
    private String tryError;

    /**
     * 任务标题
     */
    @ApiModelProperty(value = "任务标题")
    private String taskTitle;


    /**
     * 任务备注
     */
    @ApiModelProperty(value = "任务备注")
    private String taskRemark;

    /**
     * 关联业务库名
     */
    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;

    /**
     * 关联业务表名
     */
    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;

    /**
     * 关联业务表id
     */
    @ApiModelProperty(value = "关联业务表id")
    private Long fkTableId;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long studentId;

    /**
     * 方案id
     */
    @ApiModelProperty(value = "方案id")
    private Long offerId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Long offerItemId;
}
