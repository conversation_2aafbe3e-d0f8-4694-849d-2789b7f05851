package com.get.remindercenter.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 邮件抽象类
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type" // JSON 中标识类型的字段名
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = OfferItemCommissionNoticeDto.class, name = "commissionNotice"),
        @JsonSubTypes.Type(value = AcceptOfferDeadlineReminderDto.class, name = "acceptOfferDead"),
        @JsonSubTypes.Type(value = WorkLeaveReminderDto.class, name = "workLeaveReminder"),
        // 可添加其他子类映射
})
@Data
public abstract class EmailParamsDto {
    //可以放公共参数
    @ApiModelProperty(value = "邮件任务队列id")
    private Long id;

    @ApiModelProperty(value = "模板类型Key")
    private String fkEmailTypeKey;

    @ApiModelProperty(value = "邮件标题（操作后可更新当次标题，方便定位邮件）")
    private String emailTitle;

    @ApiModelProperty(value = "邮件内容参数（不一定有，看业务场景）")
    private String emailParameter;

    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;

    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;

    @ApiModelProperty(value = "关联业务主键")
    private Long fkTableId;
    @ApiModelProperty(value = "语言,zh/en")
    private String  languageCode;

    @ApiModelProperty(value = "执行时间（最后的执行时间）")
    private Date operationTime;
}
