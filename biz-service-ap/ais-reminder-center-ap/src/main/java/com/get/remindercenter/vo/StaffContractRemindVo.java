package com.get.remindercenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2024/3/12 17:27
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffContractRemindVo  extends BaseVoEntity {

    private String staffFullName;

    private String title;

    private String companyName;

    private String departmentName;

    private String positionName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEndDate;
}
