package com.get.remindercenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.remindercenter.entity.BatchEmailPromotionQueue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/20 12:53
 */
@Data
public class BatchEmailPromotionQueueDto extends BaseVoEntity {

    @ApiModelProperty(value = "email邮箱地址")
    private String email;
    @ApiModelProperty(value = "新闻id")
    private Long fkNewsId;
    @ApiModelProperty(value = "发送类型类型枚举：0不区分/1Hubs/2市场")
    private Integer type;

}
