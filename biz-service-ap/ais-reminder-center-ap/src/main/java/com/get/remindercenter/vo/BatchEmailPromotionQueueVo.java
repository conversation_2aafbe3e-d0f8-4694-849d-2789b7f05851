package com.get.remindercenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BatchEmailPromotionQueueVo extends BaseVoEntity {

    @ApiModelProperty(value = "email邮箱地址")
    private String email;
    @ApiModelProperty(value = "新闻id")
    private Long fkNewsId;
    @ApiModelProperty(value = "发送类型类型枚举：0不区分/1Hubs/2市场")
    private Integer type;
}
