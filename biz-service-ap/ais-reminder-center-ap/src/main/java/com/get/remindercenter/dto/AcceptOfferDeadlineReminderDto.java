package com.get.remindercenter.dto;

//import com.get.salecenter.entity.StudentOfferItem;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 接受Offer截止提醒/支付押金截止提醒/课程开课提醒
 */
@Data
public class AcceptOfferDeadlineReminderDto extends EmailParamsDto{
    //员工id
    private Long fkStaffId;
    //支付截止时间
    private Date depositDeadline;
    //接受Offer截止时间
    private Date acceptOfferDeadline;


    //需要发送的人
    private Set<Long> staffEmailSet;

    //组装的数据
    private Map<String, String> map = new HashMap<>();

    //模板
    private String template;
}
