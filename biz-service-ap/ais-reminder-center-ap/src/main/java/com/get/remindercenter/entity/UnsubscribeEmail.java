package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("m_unsubscribe_email")
public class UnsubscribeEmail extends BaseEntity {

    @ApiModelProperty(value = "退订邮箱地址")
    private String email;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "类型枚举：0全部/1Hubs/2市场")
    private Integer type;

}
