package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_remind_template")
public class RemindTemplate extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 提醒类型Key，必填
     */
    @ApiModelProperty(value = "提醒类型Key，必填")
    private String fkRemindEventTypeKey;
    /**
     * 短信模板，可填写第三方模板id
     */
    @ApiModelProperty(value = "短信模板，可填写第三方模板id")
    private String smsTemplate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 电邮模板
     */
    @ApiModelProperty(value = "电邮模板")
    private String emailTemplate;

    /**
     * 英文模板
     */
    @ApiModelProperty(value = "电邮模板")
    private String emailTemplateEn;


    @ApiModelProperty("邮件标题")
    private String title;
}