package com.get.remindercenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.remindercenter.dto.*;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.vo.*;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_REMINDER_CENTER
)
public interface IReminderCenterClient {

    String API_PREFIX = "/feign";

//	String GET_ALL_TYPE = API_PREFIX + "/get-all-type";
////	@VerifyPermission(IsVerify = false)
//	@GetMapping(GET_ALL_TYPE)
//	Result<List<ContactPersonTypeDto>> getAllType();

    /**
     * 批量新增任务
     */
    String BATCH_ADD = API_PREFIX + "/batch-add";

    /**
     * 批量新增任务
     */
    String ADD = API_PREFIX + "/add";

    /**
     * 发送邮件
     *
     * @Date 10:37 2022/8/22
     * <AUTHOR>
     */
    String SEND_MAIL = API_PREFIX + "/send-mail";

    String SEND_EMAIL = API_PREFIX + "send-email";

    String BATCH_SEND_EMAIL = API_PREFIX +"/batch-send-email";

    String BATCH_SEND_EN_EMAIL = API_PREFIX +"/batch-send-en-email";


    String BATCH_SEND_EMAIL_CUSTOM = API_PREFIX + "/batch-send-email-custom";

    String SEND_EMAIL_TO_STAFF = API_PREFIX + "/send-email-to-staff";

    /**
     * 自定义发送邮件
     *
     * @Date 11:17 2022/8/25
     * <AUTHOR>
     */
    String CUSTOM_SEND_MAIL = API_PREFIX + "/custom-send-mail";
    /**
     * 自定义发送邮件通过BODY
     */
    String CUSTOM_SEND_MAIL_BY_BODY = API_PREFIX + "/custom-send-mail-by-body";
    /**
     * 批量修改任务
     */
    String BATCH_UPDATE = API_PREFIX + "/batch-update";

    String BATCH_UPDATE_NEW = API_PREFIX + "/batch-update-new";

    String BATCH_UPDATE_TASK_NEW = API_PREFIX + "/batch-update-task-new";

    String BATCH_DELETE_TASK_NEW = API_PREFIX + "/batch-delete-task-new";

    String BATCH_ADD_TASK = API_PREFIX + "/batch-add-task";

    String BATCH_UPDATE_IMPORT = API_PREFIX + "/batch-update-import";
    /**
     * 批量修改任务(支持表的多个Id)
     */
    String BATCH_UPDATE_BY_TABLE_IDS = API_PREFIX + "/batch-update-by-table-ids";

    String BATCH_DELETE_BY_TABLE_ID = API_PREFIX + "/batch-delete-by-table-id";
    /**
     * 发送队列提醒
     */
    String PERFORM_TASKS = API_PREFIX + "/performTasks";

    String SEND_EMAIL_TASKS = API_PREFIX + "/send-email-tasks";

    String SEND_SMS = API_PREFIX + "/send-sms";

    String GET_REMIND_TEMPLATE_BY_TYPE_KEY = API_PREFIX + "/get-remind-template-by-type-key";

    String GET_EMAIL_TEMPLATE_BY_TYPE_KEY = API_PREFIX + "/get-email-template-by-type-key";

    String ALIYUN_SEND_MAIL_NEW = API_PREFIX + "/aliyun-send-mail-new";

    String GET_DEFAULT_SEND_EMAIL = API_PREFIX + "/get-default-send-email";

    String ALIYUN_ADD_TAG = API_PREFIX + "/aliyun-add-tag";

    String GET_REMIND_ADD_MSG_2_QUEEN = API_PREFIX + "/aliyun-send-mail-queen";

    String GET_REMIND_GET_EMAIL_QUEEN = API_PREFIX + "/get-remind-get-email-queen";

    String SEND_NEWS_EMAIL = API_PREFIX + "/send-news-email";

    String DEL_MSG_FROM_NEWS_EMAIL_QUEEN = API_PREFIX + "/del-msg-from-news-email-queen";

    String SEND_SYSTEM_MAIL = API_PREFIX + "/send-system-mail";
    String SEND_CUSTOM_MAIL = API_PREFIX + "/send-custom-mail";
    String SEND_MQ_EMAIL = API_PREFIX + "/send-mq-email";

    String SEND_MQ_EMAIL_TASK = API_PREFIX + "/send-mq-email-task";

    String BATCH_ADD_EMAIL_QUEUE = API_PREFIX + "/batch-add-email-queue";

    String UPDATE_EMAIL_QUEUE = API_PREFIX + "/update-email-queue";


    String DELETE_EMAIL_QUEUE_BY_TABLE_ID = API_PREFIX + "/delete-email-queue-by-table-id";


    /**
     * 添加发送新闻的邮件到队列表中
     */
    @PostMapping(GET_REMIND_ADD_MSG_2_QUEEN)
    Result<Boolean> addTask2NewsEmailQueen(@RequestBody AliyunSendMailDto aliyunSendMailVo);


    /**
     * 获取必发邮箱
     * @return
     */
    @GetMapping(GET_DEFAULT_SEND_EMAIL)
    Result<List<String>> getDefaultSendEmail();

    /**
     * 批量新增任务
     * @param remindTaskDtos
     * @return
     */
    @PostMapping(BATCH_ADD)
    Result<Boolean> batchAdd(@RequestBody List<RemindTaskDto> remindTaskDtos);

    /**
     * 批量修改任务
     * @param remindTaskDtos
     * @param fkTableName
     * @param fkTableId
     * @return
     */
    @PostMapping(BATCH_UPDATE)
    Result<Boolean> batchUpdate(@RequestBody List<RemindTaskDto> remindTaskDtos, @RequestParam("fkTableName") String fkTableName,
                                @RequestParam("fkTableId") Long fkTableId);

    /**
     * 批量修改任务
     * @param remindTaskDtos
     * @return
     */
    @PostMapping(BATCH_UPDATE_NEW)
    Result<Boolean> batchUpdateNew(@RequestBody List<RemindTaskDto> remindTaskDtos);

    @PostMapping(BATCH_UPDATE_TASK_NEW)
    Result<Boolean> batchUpdateTaskNew(@RequestBody List<RemindTaskDto> remindTaskDtos);

    @PostMapping(BATCH_DELETE_TASK_NEW)
    Result<Boolean> batchDeleteTaskNew(@RequestBody List<RemindTaskDto> remindTaskDtos);

    /**
     * 批量修改任务
     * @param remindTaskDtos
     * @return
     */
    @PostMapping(BATCH_UPDATE_IMPORT)
    Result<Boolean> batchUpdateImport(@RequestBody List<RemindTaskDto> remindTaskDtos);

    @PostMapping(BATCH_ADD_TASK)
    Result<Boolean> batchAddTask(@RequestBody List<RemindTaskDto> remindTaskDtos);

    /**
     * 批量删除任务
     * @param fkTableName
     * @param fkTableId
     * @param fkRemindEventTypeKeys
     * @return
     */
    @PostMapping(BATCH_DELETE_BY_TABLE_ID)
    Result<Boolean> batchDeleteByTableId(@RequestParam("fkTableName") String fkTableName,
                                         @RequestParam("fkTableId") Long fkTableId,
                                         @RequestParam("fkRemindEventTypeKeys") List<String> fkRemindEventTypeKeys);

    @PostMapping(DELETE_EMAIL_QUEUE_BY_TABLE_ID)
    Result<Boolean> deleteEmailQueueByTableId(@RequestBody RemindTaskDto remindTaskDto);

    /**
     * 批量修改任务(支持表的多个Id)
     * @param remindTaskDtos
     * @param fkTableName
     * @param fkTableIds
     * @return
     */
    @PostMapping(BATCH_UPDATE_BY_TABLE_IDS)
    Result<Boolean> batchUpdateByTableIds(@RequestBody List<RemindTaskDto> remindTaskDtos, @RequestParam("fkTableName") String fkTableName,
                                          @RequestParam("fkTableIds") Set<Long> fkTableIds);

    /**
     * 执行任务
     */
    @PostMapping(PERFORM_TASKS)
    void performTasks();


    @PostMapping(SEND_EMAIL_TASKS)
    void sendEmailScheduleTask();

    /**
     * 发送短信
     * @param smsDto
     * @return
     */
    @PostMapping(SEND_SMS)
    Result<Boolean> sendSms(@RequestBody SmsDto smsDto);

    @PostMapping(ADD)
    Result add(@RequestBody RemindTaskUpdateDto remindTaskUpdateDto);

    /**
     * 发送邮件 PS:如果学习计划为财务专用的学习计划，不能发送任何邮件
     *
     * @Date 10:37 2022/8/22
     * <AUTHOR>
     */
    @PostMapping(SEND_MAIL)
    Result<Boolean> sendMail(@RequestParam("title") String title, @RequestParam("template")String template,
                             @RequestParam("contactEmail")String contactEmail, @RequestParam(value = "ccEmail" ,required = false)String ccEmail);


//    @GetMapping(SEND_EMAIL)
//    void sendEmail(@RequestParam("title") String title,@RequestParam("typeKey") String typeKey,@RequestParam("fkStaffId")Long fkStaffId,@RequestParam("taskRemark") String taskRemark);

    /**
     * 批量发邮件
     * PS:如果学习计划为财务专用的学习计划，不能发送任何邮件
     * @Date 16:49 2024/5/10
     * <AUTHOR>
     */
    @PostMapping(BATCH_SEND_EMAIL)
    void batchSendEmail(@RequestBody List<Map<String,String>> list,@RequestParam("typeKey") String typeKey);

    @PostMapping(BATCH_SEND_EN_EMAIL)
    void batchSendEnEmail(@RequestBody List<Map<String,String>> list,@RequestParam("typeKey") String typeKey,@RequestParam("version")String version);

    @PostMapping(BATCH_SEND_EMAIL_CUSTOM)
    void batchSendEmailCustom(@RequestBody MailDto mailDto);

    @GetMapping(SEND_EMAIL_TO_STAFF)
    Result<Boolean> sendEmailToStaff(@RequestParam("title") String title,@RequestParam("typeKey") String typeKey,@RequestParam("fkStaffId")Long fkStaffId,@RequestParam("taskRemark") String taskRemark);
    /**
     * 自定义发送邮件
     * PS:如果学习计划为财务专用的学习计划，不能发送任何邮件
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @PostMapping(CUSTOM_SEND_MAIL)
    Result<Boolean> customSendMail(@RequestParam("defaultEncoding") String defaultEncoding, @RequestParam("host") String host, @RequestParam("port") int port,
                                   @RequestParam("protocol")String protocol, @RequestParam("userName") String userName, @RequestParam("password") String password,
                                   @RequestParam("title") String title,  @RequestParam("toEmail") String toEmail, @RequestParam("ccEmail") String ccEmail,
                                   @RequestParam("template") String template, @RequestParam("flag") boolean flag);
    /**
     * 自定义发送邮件,传递body，避免邮件内容超长
     * PS:如果学习计划为财务专用的学习计划，不能发送任何邮件
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @PostMapping(CUSTOM_SEND_MAIL_BY_BODY)
    Result<Boolean> customSendMailByBody(@RequestBody MailDto mailDto);

    /**
     * 获取邮件模板
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @PostMapping(GET_REMIND_TEMPLATE_BY_TYPE_KEY)
    Result<RemindTemplate> getRemindTemplateByTypeKey(@RequestParam("typeKey") String typeKey);

    /**
     * 获取新邮件模板
     *
     * @Date 11:16 2022/8/25
     * <AUTHOR>
     */
    @PostMapping(GET_EMAIL_TEMPLATE_BY_TYPE_KEY)
    Result<EmailTemplate> getEmailTemplateByTypeKey(@RequestParam("typeKey") String typeKey);


    /**
     * 阿里云添加邮件标签
     */
    @PostMapping(ALIYUN_ADD_TAG)
    Result<Long> aliyunAddTag(@RequestBody String TagName);

    /**
     * 获取需要新闻的邮件队列
     * @return
     */
    @PostMapping(GET_REMIND_GET_EMAIL_QUEEN)
    @VerifyLogin(IsVerify = false)
    Result<List<BatchEmailPromotionQueueVo>> getSendNewsEamilQueues();

    @PostMapping(SEND_NEWS_EMAIL)
    Result<Boolean> sendNewsEmail(@RequestBody AliyunSendMailDto aliyunSendMailVo);

    @PostMapping(DEL_MSG_FROM_NEWS_EMAIL_QUEEN)
    Result<Boolean> deleteMsgFromQueue(@RequestBody Long id);

    /**
     * 阿里云邮件推送
     *
     * @return
     */
    @PostMapping(ALIYUN_SEND_MAIL_NEW)
    Result<Boolean> aliyunSendMailNew(@RequestBody AliyunSendMailDto aliyunSendMailVo);

    /**
     * 发送系统邮件
     * @param emailSystemMQMessageDto
     * @return
     */
    @PostMapping(SEND_SYSTEM_MAIL)
    Result<Boolean> sendSystemMail(@RequestBody EmailSystemMQMessageDto emailSystemMQMessageDto);

    /**
     * 发送自定义邮件
     * @param emailCustomMQMessageDto
     * @return
     */
    @PostMapping(SEND_CUSTOM_MAIL)
    Result<Boolean> sendCustomMail(@RequestBody EmailCustomMQMessageDto emailCustomMQMessageDto);



    /**
     * MQ异步发送邮件
     * @param
     * @return
     */
    @PostMapping(SEND_MQ_EMAIL)
    //Result<Boolean> sendMqEmail(@RequestBody EmailParamsDto emailParamsDto);
    Result<Boolean> sendMqEmail(@RequestBody EmailSenderQueue emailSenderQueue);


    @PostMapping(SEND_MQ_EMAIL_TASK)
    Result<Boolean> sendMqEmailTask(@RequestBody EmailSenderQueue emailSenderQueue);


    @PostMapping(BATCH_ADD_EMAIL_QUEUE)
    Result<Boolean> batchAddEmailQueue(@RequestBody List<EmailSenderQueue> queueList);


    @PostMapping(UPDATE_EMAIL_QUEUE)
    Result<Boolean> updateEmailQueue(@RequestBody EmailSenderQueue emailSenderQueue);
}
