package com.get.remindercenter.dto;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 通用邮件数据传输对象
 * 支持动态参数的邮件发送
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
public class UniversalEmailDto {

    /**
     * 邮件队列ID
     */
    private Long id;

    /**
     * 邮件模板类型Key
     */
    private String fkEmailTypeKey;

    /**
     * 邮件标题
     */
    private String emailTitle;

    /**
     * 邮件参数（JSON字符串）
     */
    private String emailParameter;

    /**
     * 收件人邮箱
     */
    private String recipientEmail;

    /**
     * 关联数据库名
     */
    private String fkDbName;

    /**
     * 关联表名
     */
    private String fkTableName;

    /**
     * 关联表ID
     */
    private Long fkTableId;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 语言代码（zh/en等）
     */
    private String languageCode;

    /**
     * 动态参数Map
     */
    private Map<String, String> dynamicParameters;

    /**
     * 处理后的邮件内容
     */
    private String processedContent;

    /**
     * 错误信息
     */
    private String errorMessage;

}