package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/26 9:42
 * @desciption: 邮件发送日志
 */
@TableName("log_send_email")
@Data
public class LogSendEmail extends BaseEntity {
    @ApiModelProperty(value = "操作类型")
    private String optType;
    @ApiModelProperty(value = "发件人地址")
    private String fromEmail;
    @ApiModelProperty(value = "收件人地址")
    private String toEmail;
    @ApiModelProperty(value = "邮件标题")
    private String subject;
    @ApiModelProperty(value = "状态：失败0/成功1")
    private Integer status;
    @ApiModelProperty(value = "错误信息")
    private String message;
    @ApiModelProperty(value = "消耗时间（毫秒）")
    private Integer expendTime;
}
