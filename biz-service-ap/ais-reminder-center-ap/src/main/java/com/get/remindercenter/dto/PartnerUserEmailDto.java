package com.get.remindercenter.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 合作伙伴用户邮件发送DTO
 */
@Data
public class PartnerUserEmailDto extends EmailParamsDto {

    /**
     * 邮件模板参数
     */
    private Map<String, String> map = new HashMap<>();

    /**
     * 收件人邮箱地址
     */
    private String recipientEmail;

    /**
     * 收件人姓名
     */
    private String personalName;

    /**
     * 代理名称
     */
    private String agentName;

    /**
     * 账号（邮箱）
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 小程序链接
     */
    private String taskLink;

}