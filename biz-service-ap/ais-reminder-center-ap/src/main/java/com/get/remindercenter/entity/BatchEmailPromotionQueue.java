package com.get.remindercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/20 12:47
 */
@Data
@TableName("m_batch_email_promotion_queue")
@AllArgsConstructor
@NoArgsConstructor
public class BatchEmailPromotionQueue extends BaseEntity {

    @ApiModelProperty(value = "email邮箱地址")
    private String email;
    @ApiModelProperty(value = "新闻id")
    private Long fkNewsId;
    @ApiModelProperty(value = "发送类型类型枚举：0不区分/1Hubs/2市场")
    private Integer type;

}
