package com.get.remindercenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/10 23:05
 */
@Data
public class RemindTaskToAppDto extends BaseVoEntity {

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private String fkStaffId;

    /**
     * 提醒类型Key，必填
     */
    @ApiModelProperty(value = "提醒类型Key，必填")
    private String fkRemindEventTypeKey;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;


    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    /**
     * 提前天数提醒，多选：0,1,3,7（0为当天提醒）
     */
    @ApiModelProperty(value = "提前天数提醒，多选：0,1,3,7（0为当天提醒）")
    private String advanceDays;

    /**
     * 任务标题
     */
    @ApiModelProperty(value = "任务标题")
    private String taskTitle;

    /**
     * 任务备注
     */
    @ApiModelProperty(value = "任务备注")
    private String taskRemark;

    /**
     * 任务背景颜色
     */
    @ApiModelProperty(value = "任务背景颜色")
    private String taskBgColor;

    /**
     * 任务文字颜色
     */
    @ApiModelProperty(value = "任务文字颜色")
    private String taskTxtColor;

    /**
     * 任务链接
     */
    @ApiModelProperty(value = "任务链接")
    private String taskLink;

    /**
     * 提醒方式：0系统内/1邮件/2短信
     */
    @ApiModelProperty(value = "提醒方式：0系统内/1邮件/2短信")
    private String remindMethod;

    /**
     * 任务状态：0取消/1执行中/2停止/3结束
     */
    @ApiModelProperty(value = "任务状态：0取消/1执行中/2停止/3结束")
    private String status;


    /**
     * 关联业务表名
     */
    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;

    /**
     * 关联业务表Id
     */
    @ApiModelProperty(value = "关联业务表Id")
    private String fkTableId;

    /**
     * 关联业务库名
     */
    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;
}
