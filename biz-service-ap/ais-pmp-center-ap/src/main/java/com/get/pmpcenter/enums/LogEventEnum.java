package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 日志事件枚举
 */

@Getter
@AllArgsConstructor
public enum LogEventEnum {

    ADD_INSTITUTION("ADD_INSTITUTION", "添加了", "所学校"),
    DEL_INSTITUTION("DEL_INSTITUTION", "删除了", "所学校"),
    ADD_TERRITORY("ADD_TERRITORY", "添加了", "个地区"),
    DEL_TERRITORY("DEL_TERRITORY", "删除了", "个地区"),
    ADD_COMMISSION("ADD_COMMISSION", "添加了", "个佣金明细"),
    DEL_COMMISSION("DEL_COMMISSION", "删除了", "个佣金明细"),
    UPDATE_COMMISSION("UPDATE_COMMISSION", "修改了", "的佣金明细"),
    ADD_COMBINATION_COMMISSION("ADD_COMBINATION_COMMISSION", "添加了", "个佣金组合"),
    DEL_COMBINATION_COMMISSION("DEL_COMBINATION_COMMISSION", "删除了", "个佣金组合"),
    UPDATE_COMBINATION_COMMISSION("UPDATE_COMBINATION_COMMISSION", "修改了", "的佣金明细"),
    ADD_BONUS_COMMISSION("ADD_BONUS_COMMISSION", "添加了", "个bonus条件"),
    DEL_BONUS_COMMISSION("DEL_BONUS_COMMISSION", "删除了", "个bonus条件"),
    UPDATE_BONUS_COMMISSION("UPDATE_BONUS_COMMISSION", "修改了", "整体bonus条件明细"),
    ADD_PLAN("ADD_PLAN", "添加了", ""),
    DEL_PLAN("DEL_PLAN", "删除了", ""),
    UPDATE_PLAN("UPDATE_PLAN", "修改了", ""),
    RENEW_CONTRACT_ORIGINAL("RENEW_CONTRACT_ORIGINAL", "续签合同,新合同编号为:", ""),
    RENEW_CONTRACT_NEW("RENEW_CONTRACT_NEW", "通过", "续签,创建了此合同记录"),
    ;

    private String code;
    private String prefix;
    private String msg;


    public static LogEventEnum getEnumByCode(String code) {
        for (LogEventEnum value : LogEventEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
