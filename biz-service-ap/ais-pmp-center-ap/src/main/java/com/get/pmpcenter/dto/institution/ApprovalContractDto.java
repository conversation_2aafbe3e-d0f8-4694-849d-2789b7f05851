package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:审核合同DTO
 */
@Data
public class ApprovalContractDto {

    @ApiModelProperty(value = "审批状态：2通过/3拒绝")
    @NotNull(message = "审批状态不能为空")
    private Integer approvalStatus;

    @ApiModelProperty(value = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;

    @ApiModelProperty(value = "审批意见")
    private String approvalComment;
}
