package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/3
 * @Version 1.0
 * @apiNote:
 */
@Data
public class InstitutionAgentPlanDto {

    @ApiModelProperty(value = "分公司id")
    @NotNull(message = "分公司id不能为空")
    private Long companyId;

    @ApiModelProperty(value = "学校id集合")
    @NotNull(message = "学校id不能为空")
    private List<Long> institutionIds;
}
