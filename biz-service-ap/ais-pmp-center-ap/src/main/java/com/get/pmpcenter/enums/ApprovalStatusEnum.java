package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 日志类型
 */

@Getter
@AllArgsConstructor
public enum ApprovalStatusEnum {

    UN_COMMITTED(0, "0未提交"),
    PENDING_APPROVAL(1, "待审批"),
    PASS(2, "通过"),
    REJECT(3, "拒绝"),
    ;

    private Integer code;

    private String msg;

    public static ApprovalStatusEnum getEnumByCode(Integer code) {
        for (ApprovalStatusEnum value : ApprovalStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
