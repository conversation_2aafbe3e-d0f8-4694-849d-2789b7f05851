package com.get.pmpcenter.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/13  10:08
 * @Version 1.0
 * 媒体附件
 */
@Data
public class MediaDto {

    @ApiModelProperty(value = "媒体附件Id-编辑传")
    private Long id;

    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fileGuid;

    @ApiModelProperty(value = "链接")
    private String link;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}
