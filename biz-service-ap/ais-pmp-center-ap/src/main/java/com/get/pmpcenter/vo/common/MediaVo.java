package com.get.pmpcenter.vo.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  15:36
 * @Version 1.0
 * 附件
 */
@Data
public class MediaVo {

    @ApiModelProperty(value = "媒体附件Id")
    private Long id;

    @ApiModelProperty(value = "目标文件名")
    private String fileName;

    @ApiModelProperty(value = "文件guid")
    private String fileGuid;

    @ApiModelProperty(value = "源文件类型")
    private String fileTypeOrc;

    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;

    @ApiModelProperty(value = "目标文件路径")
    private String filePath;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "表Id")
    private String tableId;

    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;

    @ApiModelProperty(value = "链接")
    private String link;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

}
