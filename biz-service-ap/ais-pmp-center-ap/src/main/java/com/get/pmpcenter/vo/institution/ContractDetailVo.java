package com.get.pmpcenter.vo.institution;

import com.get.pmpcenter.dto.institution.SaveProviderCommissionPlanDetailDto;
import com.get.pmpcenter.dto.institution.SaveProviderContractDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:05
 * @Version 1.0
 * 合同详情
 */
@Data
public class ContractDetailVo {

    @ApiModelProperty("合同详情")
    @NotNull(message = "合同详情不能为空")
    private SaveProviderContractDto contract;

//    @ApiModelProperty("佣金方案及明细详情列表")
//    @NotNull(message = "佣金方案及明细详情不能为空")
//    private List<SaveProviderCommissionPlanDetailDto> plans;

    @ApiModelProperty("续签合同ID")
    @NotNull(message = "续签合同ID不能为空")
    private Long renewalContractId;


    public ContractDetailVo() {
        this.contract = new SaveProviderContractDto();
//        this.plans = new ArrayList<>();
    }
}
