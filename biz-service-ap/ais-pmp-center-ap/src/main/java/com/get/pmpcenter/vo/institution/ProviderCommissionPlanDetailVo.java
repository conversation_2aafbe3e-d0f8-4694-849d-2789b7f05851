package com.get.pmpcenter.vo.institution;

import com.get.pmpcenter.dto.institution.PlanTerritoryDto;
import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  15:55
 * @Version 1.0
 * 供应商佣金计划详情
 */
@Data
public class ProviderCommissionPlanDetailVo extends ProviderCommissionPlanVo {

    @ApiModelProperty(value = "佣金方案附件列表")
    private List<MediaVo> commissionPlanMediaList;

    @ApiModelProperty(value = "适用国家列表")
    private List<PlanTerritoryDto> territoryList;

    @ApiModelProperty(value = "适用国家列表-展示")
    private List<PlanTerritoryVo> territoryShowList;
}
