package com.get.pmpcenter.vo.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 学校列表
 */
@Data
public class MajorLevelVo {

    @ApiModelProperty(value = "课程等级Id")
    private Long levelId;

    @ApiModelProperty(value = "等级名称")
    private String customName;

    @ApiModelProperty(value = "等级名称-中文")
    private String customNameChn;

    @ApiModelProperty(value = "是否通用等级：0否/1是")
    private Integer isGeneral;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "父级课程等级Id")
    private Long parentLevelId;

    @ApiModelProperty(value = "父级等级名称")
    private String parentLevelName;

    @ApiModelProperty(value = "父级等级名称-中文")
    private String parentLevelNameChn;

}
