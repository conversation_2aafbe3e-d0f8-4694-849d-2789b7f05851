package com.get.pmpcenter.vo.agent;

import com.get.pmpcenter.dto.agent.UpdateAgentCommissionPlanDto;
import com.get.pmpcenter.vo.common.InstitutionVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/26  18:13
 * @Version 1.0
 * 继承模板的佣金方案明细
 */
@Data
public class ExtendAgentCommissionVo extends AgentCommissionListVo {

    @ApiModelProperty(value = "学校列表")
    private List<InstitutionVo> institutionList;

    @ApiModelProperty(value = "佣金方案基本信息")
    private UpdateAgentCommissionPlanDto updateAgentCommissionPlanDto;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "锁定权限:0-方案未锁定,没有锁定权限;1-方案未锁定,有锁定权限;2-方案已锁定,没有解锁权限;3-方案已锁定,有解锁权限")
    private Integer lockPermission;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核权限:0-方案未提交审核,没有权限提交审核;1-方案未提交审核,有权限提交审核;2-方案已提交审核但没有审核权限;3-方案已提交审核且有审核权限;")
    private Integer approvalPermission;

    @ApiModelProperty(value = "是否有多个方案-返回方案数量")
    private Integer hasManyPlans;

}
