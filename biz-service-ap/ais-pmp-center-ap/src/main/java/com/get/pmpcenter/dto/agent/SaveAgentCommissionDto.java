package com.get.pmpcenter.dto.agent;

import com.get.pmpcenter.vo.agent.AgentCommissionListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:22
 * @Version 1.0
 * 保存佣金明细
 */
@Data
public class SaveAgentCommissionDto extends AgentCommissionListVo {

    @ApiModelProperty(value = "代理佣金方案ID")
    @NotNull(message = "代理佣金方案ID不能为空")
    private Long agentCommissionPlanId;

    @ApiModelProperty(value = "是否解除其他方案的绑定关系-true:解除;false:不解除")
    private Boolean unbind = false;
}
