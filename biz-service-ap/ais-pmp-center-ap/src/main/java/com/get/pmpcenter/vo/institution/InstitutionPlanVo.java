package com.get.pmpcenter.vo.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/3
 * @Version 1.0
 * @apiNote:学校佣金方案展示
 */
@Data
public class InstitutionPlanVo {

    @ApiModelProperty(value = "合同ID")
    private Long contractId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "合同名称")
    private String contractTitle;

    @ApiModelProperty(value = "方案ID")
    private Long planId;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "方案开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;
}
