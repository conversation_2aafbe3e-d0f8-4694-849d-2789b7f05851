package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_major_level_custom")
public class MajorLevelCustom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自定义名称")
    private String customName;

    @ApiModelProperty(value = "自定义名称（中文）")
    private String customNameChn;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否通用等级：0否/1是")
    private Boolean isGeneral;

    @ApiModelProperty(value = "父用户自定义课程等级Id")
    private Long fkMajorLevelCustomIdParent;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "父级课程等级Id")
    @TableField(exist = false)
    private Long parentLevelId;

    @ApiModelProperty(value = "父级等级名称")
    @TableField(exist = false)
    private String parentLevelName;

    @ApiModelProperty(value = "父级等级名称-中文")
    @TableField(exist = false)
    private String parentLevelNameChn;


}
