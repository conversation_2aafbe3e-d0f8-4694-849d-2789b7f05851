package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 佣金方案适用地区规则枚举类
 */

@Getter
@AllArgsConstructor
public enum TerritoryRuleEnum {

    INCLUDE(1, "", ""),
    CASE_BY_CASE(2, "Case by case: ", "Case by case: "),
    ON_SHORE(3, "AU onshore", "AU onshore"),
    EXCEPT(-1, "Global except: ", "全球（除以下地区外）: "),
    UK_SHORE(4, "UK onshore", "UK onshore"),

    GLOBAL(5,"Global","全球"),
            ;

    ;

    private Integer code;

    private String msg;

    private String msgChn;

    public static TerritoryRuleEnum getEnumByCode(Integer code) {
        for (TerritoryRuleEnum value : TerritoryRuleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
