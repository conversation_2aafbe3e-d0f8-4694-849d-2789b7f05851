package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 日志类型
 */

@Getter
@AllArgsConstructor
public enum LogTypeEnum {

    INSERT("INSERT", "新增佣金明细"),
    UPDATE("UPDATE", "更新佣金明细"),
    DELETE("DELETE", "删除佣金明细"),
    INSERT_PLAN("INSERT_PLAN", "新增佣金方案"),
    UPDATE_PLAN("UPDATE_PLAN", "更新佣金方案"),
    DELETE_PLAN("DELETE_PLAN", "删除佣金方案"),
    RENEW_CONTRACT("RENEW_CONTRACT", "续签合同"),
    ;

    private String code;

    private String msg;

    public static LogTypeEnum getEnumByCode(String code) {
        for (LogTypeEnum value : LogTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
