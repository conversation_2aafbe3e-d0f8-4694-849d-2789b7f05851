package com.get.pmpcenter.vo.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 学校标签列表
 */
@Data
public class InstitutionLabelVo {

    @ApiModelProperty(value = "业务标签")
    private List<NameLabel> nameLabels;

    @ApiModelProperty(value = "自定义标签")
    private List<CustomizeLabelVo> customizeLabels;


    public InstitutionLabelVo() {
        this.nameLabels = Collections.emptyList();
        this.customizeLabels = Collections.emptyList();
    }
}
