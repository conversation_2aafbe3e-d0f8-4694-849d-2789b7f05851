package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/26  17:53
 * @Version 1.0
 * 继承模板详情
 */
@Data
public class ExtendTemplateVo {

    @ApiModelProperty(value = "模板类型:1-自定义模板(已经新增过的代理佣金方案);2-学校提供商全局模板")
    private Integer type;

    @ApiModelProperty(value = "学校提供商方案ID")
    private Long providerCommissionPlanId;

    @ApiModelProperty(value = "佣金方案名称")
    private String name;

    @ApiModelProperty(value = "代理佣金方案ID/学校提供商佣金方案:如果type=1表示代理佣金方案ID，type=2表示学校提供商佣金方案ID或者代理佣金方案(全局模板的id)")
    private Long id;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "备注")
    private String remark;
}
