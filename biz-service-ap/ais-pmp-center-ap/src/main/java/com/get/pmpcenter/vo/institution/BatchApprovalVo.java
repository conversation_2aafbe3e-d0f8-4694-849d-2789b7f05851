package com.get.pmpcenter.vo.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/24
 * @Version 1.0
 * @apiNote:
 */
@Data
public class BatchApprovalVo extends InstitutionProviderCommissionPlanApproval {

    @ApiModelProperty("方案名称集合")
    private String planNames;

    @ApiModelProperty(value = "佣金方案下的适用国家/区域规则说明")
    List<PlanTerritoryVo> planTerritoryList;

    @ApiModelProperty(value = "批量修改除适用地区之外的修改信息")
    private BatchApprovalOtherInfoVo approvalOtherInfo;
}
