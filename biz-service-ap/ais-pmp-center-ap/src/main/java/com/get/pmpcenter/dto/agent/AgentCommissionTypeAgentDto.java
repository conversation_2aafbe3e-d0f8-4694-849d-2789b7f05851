package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Alias("AgentCommissionTypeAgentDto")
public class AgentCommissionTypeAgentDto {
    @ApiModelProperty(value = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "学生代理Id")
    private List<Long> fkAgentIds;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * BD绑定的大区ID
     */
    @ApiModelProperty("BD绑定的大区ID")
    private Long fkAreaRegionId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @NotNull(message = "州省Id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;
    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String AgentCode;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String AgentName;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> companyIds;

    @ApiModelProperty(value = "是否绑定 0/未绑定 1/已绑定")
    private Integer isBind;
}
