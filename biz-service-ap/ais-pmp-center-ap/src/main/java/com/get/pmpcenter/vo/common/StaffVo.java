package com.get.pmpcenter.vo.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 员工列表
 */
@Data
public class StaffVo {

    @ApiModelProperty(value = "员工Id")
    private Long staffId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "英文名")
    private String nameEn;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "登录账号")
    private String loginId;

}
