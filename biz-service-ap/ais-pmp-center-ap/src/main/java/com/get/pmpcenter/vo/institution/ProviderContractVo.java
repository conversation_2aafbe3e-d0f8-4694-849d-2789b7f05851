package com.get.pmpcenter.vo.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:05
 * @Version 1.0
 * 学校供应商合同
 */
@Data
public class ProviderContractVo {

    @ApiModelProperty(value = "合同id")
    private Long id;

    @ApiModelProperty(value = "学校佣金合同方Id")
    private Long fkContractPartyId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "合同类型Id")
    private Long fkContractTypeId;

    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    @ApiModelProperty(value = "合同名称")
    private String contractTitle;

    @ApiModelProperty(value = "合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "学校提供商名称")
    private String providerName;

    @ApiModelProperty(value = "学校提供商中文名称")
    private String providerNameChn;

    @ApiModelProperty(value = "合同签订方名称")
    private String contractPartyName;

    @ApiModelProperty(value = "合同签订方中文名称")
    private String contractPartyNameChn;

    @ApiModelProperty(value = "合同备注")
    private String remark;

    @ApiModelProperty(value = "合同创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "当前用户是否有权限操作改合同：true有/false无")
    private Boolean hasPermission;

    @ApiModelProperty(value = "当前用户是否有权限审核合同：1-合同未提交审核;2-无权限审核;3-有权限审核")
    private Integer hasExaminePermission;

    @ApiModelProperty(value = "绑定学校数量")
    private Integer institutionCount = 0;

    @ApiModelProperty(value = "佣金方案审核通过数量")
    private Integer passPlanCount = 0;

    @ApiModelProperty(value = "佣金方案审核中数量")
    private Integer examiningPlanCount = 0;

    @ApiModelProperty(value = "佣金方案未审核数量")
    private Integer pendingPlanCount = 0;

    @ApiModelProperty(value = "佣金方案审核失败数量")
    private Integer rejectPlanCount = 0;

    @ApiModelProperty(value = "创建人")
    private String createUserNames;

    @ApiModelProperty(value = "创建人登录账号")
    private String createUserLoginIds;

    @ApiModelProperty(value = "国家名称")
    private String countryNames;

    @ApiModelProperty(value = "能否续约:true-可以;false-不可以")
    private Boolean canRenewal=Boolean.TRUE;
}
