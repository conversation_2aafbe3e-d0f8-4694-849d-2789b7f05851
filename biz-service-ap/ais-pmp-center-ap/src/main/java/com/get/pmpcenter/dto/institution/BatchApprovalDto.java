package com.get.pmpcenter.dto.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.pmpcenter.vo.institution.BatchApprovalOtherInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/24
 * @Version 1.0
 * @apiNote:批量提交试用地区审核
 */
@Data
public class BatchApprovalDto {

    @ApiModelProperty(value = "佣金方案ID-审核中和非自己创建的不能提交")
    @NotNull(message = "佣金方案ID不能为空")
    private List<Long> planIds;

    @ApiModelProperty(value = "佣金方案下的适用国家/区域规则说明")
    private List<PlanTerritoryDto> planTerritoryList;

    @ApiModelProperty(value = "审核人ID")
    private Long staffId;

    @ApiModelProperty(value = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;

    @ApiModelProperty(value = "提审说明")
    private String submitNote;

    @ApiModelProperty(value = "提交类型:1-仅保存;2:保存并且提交审核")
    @NotNull(message = "提交类型不能为空")
    private Integer submitType;

    @ApiModelProperty(value = "除使用地区之外的其他修改信息")
    private BatchApprovalOtherInfoVo approvalOtherInfo;
}
