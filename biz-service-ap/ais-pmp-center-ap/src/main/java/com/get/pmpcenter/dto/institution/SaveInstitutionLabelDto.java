package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/21
 * @Version 1.0
 * @apiNote:保存学校标签
 */
@Data
public class SaveInstitutionLabelDto {

    @ApiModelProperty(value = "学校ID")
    @NotNull(message = "学校ID不能为空")
    private Long institutionId;

    @ApiModelProperty(value = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @ApiModelProperty(value = "自定义标签ID")
    private List<Long> labelIds;
}
