package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:18
 * @Version 1.0
 * 生效、不生效合同
 */
@Data
public class EnableProviderContractDto {

    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "激活状态不能为空")
    private Integer isActive;


    @ApiModelProperty(value = "合同ID列表")
    @NotNull(message = "合同ID列表不能为空")
    private List<Long> idList;
}
