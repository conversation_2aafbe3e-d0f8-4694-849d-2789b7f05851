package com.get.pmpcenter.vo.agent;

import com.get.institutioncenter.vo.AreaRegionVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

@Data
public class AgentCommissionTypeAgentVo {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
    /**
     * 名字
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * 名字
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @Column(name = "name")
    private String name;

    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    @Column(name = "num")
    private String num;
    /**
     * BD绑定的大区
     */
    @ApiModelProperty("BD绑定的大区")
    private List<AreaRegionVo> areaRegionDtos;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    private String stateName;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 拼接字符：名字备注/公司名称
     */
    @ApiModelProperty(value = "拼接字符：名字备注/公司名称")
    private String nameNoteAndCompanyName;
    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;
    
}
