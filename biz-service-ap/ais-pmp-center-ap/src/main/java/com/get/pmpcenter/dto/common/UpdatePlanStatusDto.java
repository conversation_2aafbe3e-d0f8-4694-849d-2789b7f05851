package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/18
 * @Version 1.0
 * @apiNote:
 */
@Data
public class UpdatePlanStatusDto {

    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "激活状态不能为空")
    private Integer isActive;

    @ApiModelProperty(value = "方案ID")
    @NotNull(message = "方案ID不能为空")
    private Long planId;
}
