package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_institution_provider_commission_major_level_custom")
public class InstitutionProviderCommissionMajorLevelCustom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校提供商佣金Id")
    private Long fkInstitutionProviderCommissionId;

    @ApiModelProperty(value = "用户自定义课程等级Id")
    private Long fkMajorLevelCustomId;
}
