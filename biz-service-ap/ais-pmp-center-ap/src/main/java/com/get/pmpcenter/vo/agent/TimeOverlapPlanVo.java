package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/4
 * @Version 1.0
 * @apiNote:时间重叠展示
 */
@Data
public class TimeOverlapPlanVo {

    @ApiModelProperty(value = "学校提供商Id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "代理方案ID")
    private Long planId;

    @ApiModelProperty(value = "代理方案名称")
    private String planName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "分公司ID")
    private Long companyId;

    @ApiModelProperty(value = "分公司编码")
    private String companyNum;

    @ApiModelProperty(value = "学校名称,多个用逗号拼接")
    private String institutionNames;

    @ApiModelProperty(value = "学校ID集合")
    private List<Long> institutionIds;
}
