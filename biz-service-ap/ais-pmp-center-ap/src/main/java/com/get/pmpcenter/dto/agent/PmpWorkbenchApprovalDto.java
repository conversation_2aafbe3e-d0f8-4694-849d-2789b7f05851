package com.get.pmpcenter.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/5/14
 * @Version 1.0
 * @apiNote:pmp工作台审批dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PmpWorkbenchApprovalDto {

    @ApiModelProperty(value = "当前登录员工ID-前端不用传")
    private Long staffId;

    @ApiModelProperty(value = "审核类型")
    private String approvalType;

    @ApiModelProperty(value = "审核状态:0-待审核,3-审核不通过")
    private Integer approvalStatus;

    @ApiModelProperty(value = "当前登录账号-前端不用传")
    private String loginId;
}
