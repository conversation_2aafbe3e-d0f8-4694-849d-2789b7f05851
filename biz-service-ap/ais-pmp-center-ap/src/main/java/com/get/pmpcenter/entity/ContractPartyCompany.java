package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_contract_party_company")
public class ContractPartyCompany extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型名称")
    private Long fkContractPartyId;

    @ApiModelProperty(value = "类型Key，枚举类型Key")
    private Long fkCompanyId;

}
