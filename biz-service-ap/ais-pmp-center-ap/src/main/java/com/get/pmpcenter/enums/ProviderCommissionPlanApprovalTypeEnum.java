package com.get.pmpcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 学校提供商佣金方案审批类型
 */

@Getter
@AllArgsConstructor
public enum ProviderCommissionPlanApprovalTypeEnum {

    SINGLE("single", "单个审核"),
    MASS("mass", "批量审核"),
    ;

    private String code;

    private String msg;

    public static ProviderCommissionPlanApprovalTypeEnum getEnumByCode(String code) {
        for (ProviderCommissionPlanApprovalTypeEnum value : ProviderCommissionPlanApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
