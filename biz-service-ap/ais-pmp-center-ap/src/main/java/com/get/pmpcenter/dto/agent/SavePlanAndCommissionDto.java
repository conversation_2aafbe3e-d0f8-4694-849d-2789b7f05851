package com.get.pmpcenter.dto.agent;

import com.get.pmpcenter.vo.agent.AgentCommissionListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:22
 * @Version 1.0
 * 保存佣金明细
 */
@Data
public class SavePlanAndCommissionDto extends AgentCommissionListVo {

    @ApiModelProperty(value = "佣金方案基本信息")
    @NotNull(message = "佣金方案基本信息不能为空")
    private UpdateAgentCommissionPlanDto updateAgentCommissionPlanDto;


    @ApiModelProperty(value = "继承模板类型-1:自定义模板;2:学校合同提供商默认模板-继承模板获取")
    @NotNull(message = "继承模板类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "继承模板ID-从继承模板获取")
    @NotNull(message = "继承模板方案ID不能为空")
    private Long id;

    @ApiModelProperty(value = "1:通过列表保存2:通过添加按钮保存")
    @NotNull(message = "保存方式不能为空")
    private Integer saveType;

    @ApiModelProperty(value = "是否解除其他方案的绑定关系-true:解除;false:不解除")
    private Boolean unbind = false;
}
