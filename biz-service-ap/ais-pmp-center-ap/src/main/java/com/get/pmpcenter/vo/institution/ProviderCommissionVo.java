package com.get.pmpcenter.vo.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/4/7
 * @Version 1.0
 * @apiNote:
 */
@Data
public class ProviderCommissionVo extends ProviderCommissionListVo{

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "锁定权限:0-方案未锁定,没有锁定权限;1-方案未锁定,有锁定权限;2-方案已锁定,没有解锁权限;3-方案已锁定,有解锁权限")
    private Integer lockPermission;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核权限:0-方案未提交审核,没有权限提交审核;1-方案未提交审核,有权限提交审核;2-方案已提交审核但没有审核权限;3-方案已提交审核且有审核权限;")
    private Integer approvalPermission;

    @ApiModelProperty(value = "当前登录账号")
    private String currentLoginId;

    @ApiModelProperty(value = "审批中的审批类型(仅当方案在审批中才会有值):single-佣金明细审批;mass-批量审批")
    private String pendingApprovalType;

}
