package com.get.pmpcenter.vo.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/5/21
 * @Version 1.0
 * @apiNote:自定义标签
 */
@Data
public class CustomizeLabelVo {

    @ApiModelProperty(value = "自定义标签Id")
    private Long labelId;

    @ApiModelProperty(value = "自定义标签类型Id")
    private Long labelTypeId;

    @ApiModelProperty(value = "自定义标签类型名称")
    private String labelTypeName;

    @ApiModelProperty(value = "自定义标签类型Key")
    private String labelTypeKey;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签关键字")
    private String labelKey;

    @ApiModelProperty(value = "颜色code")
    private String color;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "icon名称")
    private String icoName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
