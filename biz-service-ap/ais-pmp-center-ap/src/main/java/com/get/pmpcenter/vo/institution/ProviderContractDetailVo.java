package com.get.pmpcenter.vo.institution;

import com.get.pmpcenter.vo.common.InstitutionVo;
import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  15:34
 * @Version 1.0
 * 供应商合同详情
 */
@Data
public class ProviderContractDetailVo extends ProviderCommissionPlanListVo {

    @ApiModelProperty(value = "合同基本信息")
    private ProviderContractVo contractInfo;

    @ApiModelProperty(value = "合同附件列表")
    private List<MediaVo> contracMediaList;

    @ApiModelProperty(value = "关联学校ID集合")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "学校列表")
    private List<InstitutionVo> institutionList;

    @ApiModelProperty(value = "是否有批量审核的数据")
    private Boolean hasBatchApprovalList = Boolean.FALSE;

}
