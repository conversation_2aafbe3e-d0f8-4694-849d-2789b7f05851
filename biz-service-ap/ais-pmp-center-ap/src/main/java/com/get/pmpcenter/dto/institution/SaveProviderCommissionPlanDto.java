package com.get.pmpcenter.dto.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.pmpcenter.dto.common.MediaDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:22
 * @Version 1.0
 * 保存合同
 */
@Data
public class SaveProviderCommissionPlanDto {

    @ApiModelProperty(value = "佣金方案id-编辑/复制传")
    private Long id;

    @ApiModelProperty(value = "合同供应商Id(学校提供商Id)")
    @NotNull(message = "合同供应商Id不能为空")
    private Long institutionProviderId;

    @ApiModelProperty(value = "合同Id")
    @NotNull(message = "合同ID不能为空")
    private Long institutionProviderContractId;

    @ApiModelProperty(value = "方案名称")
    @NotBlank(message = "方案名称不能为空")
    private String name;

    @ApiModelProperty(value = "方案开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "方案开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "方案结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制(是否长期生效)：0否/1是")
    @NotNull(message = "有效时间是否无时间限制不能为空")
    private Integer isTimeless = 0;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive = 1;

    @ApiModelProperty(value = "合同备注")
    private String remark;

    @ApiModelProperty(value = "佣金方案适用学生说明-英文")
    private String territory;

    @ApiModelProperty(value = "佣金方案适用学生说明-中文")
    private String territoryChn;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-英文")
    private String summary;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-中文")
    private String summaryChn;

    @ApiModelProperty(value = "佣金方案专业说明-英文")
    private String course;

    @ApiModelProperty(value = "佣金方案专业说明（中文）")
    private String courseChn;

    @ApiModelProperty(value = "佣金方案附件文件集合")
    private List<MediaDto> mediaList;

    @ApiModelProperty(value = "适用国家列表")
    private List<PlanTerritoryDto> territoryList;

}
