package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_agent_commission_type")
public class AgentCommissionType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id（每个区域分公司都有自己的分类及命名）")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "分类名称，如：1级代理，2级代理")
    @Column(name = "type_name")
    private String typeName;

    @ApiModelProperty(value = "佣金比例")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，当设置屏蔽后，其绑定对应的佣金模板也会失效")
    @Column(name = "is_active")
    private Integer isActive;

    @ApiModelProperty(value = "是否只显示等级佣金：0否/1是")
    private Integer isShowOnly;

    @ApiModelProperty(value = "默认佣金比例，按国家线分")
    @Column(name = "commission_rate_default ")
    private String commissionRateDefault ;
}
