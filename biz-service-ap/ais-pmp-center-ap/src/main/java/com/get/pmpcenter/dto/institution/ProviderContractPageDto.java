package com.get.pmpcenter.dto.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:46
 * @Version 1.0
 */
@Data
public class ProviderContractPageDto {

    @ApiModelProperty(value = "合同供应商名称(合同甲方)")
    private String institutionProviderName;

    @ApiModelProperty(value = "合同供应商ID")
    private Long institutionProviderId;

    @ApiModelProperty(value = "合同签订方名称(合同乙方)")
    private String contractPartyName;

    @ApiModelProperty(value = "合同名称/学校名称/供应商名称")
    private String contractTitle;

    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    @ApiModelProperty(value = "合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "合同开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "是否上架：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "审核状态:0未提交审核/1待审核/2审核通过/3审核不通过")
    private Integer approvalStatus;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "国家ID")
    private List<Long> countryIds;
}
