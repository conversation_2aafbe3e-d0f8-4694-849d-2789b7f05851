package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_major_level_custom_major_level")
public class MajorLevelCustomMajorLevel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户自定义课程等级Id")
    private Long fkMajorLevelCustomId;

    @ApiModelProperty(value = "系统定义课程等级Id")
    private Long fkMajorLevelId;
}
