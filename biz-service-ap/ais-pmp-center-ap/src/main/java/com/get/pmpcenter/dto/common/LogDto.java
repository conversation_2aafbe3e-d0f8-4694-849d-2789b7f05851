package com.get.pmpcenter.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:28
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogDto {

    @ApiModelProperty("操作类型")
    private String action;

    @ApiModelProperty("操作描述")
    private String desc;

    @ApiModelProperty("操作人")
    private String loginId;

    @ApiModelProperty("关联的表名")
    private String tableName;

    @ApiModelProperty("关联的表ID")
    private Long tableId;

    @ApiModelProperty("学校提供商合同Id")
    private Long fkInstitutionProviderContractId;
}
