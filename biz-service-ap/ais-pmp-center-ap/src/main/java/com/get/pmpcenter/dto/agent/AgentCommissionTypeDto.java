package com.get.pmpcenter.dto.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AgentCommissionTypeDto {
    @ApiModelProperty
    private Long id;

    @ApiModelProperty(value = "公司Id（每个区域分公司都有自己的分类及命名）")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司Ids")
    private List<Long> companyIds;

//    @ApiModelProperty(value = "公司名 简称")
//    private String companyName;

    @ApiModelProperty(value = "分类名称，如：1级代理，2级代理")
    private String typeName;

    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，当设置屏蔽后，其绑定对应的佣金模板也会失效")
    private Integer isActive;
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "是否只显示等级佣金：0否/1是")
    private Integer isShowOnly;

    @ApiModelProperty(value = "默认佣金比例，按国家线分-json格式:示例：{\"GBR\":80,\"AUS\":60,\"USA\":60,\"CAN\":75,\"NZL\":70,\"DEFAULT\":60}")
    private String commissionRateDefault ;
}
