package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 差旅报销费用类型
 */
@Data
@TableName("u_travel_claim_fee_type")
public class TravelClaimFeeType extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}