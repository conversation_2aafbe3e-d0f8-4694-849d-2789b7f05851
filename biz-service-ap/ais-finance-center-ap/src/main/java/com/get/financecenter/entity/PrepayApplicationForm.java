package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 借款申请单
 */
@Data
@TableName("m_prepay_application_form")
public class PrepayApplicationForm extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;
    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;
    /**
     * 关联撤销表单Id
     */
    @ApiModelProperty(value = "关联撤销表单Id")
    private Long fkPrepayApplicationFormIdRevoke;
    /**
     * 借款申请单编号（系统生成）
     */
    @ApiModelProperty(value = "借款申请单编号（系统生成）")
    private String num;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 借款金额
     */
    @ApiModelProperty(value = "借款金额")
    private BigDecimal amount;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 通知员工Ids，支持多选（英文逗号分隔：1,2,3）
     */
    @ApiModelProperty(value = "通知员工Ids，支持多选（英文逗号分隔：1,2,3）")
    private String fkStaffIdsNotice;
    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

}