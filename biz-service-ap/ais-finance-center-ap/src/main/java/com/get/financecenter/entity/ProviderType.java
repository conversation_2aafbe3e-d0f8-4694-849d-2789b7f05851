package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_provider_type")
public class ProviderType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 供应商类型名称
     */
    @ApiModelProperty(value = "供应商类型名称")
    private String typeName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}