package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@TableName("u_receipt_method_type")
public class ReceiptMethodType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    /**
     * 科目id
     */
    @ApiModelProperty(value = "科目id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


}

