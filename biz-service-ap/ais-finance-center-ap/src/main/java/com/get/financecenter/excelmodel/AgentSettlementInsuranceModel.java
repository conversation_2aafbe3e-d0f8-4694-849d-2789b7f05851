package com.get.financecenter.excelmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 留学保险模板
 *
 * <AUTHOR>
 * @date 2022/1/15 15:46
 */
@Data
public class AgentSettlementInsuranceModel {

    @ApiModelProperty(value = "应付计划id")
    private Long id;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "保险金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrency;

    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "折合已收金额")
    private BigDecimal paid;

    @ApiModelProperty(value = "本次结算金额")
    private BigDecimal outstanding;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    private String description;
}
