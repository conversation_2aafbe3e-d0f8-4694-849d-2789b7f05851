package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("r_invoice_type_key_target")
public class InvoiceTarget extends BaseEntity {

    @ApiModelProperty("发票id")
    private Long fkInvoiceId;

    @ApiModelProperty("渠道")
    private String fkTypeKey;

    @ApiModelProperty("渠道对象id")
    private Long fkTypeTargetId;
}
