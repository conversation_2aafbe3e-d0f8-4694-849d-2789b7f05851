package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_payment_form")
public class PaymentForm extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 目标类型关键字，枚举：m_agent代理
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_agent代理")
    private String fkTypeKey;
    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private Long fkTypeTargetId;
    /**
     * 银行帐号Id（代理/供应商）
     */
    @ApiModelProperty(value = "银行帐号Id（代理/供应商）")
    private Long fkBankAccountId;
    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountIdCompany;
    /**
     * 付款费用类型id
     */
    @ApiModelProperty(value = "付款费用类型id")
    private Long fkPaymentFeeTypeId;
    /**
     * 付款单编号（系统生成）
     */
    @ApiModelProperty(value = "付款单编号（系统生成）")
    private String numSystem;

    @ApiModelProperty(value = "实付汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "汇率调整金额（可正可负）")
    @UpdateWithNull
    private BigDecimal amountExchangeRate;

    /**
     * 付款单编号（凭证号）
     */
    @ApiModelProperty(value = "付款单编号（凭证号）")
    private String numBank;
    /**
     * 付款日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "付款日期")
    private Date paymentDate;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 付款总金额（含手续费）
     */
    @ApiModelProperty(value = "付款总金额（含手续费）")
    private BigDecimal amount;
    /**
     * 汇率（港币）
     */
    @ApiModelProperty(value = "汇率（港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 付款总金额（港币，含手续费）
     */
    @ApiModelProperty(value = "付款总金额（港币，含手续费）")
    private BigDecimal amountHkd;
    /**
     * 汇率（人民币）
     */
    @ApiModelProperty(value = "汇率（人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 付款总金额（人民币，含手续费）
     */
    @ApiModelProperty(value = "付款总金额（人民币，含手续费）")
    private BigDecimal amountRmb;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;
}