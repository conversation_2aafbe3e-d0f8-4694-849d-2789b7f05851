package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.Position;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/6/30
 * @TIME: 16:17
 **/
@Data
public class PositionVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 员工id
     */
    @ApiModelProperty("员工id")
    private Long fkStaffId;

    //====================

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    @Column(name = "fk_department_id")
    private Long fkDepartmentId;

    /**
     * 职位编号
     */
    @ApiModelProperty(value = "职位编号")
    @Column(name = "num")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 职位等级
     */
    @ApiModelProperty(value = "职位等级")
    @Column(name = "pos_level")
    private String posLevel;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
