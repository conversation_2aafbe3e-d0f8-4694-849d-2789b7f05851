package com.get.permissioncenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MInstitutionPermissionGroupVo {

    /**
     * 学校权限组别Id
     */
    @ApiModelProperty(value = "学校权限组别Id")
    private Long id;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 学校权限组别编号
     */
    @ApiModelProperty(value = "学校权限组别编号")
    private String groupNum;

    /**
     * 学校权限组别名称
     */
    @ApiModelProperty(value = "学校权限组别名称")
    private String groupName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateUser;
    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;
    @ApiModelProperty(value = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
