package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.PermissionGroupGradeName;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2021/4/30
 * @TIME: 17:16
 * @Description:
 **/
public class PermissionGroupGradeNameVo extends BaseEntity {

    //============实体类PermissionGroupGradeName====================
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty(value = "权限组别Id")
    @Column(name = "fk_permission_group_id")
    private Long fkPermissionGroupId;
    /**
     * 权限级别Id
     */
    @ApiModelProperty(value = "权限级别Id")
    @Column(name = "fk_permission_grade_id")
    private Long fkPermissionGradeId;
    /**
     * 权格名称
     */
    @ApiModelProperty(value = "权格名称")
    @Column(name = "permission_name")
    private String permissionName;
}
