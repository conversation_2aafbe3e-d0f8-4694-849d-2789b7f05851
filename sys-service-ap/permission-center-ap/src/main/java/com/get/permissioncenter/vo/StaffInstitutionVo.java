package com.get.permissioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2024/1/29 10:45
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffInstitutionVo {

    @NotNull(message = "员工Id不能为空")
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @NotNull(message = "学校Id不能为空")
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

}
