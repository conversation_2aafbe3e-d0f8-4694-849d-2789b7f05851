package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.Department;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/6/28
 * @TIME: 16:46
 **/
@Data
public class DepartmentVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //========实体类Department===========
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @Column(name = "num")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
