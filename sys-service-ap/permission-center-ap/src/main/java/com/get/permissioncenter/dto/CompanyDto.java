package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 11:50
 **/
@Data
public class CompanyDto extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 父公司Id
     */
    @ApiModelProperty(value = "父公司Id", required = true)
    private Long fkParentCompanyId;

    /**
     * 公司编号
     */
    @NotBlank(message = "公司编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司编号", required = true)
    private String num;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司名称", required = true)
    private String name;

    /**
     * 公司中文名称
     */
    @NotBlank(message = "公司中文名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司中文名称", required = true)
    private String nameChn;

    /**
     * 公司简称
     */
    @ApiModelProperty(value = "公司简称")
    private String shortName;

    /**
     * 公司中文简称
     */
    @ApiModelProperty(value = "公司中文简称")
    private String shortNameChn;

    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;


    /**
     * 公司图标id
     */
    @ApiModelProperty(value = "公司图标附件id")
    private Long fkMediaId;

}
