package com.get.permissioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/6  16:38
 * @Version 1.0
 */
@Data
public class ResetPasswordDto {

    @ApiModelProperty(value = "登录账号", required = true)
    @NotNull(message = "登录账号能为空")
    private String loginId;

    @ApiModelProperty(value = "登录账号的邮箱", required = true)
    @NotNull(message = "邮箱不能为空")
    private String email;
}
