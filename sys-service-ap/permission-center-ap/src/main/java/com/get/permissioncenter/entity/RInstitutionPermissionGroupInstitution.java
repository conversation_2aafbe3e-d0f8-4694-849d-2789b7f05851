package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_institution_permission_group_institution")
public class RInstitutionPermissionGroupInstitution extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学校权限组别Id
     */
    private Long fkInstitutionPermissionGroupId;

    /**
     * 学校Id
     */
    private Long fkInstitutionId;
}