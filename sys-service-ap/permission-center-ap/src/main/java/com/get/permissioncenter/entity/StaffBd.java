package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_staff_bd")
public class StaffBd extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "BDId")
    private Long fkStaffIdBd;

}
