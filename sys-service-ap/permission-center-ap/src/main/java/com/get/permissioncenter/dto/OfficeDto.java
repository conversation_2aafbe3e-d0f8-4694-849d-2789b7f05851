package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 18:24
 **/
@Data
public class OfficeDto extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id", required = true)
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    /**
     * 办公室编号
     */
    @ApiModelProperty(value = "办公室编号", required = true)
    @NotBlank(message = "办公室编号不能为空", groups = {Add.class, Update.class})
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "办公室名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 热线
     */
    @ApiModelProperty(value = "热线")
    private String hotline;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    private String fax;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    private String keyWord;


}
