package com.get.permissioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2024/1/29 12:08
 * @verison: 1.0
 * @description:
 */
@Data
public class BusinessSchoolVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校id")
    private Long id;

    @ApiModelProperty(value = "学校编号")
    private String num;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校类型")
    private String institutionTypeName;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校类型id")
    private Long fkInstitutionTypeId;
}
