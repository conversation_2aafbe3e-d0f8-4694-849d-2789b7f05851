package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_staff_company")
public class StaffCompany extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "节点模式：0父/1子（以员工当前分公司作为根节点作为参考对象）")
    private Integer nodeMode;


}