package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_institution_permission_group")
public class MInstitutionPermissionGroup extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 学校权限组别编号
     */
    private String groupNum;

    /**
     * 学校权限组别名称
     */
    private String groupName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    private Integer viewOrder;

}