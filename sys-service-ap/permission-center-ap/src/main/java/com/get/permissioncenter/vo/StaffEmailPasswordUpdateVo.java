package com.get.permissioncenter.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:06
 */
@Data
public class StaffEmailPasswordUpdateVo {

    @NotNull(message = "员工Id不能为空")
    @ApiModelProperty("员工Id")
    private Long staffId;

    @NotNull(message = "邮箱不能为空")
    @ApiModelProperty(value = "邮箱")
    private String email;

    @NotNull(message = "邮箱密码不能为空")
    @ApiModelProperty(value = "邮箱密码")
    private String emailPassword;

}
