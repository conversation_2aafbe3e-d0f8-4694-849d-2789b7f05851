package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

@ApiModel("权限资源系统配置返回类")
@Data
public class PermissionGroupGradeResourceVo extends BaseEntity {

    @ApiModelProperty(value = "权限组别和权限等级对应数组")
    List<GroupGradeResourceVo> GroupGradeResourceDto;
    @ApiModelProperty(value = "纵坐标权限等级")
    List<PermissionGrade> permissionGrades;
    @ApiModelProperty(value = "横坐标权限组别")
    List<PermissionGroup> permissionGroups;

    //==========实体类PermissionGroupGradeResource==================
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限组别Id")
    @Column(name = "fk_permission_group_id")
    private Long fkPermissionGroupId;
    /**
     * 权限级别Id
     */
    @ApiModelProperty("权限级别Id")
    @Column(name = "fk_permission_grade_id")
    private Long fkPermissionGradeId;
    /**
     * 系统资源Key
     */
    @ApiModelProperty("系统资源Key")
    @Column(name = "fk_resource_key")
    private String fkResourceKey;

}
