package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ConfigDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 配置分类
     */
    @ApiModelProperty(value = "配置分类")
    @NotBlank(message = "配置分类不能为空", groups = {Add.class, Update.class})
    private String configGroup;
    /**
     * 配置主键（唯一）
     */
    @ApiModelProperty(value = "配置主键")
    @NotBlank(message = "配置主键不能为空", groups = {Add.class, Update.class})
    private String configKey;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @NotBlank(message = "描述不能为空", groups = {Add.class, Update.class})
    private String description;
    /**
     * 数值一
     */
    @ApiModelProperty(value = "数值一")
    private String value1;
    /**
     * 数值二
     */
    @ApiModelProperty(value = "数值二")
    private String value2;
    /**
     * 数值三
     */
    @ApiModelProperty(value = "数值三")
    private String value3;
    /**
     * 数值四
     */
    @ApiModelProperty(value = "数值四")
    private String value4;
    @ApiModelProperty(value = "关键词")
    private String keyWord;
}
