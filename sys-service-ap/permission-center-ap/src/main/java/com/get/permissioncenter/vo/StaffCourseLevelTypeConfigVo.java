package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2024/3/15
 * @TIME: 18:08
 * @Description:
 **/
@Data
public class StaffCourseLevelTypeConfigVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "员工Id，=0为默认值")
    private Long fkStaffId;

    @ApiModelProperty(value = "自定义名称")
    private String customName;

    @ApiModelProperty(value = "包含的课程等级/包含的课程类型")
    private String name;

    @ApiModelProperty(value = "类型表名：u_major_level/u_course_type_group")
    private String fkTableName;

    @ApiModelProperty(value = "表Ids：1,2,3")
    private String fkTableIds;

    @ApiModelProperty(value = "是否应用")
    private Boolean isChecked;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
