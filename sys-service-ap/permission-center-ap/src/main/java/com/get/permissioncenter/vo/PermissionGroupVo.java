package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.PermissionGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
@ApiModel("权限组别返回类")
public class PermissionGroupVo extends BaseEntity {
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    //==========实体类PermissionGroup================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 权限组别编号
     */
    @ApiModelProperty(value = "权限组别编号")
    @Column(name = "group_num")
    private String groupNum;
    /**
     * 权限组别名称
     */
    @ApiModelProperty(value = "权限组别名称")
    @Column(name = "group_name")
    private String groupName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
