package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_institution_permission_group_staff")
public class RInstitutionPermissionGroupStaff extends BaseEntity implements Serializable {

    /**
     * 学校权限组别Id
     */
    private Long fkInstitutionPermissionGroupId;

    /**
     * 员工Id
     */
    private Long fkStaffId;
}