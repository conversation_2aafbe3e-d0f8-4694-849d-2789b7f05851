package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.BatchModifyConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/2/23 11:26
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchModifyConfigVo extends BaseEntity {
    /**
     * 排序字段：true要排序，false:不要排序 提供给前端特殊使用
     */
    @ApiModelProperty(value = "排序字段：true要排序，false:不要排序 提供给前端特殊使用")
    private boolean sortFlag;

    @ApiModelProperty(value = "降序")
    private String desc;

    @ApiModelProperty(value = "升序")
    private String asc;

    //============实体类BatchModifyConfig=============
    private static final long serialVersionUID = 1L;

    /**
     * 库名
     */
    @ApiModelProperty(value = "库名")
    @Column(name = "fk_db_name")
    private String fkDbName;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 关联字段名
     */
    @ApiModelProperty(value = "关联字段名")
    @Column(name = "fk_key_column_name")
    private String fkKeyColumnName;

    /**
     * 关系类型：0单表关联/1聚合关联
     */
    @ApiModelProperty(value = "关系类型：0单表关联/1聚合关联")
    @Column(name = "relation_type")
    private Integer relationType;

    /**
     * 子表名
     */
    @ApiModelProperty(value = "子表名")
    @Column(name = "fk_sub_table_name")
    private String fkSubTableName;

    /**
     * 关联子表字段名
     */
    @ApiModelProperty(value = "关联子表字段名")
    @Column(name = "fk_sub_key_column_name")
    private String fkSubKeyColumnName;

    /**
     * 目标字段名
     */
    @ApiModelProperty(value = "目标字段名")
    @Column(name = "fk_target_column_name")
    private String fkTargetColumnName;

    /**
     * 字段类型：0参数/1显示/2编辑
     */
    @ApiModelProperty(value = "字段类型：0参数/1显示/2编辑")
    @Column(name = "column_type")
    private Integer columnType;

    /**
     * 字段标题名
     */
    @ApiModelProperty(value = "字段标题名")
    @Column(name = "column_title")
    private String columnTitle;

    /**
     * 输入类型：负数为联动，同值为一组/0文本/1下拉单选/2下拉多选/3数字/4日期
     */
    @ApiModelProperty(value = "输入类型：负数为联动，同值为一组/0文本/1下拉单选/2下拉多选/3数字/4日期")
    @Column(name = "input_type")
    private Integer inputType;

    /**
     * 数据源api接口
     */
    @ApiModelProperty(value = "数据源api接口")
    @Column(name = "input_src_api")
    private String inputSrcApi;

    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    @Column(name = "max_length")
    private Integer maxLength;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    @Column(name = "is_required")
    private Boolean isRequired;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
