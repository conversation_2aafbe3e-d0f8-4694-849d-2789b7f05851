package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.Config;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@ApiModel("系统配置返回类")
@Data
public class ConfigVo extends BaseEntity {

    //=========实体类Config===========
    private static final long serialVersionUID = 1L;
    /**
     * 配置分类
     */
    @ApiModelProperty("配置分类")
    @Column(name = "config_group")
    private String configGroup;
    /**
     * 配置主键（唯一）
     */
    @ApiModelProperty("配置主键")
    @Column(name = "config_key")
    private String configKey;
    /**
     * 描述
     */
    @ApiModelProperty("配置描述")
    @Column(name = "description")
    private String description;
    /**
     * 数值一
     */
    @ApiModelProperty("数值一")
    private String value1;
    /**
     * 数值二
     */
    @ApiModelProperty("数值二")
    private String value2;
    /**
     * 数值三
     */
    @ApiModelProperty("数值三")
    private String value3;
    /**
     * 数值四
     */
    @ApiModelProperty("数值四")
    private String value4;
}
