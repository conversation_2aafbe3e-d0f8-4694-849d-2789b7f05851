package com.get.permissioncenter.dto;

import com.get.permissioncenter.entity.PermissionGroupGradeResource;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/14 11:01
 * @verison: 1.0
 * @description:
 */
@AllArgsConstructor
@Data
public class GroupGradeStaffForMovingOrCopyContext {

    private Long targetGroupId;

    private Long targetGradeId;

    private List<PermissionGroupGradeStaff> sourcePermissionGroupGradeStaffs;

}
