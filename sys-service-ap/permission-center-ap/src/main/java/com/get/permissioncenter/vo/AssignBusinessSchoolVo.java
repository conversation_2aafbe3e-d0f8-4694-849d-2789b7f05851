package com.get.permissioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2024/1/29 12:08
 * @create: 2024/1/29 12:17
 * @verison: 1.0
 * @description:
 */
@Data
public class AssignBusinessSchoolVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校id")
    private Long id;

    @ApiModelProperty(value = "学校编号")
    private String num;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校类型")
    private String institutionTypeName;
    @NotNull(message = "员工id不能为空")
    @ApiModelProperty(value = "员工id",required = true)
    private Long fkStaffId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校类型id")
    private Long fkInstitutionTypeId;

//    @ApiModelProperty(value = "是否选中true已选/false未选")
//    private Boolean isSelected;
    @ApiModelProperty(value = "关键词")
    private String keyWord;
}
