package com.get.permissioncenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 工作台-审核类型枚举类
 */

@Getter
@AllArgsConstructor
public enum WorkbenchApprovalTypeEnum {

    WORK_LEAVE_FORM("WORK_LEAVE_FORM", "公休申请"),
    PMP_PROVIDER("PMP_PROVIDER", "合同端佣金方案审批"),
    PMP_AGENT("PMP_AGENT", "代理佣金方案审批"),
    PMP_PROVIDER_BATCH_TERRITORY("PMP_PROVIDER_BATCH_TERRITORY", "方案territory审批"),
    ;

    private String code;

    private String msg;

    public static WorkbenchApprovalTypeEnum getEnumByCode(String code) {
        for (WorkbenchApprovalTypeEnum value : WorkbenchApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isPmpType(String code) {
        List<String> pmpTypeCodeList = Arrays.asList(PMP_PROVIDER.getCode(),
                PMP_AGENT.getCode(), PMP_PROVIDER_BATCH_TERRITORY.getCode());
        return pmpTypeCodeList.contains(code);
    }

}
