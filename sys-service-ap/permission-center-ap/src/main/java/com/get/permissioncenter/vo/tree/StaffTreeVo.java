package com.get.permissioncenter.vo.tree;


import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/23
 * @TIME: 15:43
 * @Description: 员工树形DTO
 **/
@Data
public class StaffTreeVo extends BaseEntity {

    @ApiModelProperty(value = "业务国家")
    List<String> areaCountryDtos;
    @ApiModelProperty(value = "业务办公室")
    List<String> staffOfficeDtos;
    /**
     * 公司Id
     */
    @ApiModelProperty("公司Id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty("部门Id")
    private Long fkDepartmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty("职位Id")
    private Long fkPositionId;
    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String num;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;
    /**
     * 移动电话
     */
    @ApiModelProperty("移动电话")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty("Email")
    private String email;
    @ApiModelProperty("是否在职，0否/1是")
    private Boolean isOnDuty;
    /**
     * 姓名
     */
    @ApiModelProperty("是否为上司")
    private Boolean superior;

}
