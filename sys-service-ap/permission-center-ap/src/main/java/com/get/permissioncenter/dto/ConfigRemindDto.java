package com.get.permissioncenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("系统配置返回类")
@Data
public class ConfigRemindDto {

    @ApiModelProperty("留学申请创建时间（开始提醒时间）")
    private Date reminderTime;

    @ApiModelProperty(value = "【一般】OS晚于开学时间多长时间提醒（天）")
    private Long reminderDay2;

    @ApiModelProperty(value = "【严重】OS晚于开学时间多长时间提醒（天）")
    private Long reminderDay3;

    @ApiModelProperty(value = "(GEA)【一般】OS晚于开学时间多长时间提醒（天）")
    private Long geaDay2;

    @ApiModelProperty(value = "(IAE)【一般】OS晚于开学时间多长时间提醒（天）")
    private Long iaeDay2;

    @ApiModelProperty(value = "(GEA)【严重】OS晚于开学时间多长时间提醒（天）")
    private Long geaDay3;

    @ApiModelProperty(value = "(IAE)【严重】OS晚于开学时间多长时间提醒（天）")
    private Long iaeDay3;
}
