package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffEmail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/21 16:55
 */
@ApiModel("员工邮箱返回类")
@Data
public class StaffEmailVo extends BaseEntity {

    //==========实体类StaffEmail=============
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "Email密码")
    private String emailPassword;

    /**
     * 是否主邮箱：0否/1是
     */
    @ApiModelProperty(value = "是否主邮箱：0否/1是")
    private Boolean isPrimary;

    private static final long serialVersionUID = 1L;
}
