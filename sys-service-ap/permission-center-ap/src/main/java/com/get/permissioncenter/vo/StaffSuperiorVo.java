package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffSuperior;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/27
 * @TIME: 15:23
 * @Description:
 **/
@Data
public class StaffSuperiorVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 公司
     */
    @ApiModelProperty("公司")
    private String companyName;
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String departmentName;
    /**
     * 职位
     */
    @ApiModelProperty("职位")
    private String positionName;

    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String num;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    //==========实体类StaffSuperior=================
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 上司Id
     */
    @ApiModelProperty(value = "上司Id")
    @Column(name = "fk_staff_superior_id")
    private Long fkStaffSuperiorId;
}
