package com.get.permissioncenter.vo;


import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffDownload;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 */
@Data
public class StaffDownloadVo extends BaseEntity {

    @ApiModelProperty(value = "状态描述")
    private String statusDescription;

    @ApiModelProperty("文件路径")
    private String filePath;

    @ApiModelProperty("文件key")
    private String fileKey;

    @ApiModelProperty
    private String fileName;

    //==============实体类StaffDownload=================
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty("文件id")
    @Column(name = "fk_file_guid")
    private String fkFileGuid;

    @ApiModelProperty("操作类型")
    @Column(name = "opt_key")
    private String optKey;

    @ApiModelProperty("操作说明")
    @Column(name = "opt_description")
    private String optDescription;

    @ApiModelProperty("状态")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("备注")
    @Column(name = "remark")
    private String remark;
}
