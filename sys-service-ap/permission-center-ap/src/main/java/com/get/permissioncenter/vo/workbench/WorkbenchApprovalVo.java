package com.get.permissioncenter.vo.workbench;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

/**
 * 工作台-审核列表VO
 */
@Data
public class WorkbenchApprovalVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事件ID")
    private Long id;

    @ApiModelProperty(value = "事件关联的其他ID")
    private Map defData;

    @ApiModelProperty(value = "审核类型Code")
    private String approvalType;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核标题")
    private String name;

    @ApiModelProperty(value = "事件编号")
    private String num;

    @ApiModelProperty(value = "发起人名称")
    private String initiatorIdName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;
}
