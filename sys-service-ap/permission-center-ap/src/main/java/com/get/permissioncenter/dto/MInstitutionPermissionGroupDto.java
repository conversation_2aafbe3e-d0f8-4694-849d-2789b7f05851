package com.get.permissioncenter.dto;

import lombok.Data;

import java.util.Date;

@Data
public class MInstitutionPermissionGroupDto {
    private static final long serialVersionUID = 1L;

    /**
     * 学校权限组别Id
     */
    private Long id;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 学校权限组别编号
     */
    private String groupNum;

    /**
     * 学校权限组别名称
     */
    private String groupName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    private Integer viewOrder;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;
}