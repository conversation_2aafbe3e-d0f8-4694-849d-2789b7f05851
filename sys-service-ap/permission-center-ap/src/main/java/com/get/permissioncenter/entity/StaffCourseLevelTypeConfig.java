package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Data
@TableName("m_staff_course_level_type_config")
@ApiModel(value="StaffCourseLevelTypeConfig对象", description="")
public class StaffCourseLevelTypeConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "员工Id，=0为默认值")
    private Long fkStaffId;

    @ApiModelProperty(value = "自定义名称")
    private String customName;


    @ApiModelProperty(value = "类型表名：u_major_level/u_course_type_group")
    private String fkTableName;

    @ApiModelProperty(value = "表Ids：1,2,3")
    private String fkTableIds;

    @ApiModelProperty(value = "是否应用")
    private Boolean isChecked;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
