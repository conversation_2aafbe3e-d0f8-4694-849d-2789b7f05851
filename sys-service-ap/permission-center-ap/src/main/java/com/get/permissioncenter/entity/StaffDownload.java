package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.get.core.mybatis.base.BaseEntity;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("m_staff_download")
public class StaffDownload extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty("文件id")
    @Column(name = "fk_file_guid")
    private String fkFileGuid;

    @ApiModelProperty("操作类型")
    @Column(name = "opt_key")
    private String optKey;

    @ApiModelProperty("操作说明")
    @Column(name = "opt_description")
    private String optDescription;

    @ApiModelProperty("状态")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("备注")
    @Column(name = "remark")
    private String remark;
}
