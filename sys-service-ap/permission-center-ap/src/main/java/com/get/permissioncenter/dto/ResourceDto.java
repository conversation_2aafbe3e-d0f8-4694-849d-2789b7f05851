package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ResourceDto extends BaseVoEntity implements Serializable {
    @ApiModelProperty(value = "父系统资源Id")
    @NotNull(message = "父系统资源Id不能为空", groups = {Add.class, Update.class})
    private Long fkParentResourceId;
    @ApiModelProperty(value = "系统资源Key")
    @NotBlank(message = "系统资源Key不能为空", groups = {Add.class, Update.class})
    private String resourceKey;
    @ApiModelProperty(value = "资源名称分类")
    @NotBlank(message = "资源名称不能为空", groups = {Add.class, Update.class})
    private String resourceName;
    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
    @ApiModelProperty(value = "关键词")
    private String keyWord;
    /**
     * api交互_key（主要后端识别）
     */
    @ApiModelProperty(value = "api交互_key（主要后端识别）")
    private String apiKey;

    /**
     * 是否菜单，0否/1是
     */
    @ApiModelProperty(value = "是否菜单，0否/1是")
    @NotNull(message = "是否菜单不能为空", groups = {Add.class, Update.class})
    private Boolean isMenu;
    private String oldResourceKey;
}
