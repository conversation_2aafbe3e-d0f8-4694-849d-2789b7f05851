package com.get.permissioncenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.entity.*;
import com.get.permissioncenter.vo.*;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限 Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_PERMISSION_CENTER
)
public interface IPermissionCenterClient {
    String API_PREFIX = "/feign";

    /**
     * 登录使用：根据用户名和密码获取员工信息
     */
    String USER_INFO = API_PREFIX + "/staff-info";
    /**
     * 通过员工id 查找对应员工姓名
     */
    String GET_STAFF_NAME_BY_ID = API_PREFIX + "/get-staff-name-by-id";
    /**
     * 通过员工id 更新登录员工的基本信息（缓存）
     */
    String UPDATE_LOGIN_STAFF_INFO_BY_STAFF_ID = API_PREFIX + "/update-login-staff-info-by-staff-id";
    /**
     * fegin调用，更新年假
     */
    String UPDATE_ANNUAL_LEAVE_BASE = API_PREFIX + "/update-annual-leave-base";
    /**
     * fegin调用，更新补休
     */
    String UPDATE_COMPENSATORY_LEAVE_BASE = API_PREFIX + "/update-compensatory-leave-base";
    /**
     * feign调用 通过员工姓名或者英文名关键字查找对应ids
     */
    String GET_STAFF_ID_BY_NAME = API_PREFIX + "/get-staff-id-by-name";
    /**
     * feign调用，根据考勤号码匹配员工ID
     */
    String GET_STAFFIDS_BY_ATTENDANCE_NUM = API_PREFIX+"/get-staffids-by-attendance_num";
    /**
     * feign 调用，根据员工中文以及英文名匹配员工ID
     */
    String GET_STAFFIDS_BY_NAME_AND_ENAME = API_PREFIX + "/get-staffIds-by-name-and-enname";
    /**
     * feign调用 根据公司id查找该公司下的员工ids
     */
    String GET_STAFF_IDS_BY_COMPANY_ID = API_PREFIX + "/get-staff-ids-by-company-id";

    String GET_STAFF_IDS_BY_COMPANY_IDS = API_PREFIX + "/get-staff-ids-by-company-ids";
    /**
     * 根据员工ids获取员工LoginIds
     */
    String GET_STAFF_LOGIN_ID_BY_IDS = API_PREFIX + "/get-staff-login-id-by-ids";
    /**
     * 删除简历guid
     */
    String DELETE_GUID = API_PREFIX + "/delete-guid";
    /**
     * 根据id查询公司名称
     */
    String GET_COMPANY_NAME_BY_ID = API_PREFIX + "/get-company-name-by-id";
    /**
     * 获取全部人名字
     */
    String GET_STAFF_NAME_BY_MAP = API_PREFIX + "/get-staff-name-by-map";

    String GET_STAFF_NAME_DEPARTMENT_BY_MAP = API_PREFIX + "/get-staff-name-department-by-map";

    /**
     * 获取员工信息
     */
    String GET_STAFF_MAP_BY_STAFF_IDS = API_PREFIX + "/get-staff-map-by-staff-ids";
    /**
     * fegin调用，根据员工ids找属性
     */
    String GET_STAFF_BY_IDS = API_PREFIX + "/get-staff-by-ids";

    String GET_STAFF_BY_ID = API_PREFIX + "/get-staff-by-id";
    /**
     * 根据部门id获取员工下拉框数据
     */
    String GET_STAFF_BY_DEPARTMENT_IDS = API_PREFIX + "/get-staff-by-department-ids";

    String GET_STAFF_BY_DEPARTMENT_IDS_COUNTRYNUM = API_PREFIX + "/get-staff-by-department-ids-and-country-num";
    /**
     * fei调用根据员工id获取员工下拉框数据
     */
    String GET_STAFF_BY_STAFF_IDS = API_PREFIX + "/get-staff-by-staff-ids";
    /**
     * 根据公司Id获取员工下拉
     */
    String GET_STAFF_BY_COMPANY_ID = API_PREFIX + "/get-staff-by-company-id";
    /**
     * 根据公司ID获取员工列表
     */
    String GET_STAFFS = API_PREFIX + "/get-staffs";
    /**
     * 根据公司ID获取员工Dto
     */
    String GET_STAFF_DTO_BY_FK_COMPANY_ID = API_PREFIX + "/get-staff-dto-by-fk-company-id";
    /**
     * 根据公司ID或者部门ID获取所有员工
     */
    String GET_STAFF_DTOS = API_PREFIX + "get-staff-dtos";
    /**
     * 获取员工所有的直属下属包括下属的下属的ids
     */
    String GET_ALL_SUBORDINATE_IDS = API_PREFIX + "get-all-subordinate-ids";
    /**
     * fegin调用，根据员工ids查询对应公司
     */
    String GET_STAFF_BY_CREATE_USERS = API_PREFIX + "/get-staff-by-create-users";
    /**
     * feign 调用根据员工ids获取对应的公司id
     */
    String GET_COMPANY_ID_BY_STAFF_IDS = API_PREFIX + "/get-company-id-by-staff-ids";
    /**
     * 获取所有公司
     */
    String GET_COMPANY_ALL = API_PREFIX + "/get-company-all";
    /**
     * feign调用 根据ids查询公司名称map
     */
    String GET_COMPANY_NAMES_BY_IDS = API_PREFIX + "/get-company-names-by-ids";

    String GET_COMPANY_NAMES_BY_IDS_SORT = API_PREFIX + "/get-company-names-by-ids-sort";
    /**
     * feign调用 通过员工ids查找对应公司名称map
     */
    String GET_COMPANY_NAMES_BY_STAFF_IDS = API_PREFIX + "/get-company-names-by-staff-ids";
    /**
     * fegin调用，根据员工id查询对应公司
     */
    String GET_STAFF_BY_CREATE_USER = API_PREFIX + "/get-staff-by-create-user";
    /**
     * fegin调用，根据员工id找出属于的部门
     */
    String GET_COMPANY_ID_BY_STAFF_ID = API_PREFIX + "/get-company-id-by-staff-id";
    /**
     * 根据ids查询办公室名称map
     */
    String GET_OFFICE_NAMES_BY_IDS = API_PREFIX + "/get-office-names-by-ids";
    /**
     * 根据关键字模糊查询公司ids
     */
    String GET_COMPANY_ID_BY_NAME = API_PREFIX + "/get-company-id-by-name";
    /**
     * 根据ids查询部门名称map
     */
    String GET_DEPARTMENT_NAMES_BY_IDS = API_PREFIX + "/get-department-names-by-ids";
    /**
     * 销售中心feign调用 部门编号对应的部门名称
     */
    String GET_DEPARTMENT_NAMES_LIST = API_PREFIX + "/get-department-names-list";
    /**
     * feign调用 根据id查找对应部门名称
     */
    String GET_DEPARTMENT_NAME_BY_ID = API_PREFIX + "/get-department-name-by-id";
    /**
     * feign调用 获取部门的所有员工ids
     */
    String GET_ALL_DEPARTMENT_STAFF_IDS = API_PREFIX + "/get-all-department-staff-ids";
    /**
     * 根据部门编号获取部门id
     */
    String GET_DEPARTMENT_ID_BY_NUM = API_PREFIX + "/get-department-id-by-num";
    /**
     * 根据部门id获取部门编号
     */
    String GET_DEPARTMENT_NUM_BY_ID = API_PREFIX + "/get-department-num-by-id";
    /**
     * 根据职位编号获取人
     */
    String GET_POSITION_BY_NUM = API_PREFIX + "/get-position-by-num";
    /**
     * feign调用 获取部门最高职位员工idsfeign调用 获取部门最高职位员工ids
     */
    String GET_TOP_POSITION_STAFF_IDS = API_PREFIX + "/get-top-position-staff-ids";
    /**
     * 获取职位的所有员工ids
     */
    String GET_ALL_POSITION_STAFF_IDS = API_PREFIX + "/get-all-position-staff-ids";
    /**
     * 根据部门id查询部门信息
     */
    String GET_DEPARTMENT_BY_ID = API_PREFIX + "/get-department-by-id";
    /**
     * 根据id获取办公室
     */
    String GET_OFFICE_BY_ID = API_PREFIX + "/get-office-by-id";
    /**
     * 根据办公室id查找办公室名称
     */
    String GET_OFFICE_NAME_BY_ID = API_PREFIX + "/get-office-name-by-id";
    /**
     * 获取员工上司id
     */
    String GET_STAFF_SUPER_ID_BY_STAFF_ID = API_PREFIX + "/get-staff-super-id-by-staff-id";

    /**
     * 获取员工上司ids
     */
    String GET_STAFF_SUPER_IDS_BY_STAFF_IDS = API_PREFIX + "/get-staff-super-ids-by-staff-ids";

    /**
     * 获取下属ids集合
     */
    String GET_STAFF_FOLLOWER_IDS = API_PREFIX + "/get-staff-follower-ids";

    /**
     *  获取业务下属ids
     */
    String GET_BUSINESS_SUBORDINATES_IDS = API_PREFIX + "/get-business-subordinates-ids";

    /**
     * 获取直接下属ids
     */
    String GET_OBTAIN_DIRECT_SUBORDINATES_IDS = API_PREFIX + "/get-obtain-direct-subordinates-ids";
    /**
     * 员工业务国家
     */
    String GET_STAFF_AREA_COUNTRY_KEYS_BYFK_STAFF_ID = API_PREFIX + "/get-staff-area-country-keys-byfk-staff-id";

    /**
     * 员工业务国家id集合
     */
    String GET_STAFF_AREA_COUNTRY_IDS_BYFK_STAFF_ID = API_PREFIX + "/get-staff-area-country-ids-byfk-staff-id";

    /**
     * 通过Resource删除权限
     */
    String GET_GROUP_GRADE_RESOURCES_BY_RESOURCE = API_PREFIX + "/get-group-grade-resources-by-resource";
    /**
     * 获取公司logo
     */
    String GET_COMPANY_ICON = API_PREFIX + "/get-company-icon";
    /**
     * 保存简历Guid
     */
    String SAVE_RESUME_GUID = API_PREFIX + "/save-resume-guid";
    //---------------system--------------------
    /**
     * key查找对应的配置
     */
    String GET_CONFIG_VALUE_BY_CONFIG_KEY = API_PREFIX + "/get-config-value-by-config-key";

//	String GET_STAFF_BY_CREATE_USERSS = API_PREFIX + "/get-staff-by-create-users";
//	@PostMapping(GET_STAFF_BY_CREATE_USERSS)
//	Result<List<StaffVo>> getStaffsByCreateUsers(@RequestBody Set<String> createUsers);
    /**
     * feign调用 查询配置信息
     */
    String GET_CONFIG_BY_KEY = API_PREFIX + "/get-config-by-key";

    String GET_COMPANY_CONFIG_INFO = API_PREFIX + "/get-company-config-info";
    //	public ConfigVo getConfigValueByConfigKeyAndValue(@RequestParam(name = "key") String key,
//													   @RequestParam(name = "value1") Long value1){
//		return configService.getConfigValueByConfigKeyAndValue(key,value1);
//	}
    /**
     * 配置key和value1查询对象
     */
    String GET_CONFIG_VALUE_BY_CONFIG_KEY_AND_VALUE = API_PREFIX + "/get-config-value-by-config-key-and-value";
    /**
     * 所有员工ids
     */
    String GET_ALL_STAFF_IDS = API_PREFIX + "/get-all-staff-ids";
    /**
     * feign调用 通过员工姓名关键字查找对应ids
     */
    String GET_STAFF_IDS_BY_NAME_KEY = API_PREFIX + "/get-staff-ids-by-name-key";
    /**
     * feign调用 通过员工ids 查找对应的员工姓名map
     */
    String GET_STAFF_NAMES_BY_IDS = API_PREFIX + "/get-staff-names-by-ids";
    /**
     * 加载批量修改项接口
     */
    String GET_BATCH_UPDATE_ITEMS = API_PREFIX + "/get-batch-update-items";
    /**
     * 系统中心/批量修改配置管理/批量修改配置详情
     */
    String DETAIL = API_PREFIX + "/detail";
    /**
     * feign调用 通过员工ids 查找对应的职位编号
     */
    String GET_POSITION_NUM_BY_IDS = API_PREFIX + "/get-position-num-by-ids";
    /**
     * 根据职位编号positionNums获取员工ids
     */
    String GET_STAFF_IDS_BY_POSITION_NUMS = API_PREFIX + "/get-staff-ids-by-position-nums";
    /**
     * 获取职位下的员工
     */
    String GET_POSITION_NUM_AND_STAFF_ID_MAP = API_PREFIX + "/get-position-num-and-staff-id-map";
    /**
     * 批量获取业务上司
     */
    String GET_STAFF_SUPERIOR_BY_IDS = API_PREFIX + "/get-staff-superior-by-ids";
    /**
     * 获取当天生日的员工
     */
    String GET_DAY_OF_STAFF_BIRTHDAY = API_PREFIX + "/get-day-of-staff-birthday";
    /**
     * 根据输入关键字模糊查询员工列表
     */
    String GET_STAFF_LIST_BY_STAFF_NAME = API_PREFIX + "/get-staff-list-by-staff-name";
    /**
     * 获取员工在职状态
     */
    String GET_STAFF_IS_ON_DUTY=API_PREFIX +"/get-staff-is-on-duty";
    /**
     * feign调用 通过员工ids 查找对应的员工姓名map 单纯获取英文名
     */
    String GET_STAFF_EN_NAMES_BY_IDS=API_PREFIX+"/get-staff-en-name-by-ids";
    String GET_STAFF_CHN_NAMES_BY_IDS = API_PREFIX+"/get-staff-chn-name-by-ids";
    String GET_CHILD_COMPANY_IDS = API_PREFIX+"/get-child-company-ids";
    String FUZZY_SEARCH_COMPANY_NAME = API_PREFIX + "fuzzy-search-company-name";
    String GET_DEPARTMENT_AND_STAFF_DTO_BY_STAFF_IDS = API_PREFIX + "/get-department-and-staff-dto-by-staff-ids";

    String GET_STAFF_DTO_BY_IDS = API_PREFIX + "/get-staff-dto-by-ids";

    String GET_STAFF_DEPARTMENTS_BY_ID = API_PREFIX + "/get-staff-departments-by-id";

    String ADD_DOWNLOAD_RECORD = API_PREFIX + "/add-download-record";

    String UPDATE_DOWNLOAD = API_PREFIX + "/update-download";

    String SAVE_STAFF_CONFIG_BY_TYPE = API_PREFIX + "/save-staff-config-by-type";

    String GET_STAFF_CONFIG_BY_TYPE = API_PREFIX + "/get-staff-config-by-type";

    String GET_RE_STAFF_ID_BY_KEY = API_PREFIX + "/get-re-staff-id-by-key";

    String GET_MEDIA_AND_ATTACHED_BY_IAE_CRM = API_PREFIX + "/get-media-and-attached-by-iae-crm";

    String WX_CP_LOGIN = API_PREFIX + "/wx-cp-login";

    String UPDATE_MEDIA_AND_ATTACHED_BY_ID = API_PREFIX + "/update-media-and-attached-by-id";

    String GET_STAFFDTOS_BY_DEPARTMENT_NUMS = API_PREFIX + "/get-staffdtos-by-department-nums";

    String GET_STAFF_BY_LOGIN_ID = API_PREFIX +"/get-staff-by-login-id";

    /**
     * 获得项目成员限制配置
     */
    String GET_PROJECT_LIMIT_CONFIGKEY = API_PREFIX + "get-project-limit-configkey";

    String GET_STAFF_IDS_BY_LIKE_CONDITION = API_PREFIX + "get-staff-ids-by-like-condition";

    String GET_STAFF_BY_LOGIN_IDS = API_PREFIX +"/get-staff_by_login_ids";

    String GET_COMPANY_SETTLEMENT_CONFIG_INFO = API_PREFIX +"/get-company-settlement-config-info";

    String GET_COMPANY_SETTLEMENT_CONFIG_INFO_MAP = API_PREFIX + "/get-company-settlement-config-info-map";

    String GET_STAFF_IDS_BY_RESOURCE_KEY = API_PREFIX +"/get-staff-ids-by-resource-key";

    String GET_STAFF_COURSE_LEVEL_TYPE_CONFIG = API_PREFIX + "/get-staff-course-level-type-config";

    String GET_COMPANY_CONFIG_MAP = API_PREFIX + "/get-company-config-map";

    String GET_COMPANY_CONFIG_ANALYSIS = API_PREFIX + "/get-company-config-analysis";

    String GET_STAFF_AREA_COUNTRY_BY_STAFF_IDS = API_PREFIX + "/get-staff-area-country-by-staff-ids";

    String GET_IS_STAFF_BUSINESS = API_PREFIX + "/get-is-staff-business";

    String GET_STAFF_DTO_MAP_BY_LOGIN_IDS = API_PREFIX + "/get-staff-dto-map-by-login-ids";

    String GET_ALL_STAFF_SUPERIOR_BY_STAFF_IDS = API_PREFIX + "/get-all-staff-superior-by-staff-ids";

    String GET_PUSH_DEPARTMENT_STAFF_EMAIL = API_PREFIX + "/get-push-department-staff-email";

    String GET_COMPANY_TREE= API_PREFIX + "/get-company-tree";

    String GET_COMPANY_BY_CURRENT_ID = API_PREFIX + "/get-company-by-current-id";

    String GET_AUTHORIZED_STAFF_IDS_BY_RESOURCE_KEY= API_PREFIX + "/get-authorized-staff-ids-by-resource-key";

    String  GET_HTI_AND_CHILD_COMPANY = API_PREFIX + "/get-hti-and-child-company";



    /**
     * 获取员工信息集合
     */
    String GET_STAFF_LIST_BY_IDS_AND_COMPANY_IDS = API_PREFIX + "/get-staff-list-by-ids-and-company-ids";


    String GET_ALL_STAFF = API_PREFIX + "/get-all-staff";
    @GetMapping(GET_ALL_STAFF)
    Result<List<Staff>> getAllStaff();




    @GetMapping(GET_COMPANY_TREE)
     Result<List<CompanyTreeVo>> getCompanyTree(@RequestParam(name = "companyId", required = false) Long companyId);


    @GetMapping(GET_HTI_AND_CHILD_COMPANY)
    Result<List<Long>> getHtiAndChildCompany();

    /**
     * 根据当前登录人获取公司id及子公司id
     */

    @GetMapping("GET_COMPANY_BY_CURRENT_ID")
    Result<CompanyVo> getCompanyVo();

    /**
     * 登录使用：根据用户名和密码获取员工信息
     *
     * @param userName
     * @param password
     * @param isAvatarLogin
     * @param avatarLogin
     * @return
     */
    @GetMapping(USER_INFO)
    Result<StaffInfoVo> staffInfo(@RequestParam("userName") String userName, @RequestParam(value = "password", required = false) String password, @RequestParam(value = "isAvatarLogin", required = false) String isAvatarLogin, @RequestParam(value = "avatarLogin", required = false) String avatarLogin);

    /**
     * 通过员工id 查找对应员工姓名
     * @param id
     * @return
     */
    @GetMapping(GET_STAFF_NAME_BY_ID)
    Result<String> getStaffName(@RequestParam("id") Long id);

    /**
     * 通过员工id 更新登录员工的基本信息（缓存）
     * @param staffId
     * @return
     */
    @GetMapping(UPDATE_LOGIN_STAFF_INFO_BY_STAFF_ID)
    Result<Boolean> updateLoginStaffInfoByStaffId(@RequestParam("id") Long staffId);

    /**
     * fegin调用，更新年假
     * @param staffId
     * @param annualLeaveBase
     * @return
     */
    @PostMapping(UPDATE_ANNUAL_LEAVE_BASE)
    Result<Boolean> updateAnnualLeaveBase(@RequestParam("staffId") Long staffId, @RequestParam("annualLeaveBase") BigDecimal annualLeaveBase);

    /**
     * fegin调用，更新补休
     * @param staffId
     * @param compensatoryLeaveBase
     * @return
     */
    @PostMapping(UPDATE_COMPENSATORY_LEAVE_BASE)
    Result<Boolean> updateCompensatoryLeaveBase(@RequestParam("staffId") Long staffId, @RequestParam("compensatoryLeaveBase") BigDecimal compensatoryLeaveBase);

    /**
     * feign调用 通过员工姓名或者英文名关键字查找对应ids
     * @param staffNameKeyOrEnNameKey
     * @return
     */
    @GetMapping(GET_STAFF_ID_BY_NAME)
    Result<List<Long>> getStaffIdsByNameKeyOrEnNameKey(@RequestParam("staffNameKeyOrEnNameKey") String staffNameKeyOrEnNameKey);

    /**
     * feign调用，根据考勤号码匹配员工ID
     * @param attendanceNums
     * @return
     */
    @PostMapping(GET_STAFFIDS_BY_ATTENDANCE_NUM)
    Result<Map<String,Long>> getStaffIdsByAttendanceNum(@RequestBody Set<String> attendanceNums,@RequestParam("fkCompanyId") Long fkCompanyId);

    /**
     * feign 调用，根据员工中文以及英文名匹配员工ID
     * @param entities
     * @return
     */
    @PostMapping(GET_STAFFIDS_BY_NAME_AND_ENAME)
    Result<Map<String,BaseSelectEntity>> getStaffIdsByNameAndEnName(@RequestBody List<BaseSelectEntity> entities);

    /**
     * feign调用 根据公司id查找该公司下的员工ids
     * @param companyId
     * @return
     */
    @GetMapping(GET_STAFF_IDS_BY_COMPANY_ID)
    Result<List<Long>> getStaffIdsByCompanyId(@RequestParam("companyId") Long companyId);

    /**
     * feign调用 根据公司ids查找该公司下的员工ids
     * @param companyIds
     * @return
     */
    @PostMapping(GET_STAFF_IDS_BY_COMPANY_IDS)
    Result<List<Long>> getStaffIdsByCompanyIds(@RequestBody List<Long> companyIds);

    /**
     * 根据员工ids获取员工LoginIds
     * @param ids
     * @return
     */
    @PostMapping(GET_STAFF_LOGIN_ID_BY_IDS)
    Result<Map<Long, String>> getStaffLoginIdByIds(@RequestBody(required = false) Set<Long> ids);

    /**
     * 删除简历guid
     * @param guid
     * @return
     */
    @GetMapping(DELETE_GUID)
    Result<Boolean> deleteGuid(@RequestParam("guid") String guid);

    /**
     * 根据id查询公司名称
     * @param id
     * @return
     */
    @GetMapping(GET_COMPANY_NAME_BY_ID)
    Result<String> getCompanyNameById(@RequestParam("id") Long id);

    /**
     * 获取全部人名字
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_NAME_BY_MAP)
    Result<Map<Long, String>> getStaffNameMap(@RequestBody Set<Long> staffIds);

    /**
     * 【部门】名字（离职）（英文名）
     * @return
     */
    @PostMapping(GET_STAFF_NAME_DEPARTMENT_BY_MAP)
    Result<Map<Long, String>> getStaffNameDepartMentMap(@RequestBody Set<Long> staffIds);


    /**
     * 获取全部人名字
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_MAP_BY_STAFF_IDS)
    Result<Map<Long, Staff>> getStaffMapByStaffIds(@RequestBody Set<Long> staffIds);
    /**
     * fegin调用，根据员工ids找属性
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_BY_IDS)
    List<StaffVo> getStaffByIds(@RequestBody Set<Long> staffIds);

    @GetMapping(GET_STAFF_BY_ID)
    Result<StaffVo> getStaffById(@RequestParam("staffId") Long staffId);
    /**
     * 根据部门id获取员工下拉框数据
     * @param departmentIds
     * @return
     */
    @PostMapping(GET_STAFF_BY_DEPARTMENT_IDS)
    Result<List<BaseSelectEntity>> getStaffByDepartmentIds(@RequestBody List<Long> departmentIds);

    @PostMapping(GET_STAFF_BY_DEPARTMENT_IDS_COUNTRYNUM)
    Result<List<BaseSelectEntity>> getStaffByDepartmentIdsAndCountryNum(@RequestBody List<Long> departmentIds,@RequestParam("fkCountryNum") String fkCountryNum);

    /**
     * fei调用根据员工id获取员工下拉框数据
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_BY_STAFF_IDS)
    Result<List<BaseSelectEntity>> getStaffByStaffIds(@RequestBody Set<Long> staffIds);

    /**
     * 根据公司Id获取员工下拉
     * @param fkCompanyId
     * @return
     */
    @GetMapping(GET_STAFF_BY_COMPANY_ID)
    List<BaseSelectEntity> getStaffByCompanyId(@RequestParam("fkCompanyId") Long fkCompanyId);

    /**
     * 根据公司ID获取员工列表
     * @param fkCompanyId
     * @return
     */
    @GetMapping(GET_STAFFS)
    List<Staff> getStaffs(@RequestParam("fkCompanyId") Long fkCompanyId);

    /**
     * 根据公司ID获取员工Dto
     * @param fkCompanyId
     * @return
     */
    @GetMapping(GET_STAFF_DTO_BY_FK_COMPANY_ID)
    List<StaffVo> getStaffDtoByFkCompanyId(@RequestParam("fkCompanyId") Long fkCompanyId);

    /**
     * 根据公司ID或者部门ID获取所有员工
     * @return
     */
    @GetMapping(GET_STAFF_DTOS)
    List<StaffVo> getStaffDtos(@RequestParam(value = "fkCompanyId" ,required = false) Long fkCompanyId,
                               @RequestParam(value = "fkDepartmentId",required = false)Long fkDepartmentId,
                               @RequestParam(value = "staffNameKeyOrEnNameKey",required = false) String staffNameKeyOrEnNameKey);

    /**
     *  获取员工所有的直属下属包括下属的下属的ids
     * <AUTHOR>
     * @DateTime 2022/12/6 11:40
     */
    @GetMapping(GET_ALL_SUBORDINATE_IDS)
    List<Long> getAllSubordinateIds(@RequestParam("staffId") Long staffId);

    /**
     * fegin调用，根据员工ids查询对应公司
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_BY_CREATE_USERS)
    Result<List<StaffVo>> getStaffByCreateUsers(@RequestBody Set<String> staffIds);

    /**
     * feign 调用根据员工ids获取对应的公司id
     * @param staffIds
     * @return
     */
    @PostMapping(GET_COMPANY_ID_BY_STAFF_IDS)
    Result<Map<Long, Long>> getCompanyIdByStaffIds(@RequestBody Set<Long> staffIds);

    /**
     * 获取所有公司
     * @return
     */
    @GetMapping(GET_COMPANY_ALL)
    Result<List<CompanyTreeVo>> getAllCompanyDto();

    /**
     * feign调用 根据ids查询公司名称map
     * @param companyIds
     * @return
     */
    @PostMapping(GET_COMPANY_NAMES_BY_IDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<Long, String>> getCompanyNamesByIds(@RequestBody Set<Long> companyIds);


    /**
     * feign调用 根据ids查询公司名称排序后的list
     * @param companyIds
     * @return
     */
    @PostMapping(GET_COMPANY_NAMES_BY_IDS_SORT)
    @VerifyLogin(IsVerify = false)
    Result<List<String>> getCompanyNamesByIdsDESC(@RequestBody Set<Long> companyIds);

    /**
     * feign调用 通过员工ids查找对应公司名称map
     * @param staffIds
     * @return
     */
    @PostMapping(GET_COMPANY_NAMES_BY_STAFF_IDS)
    Result<Map<Long, String>> getCompanyNamesByStaffIds(@RequestBody Set<Long> staffIds);

    /**
     * fegin调用，根据员工id查询对应公司
     * @param createUser
     * @return
     */
    @GetMapping(GET_STAFF_BY_CREATE_USER)
    Result<StaffVo> getStaffByCreateUser(@RequestParam("createUser") String createUser);

    /**
     * fegin调用，根据员工id找出属于的部门
     * @param staffId
     * @return
     */
    @PostMapping(GET_COMPANY_ID_BY_STAFF_ID)
    Result<StaffVo> getCompanyIdByStaffId(@RequestParam("staffId") Long staffId);

    /**
     * 根据ids查询办公室名称map
     * @param ids
     * @return
     */
    @ApiIgnore
    @PostMapping(GET_OFFICE_NAMES_BY_IDS)
    Result<Map<Long, String>> getofficeNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 根据关键字模糊查询公司ids
     * @param keyWord
     * @return
     */
    @GetMapping(GET_COMPANY_ID_BY_NAME)
    List<Long> getCompanyIdByName(@RequestParam("keyWord") String keyWord);

    /**
     * 根据ids查询部门名称map
     * @param ids
     * @return
     */
    @PostMapping(GET_DEPARTMENT_NAMES_BY_IDS)
    Result<Map<Long, String>> getDepartmentNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 销售中心feign调用 部门编号对应的部门名称
     * @param departmentNumList
     * @return
     */
    @PostMapping(GET_DEPARTMENT_NAMES_LIST)
    Result<List<String>> getDepartmentNameList(@RequestBody String[] departmentNumList);

    /**
     * feign调用 根据id查找对应部门名称
     * @param id
     * @return
     */
    @GetMapping(GET_DEPARTMENT_NAME_BY_ID)
    Result<String> getDepartmentNameById(@RequestParam("id") Long id);

    /**
     * feign调用 获取部门的所有员工ids
     * @param companyId
     * @param departmentId
     * @return
     */
    @PostMapping(GET_ALL_DEPARTMENT_STAFF_IDS)
    Result<List<Long>> getAllDepartmentStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "departmentId") Long departmentId);

    /**
     * 根据部门编号获取部门id
     * @param num
     * @return
     */
    @GetMapping(GET_DEPARTMENT_ID_BY_NUM)
    Result<Long> getDepartmentIdByNum(@RequestParam("num") String num);

    /**
     * 根据部门id获取部门编号
     * @param id
     * @return
     */
    @GetMapping(GET_DEPARTMENT_NUM_BY_ID)
    Result<String> getDepartmentNumById(@RequestParam("id") Long id);

    /**
     * 根据职位编号获取人
     * @param num
     * @return
     */
    @PostMapping(GET_POSITION_BY_NUM)
    Result<List<Long>> getPositionByNum(@RequestBody List<String> num);

    /**
     * feign调用 获取部门最高职位员工idsfeign调用 获取部门最高职位员工ids
     * @param companyId
     * @param departmentId
     * @return
     */
    @PostMapping(GET_TOP_POSITION_STAFF_IDS)
    Result<List<Long>> getTopPositionStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "departmentId") Long departmentId);

    /**
     * 获取职位的所有员工ids
     * @param companyId
     * @param positionIdList
     * @return
     */
    @PostMapping(GET_ALL_POSITION_STAFF_IDS)
    Result<List<Long>> getAllPositionStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestBody List<Long> positionIdList);

    /**
     * 根据部门id查询部门信息
     * @param id
     * @return
     */
    @GetMapping(GET_DEPARTMENT_BY_ID)
    Result<DepartmentVo> getDepartmentById(@RequestParam("id") Long id);

    /**
     * 根据id获取办公室
     * @param id
     * @return
     */
    @GetMapping(GET_OFFICE_BY_ID)
    Result<OfficeVo> getOfficeById(@RequestParam("id") Long id);

    /**
     * 根据办公室id查找办公室名称
     * @param id
     * @return
     */
    @GetMapping(GET_OFFICE_NAME_BY_ID)
    Result<String> getOfficeNameById(@RequestParam("id") Long id);

    /**
     * 获取员工上司id
     * @param id
     * @return
     */
    @GetMapping(GET_STAFF_SUPER_ID_BY_STAFF_ID)
    Result<Long> getStaffSupervisorIdByStaffId(@RequestParam("id") Long id);


    /**
     * 根据员工id获取上司id
     * @param ids
     * @return
     */
    @GetMapping(GET_STAFF_SUPER_IDS_BY_STAFF_IDS)
    Result<Set<Long>> getStaffSupervisorIds(@RequestBody Set<Long> ids);


    /**
     * 获取下属ids集合
     * @param id
     * @return
     */
    @GetMapping(GET_STAFF_FOLLOWER_IDS)
    Result<List<Long>> getStaffFollowerIds(@RequestParam("id") Long id);
    /**
     * 获取一层业务下属ids集合
     * @param id
     * @return
     */
    @GetMapping(GET_BUSINESS_SUBORDINATES_IDS)
    Result<List<Long>> getBusinessSubordinatesIds(@RequestParam("id") Long id);

    /**
     * 获取直接下属ids（一层）
     * @param id
     * @return
     */
    @GetMapping(GET_OBTAIN_DIRECT_SUBORDINATES_IDS)
    Result<List<Long>> getObtainDirectSubordinatesIds(@RequestParam("id") Long id);

    /**
     * 员工业务国家
     * @param id
     * @return
     */
    @GetMapping(GET_STAFF_AREA_COUNTRY_KEYS_BYFK_STAFF_ID)
    Result<List<String>> getStaffAreaCountryKeysByfkStaffId(@RequestParam("id") Long id);


    @GetMapping(GET_STAFF_AREA_COUNTRY_IDS_BYFK_STAFF_ID)
    Result<Set<Long>> getStaffAreaCountryIdsByfkStaffId(@RequestParam("id") Long id);

    /**
     * 通过Resource删除权限
     * @param resourceKey
     * @return
     */
    @GetMapping(GET_GROUP_GRADE_RESOURCES_BY_RESOURCE)
    Result<Boolean> getGroupGradeResourcesByResource(@RequestParam("resourceKey") String resourceKey);

    /**
     * 获取公司logo
     * @param fkCompanyId
     * @return
     */
    @GetMapping(GET_COMPANY_ICON)
    Result<MediaAndAttachedVo> getCompanyIcon(@RequestParam("fkCompanyId") Long fkCompanyId);

    /**
     * 保存简历Guid
     * @param guid
     * @return
     */
    @GetMapping(SAVE_RESUME_GUID)
    Result<Boolean> saveResumeGuid(@RequestParam("guid") String guid);

    /**
     * key查找对应的配置
     * @param key
     * @return
     */
    @GetMapping(GET_CONFIG_VALUE_BY_CONFIG_KEY)
    Result<String> getConfigValueByConfigKey(@RequestParam(name = "key") String key);

    /**
     * feign调用 查询配置信息
     * @param key
     * @return
     */
    @GetMapping(GET_CONFIG_BY_KEY)
    Result<ConfigVo> getConfigByKey(@RequestParam(name = "key") String key);

    @GetMapping(GET_COMPANY_CONFIG_INFO)
    CompanyConfigVo getCompanyConfigInfo(@RequestParam(name = "key")String key, @RequestParam(name = "column") Integer column);
    /**
     * 配置key和value1查询对象
     * @param key
     * @param value1
     * @return
     */
    @GetMapping(GET_CONFIG_VALUE_BY_CONFIG_KEY_AND_VALUE)
    Result<ConfigVo> getConfigValueByConfigKeyAndValue(@RequestParam(name = "key") String key,
                                                       @RequestParam(name = "value1") Long value1);

    /**
     * 所有员工ids
     * @return
     */
    @GetMapping(GET_ALL_STAFF_IDS)
    Set<Long> getAllStaffIds();

    /**
     * feign调用 通过员工姓名关键字查找对应ids
     * @param staffNameKey
     * @return
     */
    @GetMapping(GET_STAFF_IDS_BY_NAME_KEY)
    List<Long> getStaffIdsByNameKey(@RequestParam("staffNameKey") String staffNameKey);

    /**
     * feign调用 通过员工ids 查找对应的员工姓名map
     * @param ids
     * @return
     */
    @PostMapping(GET_STAFF_NAMES_BY_IDS)
    @VerifyLogin(IsVerify = false)
    Map<Long, String> getStaffNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 加载批量修改项接口
     * @param batchModifyConfigDto
     * @return
     */
    @PostMapping(GET_BATCH_UPDATE_ITEMS)
    Result<List<BatchModifyConfigVo>> getBatchUpdateItems(@RequestBody BatchModifyConfigDto batchModifyConfigDto);

    /**
     * 系统中心/批量修改配置管理/批量修改配置详情
     * @param id
     * @return
     */
    @PostMapping(DETAIL)
    Result<BatchModifyConfigVo> detail(@RequestParam(name = "id") Long id);

    /**
     * feign调用 通过员工ids 查找对应的职位编号
     * @param ids
     * @return
     */
    @PostMapping(GET_POSITION_NUM_BY_IDS)
    Map<Long, String> getPositionNumByIds(@RequestBody Set<Long> ids);

    /**
     * 根据职位编号positionNums获取员工ids
     * @param positionNums
     * @return
     */
    @PostMapping(GET_STAFF_IDS_BY_POSITION_NUMS)
    List<Long> getStaffIdsByPositionNums(@RequestBody Set<String> positionNums);

    /**
     * 获取职位下的员工
     * @param ids
     * @return
     */
    @PostMapping(GET_POSITION_NUM_AND_STAFF_ID_MAP)
    Map<String, List<Long>> getPositionNumAndStaffIdMap(@RequestBody Set<Long> ids);

    /**
     * 批量获取业务上司
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_SUPERIOR_BY_IDS)
    Map<Long, List<Long>> getStaffSuperiorByIds(@RequestBody Set<Long> staffIds);

    /**
     * 获取当天生日的员工
     */
    @PostMapping(GET_DAY_OF_STAFF_BIRTHDAY)
    void getDayOfStaffBirthday();

    /**
     * 根据输入关键字模糊查询员工列表
     * @param staffName
     * @return
     */
    @PostMapping(GET_STAFF_LIST_BY_STAFF_NAME)
    List<Long> getStaffListByStaffName(@RequestParam(value = "staffName") String staffName);

    /**
     * 获取员工在职状态
     * @param staffIds
     * @return
     */
    @PostMapping(GET_STAFF_IS_ON_DUTY)
    Result<Map<Long,Boolean>> getStaffIsOnDuty(@RequestBody Set<Long> staffIds);

    /**
     * feign调用 通过员工ids 查找对应的员工姓名map 单纯获取英文名
     * @param ids
     * @return
     */
    @PostMapping(GET_STAFF_EN_NAMES_BY_IDS)
    Map<Long, String> getStaffEnNameByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过员工ids 查找对应的员工姓名map 单纯获取中文名
     * @param ids
     * @return
     */
    @PostMapping(GET_STAFF_CHN_NAMES_BY_IDS)
    Map<Long, String> getStaffChnNameByIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_CHILD_COMPANY_IDS)
    List<Long> getChildCompanyIds(@RequestParam(value = "companyId") Long companyId);

    @PostMapping(FUZZY_SEARCH_COMPANY_NAME)
    Result<List<BaseSelectEntity>>  fuzzySearchCompanyName(@RequestParam(value = "keyword") String keyword,@RequestParam(value = "companyId")Long companyId);

    @PostMapping(GET_DEPARTMENT_AND_STAFF_DTO_BY_STAFF_IDS)
    Result<List<DepartmentAndStaffVo>> getDepartmentAndStaffDtoByStaffIds(@RequestParam("staffIds") Set<Long> staffIds);

    @PostMapping(GET_STAFF_DTO_BY_IDS)
    List<StaffVo> getStaffDtoByIds(@RequestParam("staffIds") Set<Long> staffIds);

    @PostMapping(GET_STAFF_DEPARTMENTS_BY_ID)
    Result<Set<Long>> getStaffDepartmentsById(@RequestParam("staffId") Long staffId);

    @PostMapping(ADD_DOWNLOAD_RECORD)
    Long addDownloadRecord(@RequestBody StaffDownload staffDownload);

    /**
     * 更新下载列表
     * @param staffDownload
     */
    @PostMapping(UPDATE_DOWNLOAD)
    void updateDownload(@RequestBody StaffDownload staffDownload);

    @PostMapping(SAVE_STAFF_CONFIG_BY_TYPE)
    void saveStaffConfigByType(@RequestParam("type") String type,@RequestBody List<String> keys);

    @GetMapping(GET_STAFF_CONFIG_BY_TYPE)
    StaffConfig getStaffConfigByType(@RequestParam("type") String type);

    @PostMapping(GET_RE_STAFF_ID_BY_KEY)
    List<Long> getReStaffIdByKey(@RequestParam("key")String key,@RequestBody List<Long> staffIds);

    @PostMapping(GET_MEDIA_AND_ATTACHED_BY_IAE_CRM)
    List<PermissionMediaAndAttached> getMediaAndAttachedByIaeCrm(@RequestBody(required = false) List<String> fkTableNames);

    @PostMapping(WX_CP_LOGIN)
    Result<StaffInfoVo> wxCpLogin(@RequestParam("code")String code, @RequestParam("platformType")String platformType);

    @PostMapping(UPDATE_MEDIA_AND_ATTACHED_BY_ID)
    Boolean updateMediaAndAttachedById(@RequestBody PermissionMediaAndAttached permissionMediaAndAttached);

    @PostMapping(GET_STAFFDTOS_BY_DEPARTMENT_NUMS)
    Result<List<StaffVo>> getStaffDtosByDepartmentNums(@RequestParam("countryId")Long countryId, @RequestBody Set<String> nums);

    @GetMapping(GET_STAFF_BY_LOGIN_ID)
    StaffVo getStaffByLoginId(@RequestParam("loginId")String loginId);

    @GetMapping(GET_PROJECT_LIMIT_CONFIGKEY)
    Result<CompanyConfigValueVo> getProjectLimitConfigKey(@RequestParam("configKey") String configKey);

    @PostMapping(GET_STAFF_IDS_BY_LIKE_CONDITION)
    Result<List<Long>> getStaffIdsByLikeCondition(@RequestParam("gmtCreateUser")String gmtCreateUser);

    @PostMapping(GET_STAFF_BY_LOGIN_IDS)
    List<Staff> getStaffByLoginIds(@RequestBody  List<String> staffGmtCreate);

    @GetMapping(GET_COMPANY_SETTLEMENT_CONFIG_INFO)
    List<CompanyConfigInfoDto> getCompanySettlementConfigInfo(@RequestParam("configKey") String configKey);

    @GetMapping(GET_COMPANY_SETTLEMENT_CONFIG_INFO_MAP)
    Map<Long,Integer> getCompanySettlementConfigInfoMap(@RequestParam("configKey") String configKey);

    @PostMapping(GET_STAFF_IDS_BY_RESOURCE_KEY)
    List<Long> getStaffIdsByResourceKey(@RequestParam("resourceKey")String resourceKey, @RequestParam("isContainAdmin")Boolean isContainAdmin);

    @PostMapping(GET_STAFF_COURSE_LEVEL_TYPE_CONFIG)
    List<StaffCourseLevelTypeConfig> getStaffCourseLevelTypeConfig(@RequestParam("fkStaffId") Long fkStaffId,@RequestParam("type")Integer type);

    @PostMapping(GET_COMPANY_CONFIG_MAP)
    Result<Map<Long, String>> getCompanyConfigMap(@RequestParam("configKey") String configKey, @RequestParam("value") int value);

    //获取公司配置key解析对象
    @PostMapping(GET_COMPANY_CONFIG_ANALYSIS)
    Result<Map<Long, CompanyConfigAnalysisVo>> getCompanyConfigAnalysis(@RequestParam("configKey") String configKey);

    @PostMapping(GET_STAFF_AREA_COUNTRY_BY_STAFF_IDS)
    //Result<List<StaffAreaCountry>> getStaffAreaCountryByStaffIds(List<Long> fkStaffIds);
    Result<List<StaffAreaCountry>> getStaffAreaCountryByStaffIds(@RequestBody List<Long> fkStaffIds);

    //是否是业务员工
    @PostMapping(GET_IS_STAFF_BUSINESS)
    Result<Boolean> getIsStaffBusiness(@RequestParam("fkStaffId") Long fkStaffId);

    /**
     * 根据登录ids，获取对应的员工DTO
     *
     * @param loginIds 登录ids
     * @return
     */
    @PostMapping(GET_STAFF_DTO_MAP_BY_LOGIN_IDS)
    Result<Map<String, StaffVo>> getStaffDtoMapByLoginIds(@RequestBody Set<String> loginIds);

    /**
     * 获取每个员工的所有上司（直到最顶级）id集合
     *
     * @param staffIds 员工ids
     * @return key：员工id，value：所有上司（直到最顶级）id集合
     */
    @PostMapping(GET_ALL_STAFF_SUPERIOR_BY_STAFF_IDS)
    Result<Map<Long, Set<Long>>> getAllStaffSuperiorByStaffIds(@RequestBody Set<Long> staffIds);

    /**
     * 获取推送部门人员的邮箱
     * @param fkCountryId
     * @param fkInstitutionId
     * @param departmentList
     * @return
     */
    @PostMapping(GET_PUSH_DEPARTMENT_STAFF_EMAIL)
    Result<List<String>> getPushDepartmentStaffEmail(@RequestParam(value = "fkCountryId", required = false) Long fkCountryId, @RequestParam(value = "fkInstitutionId", required = false) Long fkInstitutionId, @RequestBody List<String> departmentList);

    /**
     * 获取指定权限资源key的员工id集合
     *
     * @param resourceKey 权限资源key
     * @return
     */
    @PostMapping(GET_AUTHORIZED_STAFF_IDS_BY_RESOURCE_KEY)
    Result<List<Long>> getAuthorizedStaffIdsByResourceKey(@RequestParam("resourceKey") String resourceKey);

    /**
     * 根据员工id集合和公司获取员工集合
     * @param staffByIdsAndCompanyIdsDto
     * @return
     */
    @PostMapping(GET_STAFF_LIST_BY_IDS_AND_COMPANY_IDS)
    Map<Long, String> getStaffListByIdsAndCompanyIds(@RequestBody StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto);
}
