package com.get.permissioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyConfigDto {

    @ApiModelProperty("iae配置")
    private String iae;

    @ApiModelProperty("other配置")
    private String other;

    public Boolean getIae() {
        if (StringUtils.isBlank(iae)) {
            return false;
        }
        return Integer.parseInt(iae)==1;
    }

    public Boolean getOther() {
        if (StringUtils.isBlank(other)) {
            return false;
        }
        return Integer.parseInt(other)==1;
    }
}
