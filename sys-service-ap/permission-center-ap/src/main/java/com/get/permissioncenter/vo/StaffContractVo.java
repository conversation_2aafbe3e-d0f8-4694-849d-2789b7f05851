package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 15:14
 **/
@Data
public class StaffContractVo extends BaseEntity {

    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    //==========实体类StaffContract==============
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 签约公司
     */
    @ApiModelProperty(value = "签约公司")
    @Column(name = "signing_company")
    private String signingCompany;
    /**
     * 签约工资
     */
    @ApiModelProperty(value = "签约工资")
    @Column(name = "signing_salary")
    private BigDecimal signingSalary;
    /**
     * 社保属地
     */
    @ApiModelProperty(value = "社保属地")
    @Column(name = "social_insurance_place")
    private String socialInsurancePlace;
    /**
     * 工作地
     */
    @ApiModelProperty(value = "工作地")
    @Column(name = "workplace")
    private String workplace;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}
