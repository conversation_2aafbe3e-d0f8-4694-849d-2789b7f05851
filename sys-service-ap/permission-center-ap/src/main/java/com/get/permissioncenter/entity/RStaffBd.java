package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.Column;
import lombok.Data;

@Data
@TableName("r_staff_bd")
public class RStaffBd extends BaseEntity implements Serializable {

    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty("BDId")
    @Column(name = "fk_staff_id_bd")
    private Long fkStaffIdBd;

}

