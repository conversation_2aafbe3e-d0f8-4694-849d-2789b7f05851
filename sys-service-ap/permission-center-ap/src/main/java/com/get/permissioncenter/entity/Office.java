package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_office")
public class Office extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 办公室编号
     */
    @ApiModelProperty(value = "办公室编号")
    @Column(name = "num")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 热线
     */
    @ApiModelProperty(value = "热线")
    @Column(name = "hotLine")
    private String hotline;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @Column(name = "fax")
    private String fax;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}