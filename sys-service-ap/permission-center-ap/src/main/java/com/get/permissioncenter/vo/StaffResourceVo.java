package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.StaffResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/21
 * @TIME: 10:44
 * @Description:
 **/
@ApiModel("权限允许禁止返回类")
@Data
public class StaffResourceVo extends BaseEntity {
    /**
     * 允许权限
     */
    @ApiModelProperty(value = "允许权限")
    private List<String> allowResources;
    /**
     * 禁止权限
     */
    @ApiModelProperty(value = "禁止权限")
    private List<String> notAllowResources;

    //=============实体类StaffResource====================
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 系统资源Key
     */
    @ApiModelProperty("系统资源Key")
    @Column(name = "fk_resource_key")
    private String fkResourceKey;
    /**
     * 权限：0禁止/1允许
     */
    @ApiModelProperty("权限：0禁止/1允许")
    @Column(name = "permission")
    private Integer permission;
}
