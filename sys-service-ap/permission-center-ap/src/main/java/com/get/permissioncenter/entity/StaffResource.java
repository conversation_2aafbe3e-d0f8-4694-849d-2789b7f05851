package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_resource")
public class StaffResource extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 系统资源Key
     */
    @ApiModelProperty("系统资源Key")
    @Column(name = "fk_resource_key")
    private String fkResourceKey;
    /**
     * 权限：0禁止/1允许
     */
    @ApiModelProperty("权限：0禁止/1允许")
    @Column(name = "permission")
    private Integer permission;

}