package com.get.permissioncenter.vo;

import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * author:<PERSON>
 * Time: 14:41
 * Date: 2023/1/9
 * Description:
 */
@Data
public class CompanyConfigValueVo {

    @ApiModelProperty("value1")
    private List<CompanyConfigInfoDto> value1;

    @ApiModelProperty("value2")
    private Set<Long> value2;

    private Set<String> limitStepString;
}
