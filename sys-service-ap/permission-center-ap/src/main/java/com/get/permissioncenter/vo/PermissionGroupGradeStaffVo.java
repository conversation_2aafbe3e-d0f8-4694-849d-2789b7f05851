package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

@ApiModel("权限人员返回类")
@Data
public class PermissionGroupGradeStaffVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "权限组别和权限等级对应数组")
    List<GroupGradeStaffVo> GroupGradeStaffDto;
    @ApiModelProperty(value = "纵坐标权限等级")
    List<PermissionGrade> permissionGrades;
    @ApiModelProperty(value = "横坐标权限组别")
    List<PermissionGroup> permissionGroups;
    /**
     * 权限组别名称
     */
    @ApiModelProperty("权限组别名称")
    private String permissionGroupName;
    /**
     * 权限等级名称
     */
    @ApiModelProperty("权限等级名称")
    private String permissionGradeName;
    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    //=============实体类===============
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限组别Id")
    @Column(name = "fk_permission_group_id")
    private Long fkPermissionGroupId;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限级别Id")
    @Column(name = "fk_permission_grade_id")
    private Long fkPermissionGradeId;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

}
