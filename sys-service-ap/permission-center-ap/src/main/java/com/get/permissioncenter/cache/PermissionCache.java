package com.get.permissioncenter.cache;

import com.get.core.cache.utils.CacheUtil;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;

import java.util.List;

import static com.get.common.cache.CacheNames.SYS_CACHE;

/**
 * 权限中心缓存
 * 如修改了内容，则需要清理缓存：CacheUtil.clear(SYS_CACHE);
 */
public class PermissionCache {
    private static final String STAFFS_FKCOMPANY_ID = "staffs:fkCompanyId:";

    private static IPermissionCenterClient permissionCenterClient;

    private static IPermissionCenterClient getPermissionCenterClient() {
        if (permissionCenterClient == null) {
            permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
        }
        return permissionCenterClient;
    }

    /**
     * 根据公司id获取员工列表
     *
     * @param fkCompanyId
     * @return
     */
    public static List<Staff> getStaffs(Long fkCompanyId) {
        return CacheUtil.get(SYS_CACHE, STAFFS_FKCOMPANY_ID, fkCompanyId, () -> {
            List<Staff> result = getPermissionCenterClient().getStaffs(fkCompanyId);
            return result;
        });


    }
}
