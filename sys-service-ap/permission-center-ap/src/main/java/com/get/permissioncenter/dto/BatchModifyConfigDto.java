package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/2/23 11:26
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchModifyConfigDto extends BaseVoEntity {
    /**
     * 外部列表统计的id串
     */
    @ApiModelProperty(value = "外部列表统计的id串")
    List<Long> ids;
    /**
     * 库名
     */
    @ApiModelProperty(value = "库名")
    private String fkDbName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    @ApiModelProperty("排序字段")
    private String orderBy;
}
