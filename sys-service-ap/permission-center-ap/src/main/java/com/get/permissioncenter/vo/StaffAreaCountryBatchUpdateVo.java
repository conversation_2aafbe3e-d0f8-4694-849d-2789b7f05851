package com.get.permissioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StaffAreaCountryBatchUpdateVo {

    @ApiModelProperty(value = "员工Id")
    private List<Long> fkStaffIds;

    @ApiModelProperty(value = "国家Key(编号)")
    private List<String> fkAreaCountryKey;

    @ApiModelProperty(value = "true:新增国家  false:重设国家")
    private Boolean countryFlag = true;

}
