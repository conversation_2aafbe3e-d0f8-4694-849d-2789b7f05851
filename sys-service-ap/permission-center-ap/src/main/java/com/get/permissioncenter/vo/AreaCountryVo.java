package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/7/21
 * @TIME: 17:34
 * @Description: 所有业务国家DTO
 **/
@Data
public class AreaCountryVo extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 国家编号
     */
    @ApiModelProperty(value = "国家编号")
    private String num;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String name;
    /**
     * 国家中文名称
     */
    @ApiModelProperty(value = "国家中文名称")
    private String nameChn;
    /**
     * 首都
     */
    @ApiModelProperty(value = "首都")
    private String capital;
    /**
     * 人口
     */
    @ApiModelProperty(value = "人口")
    private String population;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private String area;
    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String language;
    /**
     * 宗教
     */
    @ApiModelProperty(value = "宗教")
    private String religion;
    /**
     * 时差
     */
    @ApiModelProperty(value = "时差")
    private String timeDifference;
    /**
     * 现任国家元首
     */
    @ApiModelProperty(value = "现任国家元首")
    private String president;
    /**
     * 国旗意义
     */
    @ApiModelProperty(value = "国旗意义")
    private String flagMeaning;
    /**
     * 国徽意义
     */
    @ApiModelProperty(value = "国徽意义")
    private String emblemMeaning;
    /**
     * 主要城市
     */
    @ApiModelProperty(value = "主要城市")
    private String mainCity;
    /**
     * 公共假期
     */
    @ApiModelProperty(value = "公共假期")
    private String holiday;
    /**
     * 气候：春
     */
    @ApiModelProperty(value = "气候：春")
    private String spring;
    /**
     * 气候：夏
     */
    @ApiModelProperty(value = "气候：夏")
    private String summer;
    /**
     * 气候：秋
     */
    @ApiModelProperty(value = "气候：秋")
    private String autumn;
    /**
     * 气候：冬
     */
    @ApiModelProperty(value = "气候：冬")
    private String winter;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
}
