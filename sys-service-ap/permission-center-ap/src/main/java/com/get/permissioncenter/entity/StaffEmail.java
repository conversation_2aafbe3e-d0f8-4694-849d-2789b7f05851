package com.get.permissioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_staff_email")
public class StaffEmail extends BaseEntity implements Serializable {
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

     @ApiModelProperty(value = "Email密码")
    private String emailPassword;

    /**
     * 是否主邮箱：0否/1是
     */
    @ApiModelProperty(value = "是否主邮箱：0否/1是")
    private Boolean isPrimary;

    private static final long serialVersionUID = 1L;
}