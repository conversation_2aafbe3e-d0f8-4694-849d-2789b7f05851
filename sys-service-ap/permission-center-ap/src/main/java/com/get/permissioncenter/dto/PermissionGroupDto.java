package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class PermissionGroupDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 权限组别编号
     */
    @ApiModelProperty(value = "权限组别编号")
    private String groupNum;

    /**
     * 权限组别名称
     */
    @ApiModelProperty(value = "权限组别名称")
    @NotBlank(message = "权限组别名称不能为空", groups = {Add.class, Update.class})
    private String groupName;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    private String keyword;

}
