package com.get.permissioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 返回跨系统登录认证信息
 */
@Data
public class AvatarInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 请求的时间戳
     */
    @ApiModelProperty(value = "请求的时间戳")
    private String currentTimeMillis;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 用户临时密钥
     */
    @ApiModelProperty(value = "用户临时密钥")
    private String userPwd;

    /**
     * bms登陆用户Id
     */
    @ApiModelProperty(value = "bms登陆用户Id")
    private String bmsLoginId;
}
