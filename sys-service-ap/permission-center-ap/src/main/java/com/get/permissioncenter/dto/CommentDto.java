package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/11/11
 * @TIME: 10:16
 * @Description:
 **/
@Data
public class CommentDto extends BaseVoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;

}
