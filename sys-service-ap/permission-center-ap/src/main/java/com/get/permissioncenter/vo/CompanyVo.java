package com.get.permissioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseSelectEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.persistence.Column;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 14:30
 **/
@Data
public class CompanyVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 子公司集合
     */
    @ApiModelProperty(value = "子公司集合")
    private List<CompanyVo> childCompanyDto;

    @ApiModelProperty(value = "公司图标链接")
    private MediaAndAttachedVo companyIcon;

    //=========实体类Company===============
    /**
     * 父公司Id
     */
    @ApiModelProperty(value = "父公司Id")
    @Column(name = "fk_parent_company_id")
    private Long fkParentCompanyId;
    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    @Column(name = "num")
    private String num;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "name")
    private String name;
    /**
     * 公司中文名称
     */
    @ApiModelProperty(value = "公司中文名称")
    private String nameChn;
    /**
     * 公司简称
     */
    @ApiModelProperty(value = "公司简称")
    private String shortName;
    /**
     * 公司中文简称
     */
    @ApiModelProperty(value = "公司中文简称")
    private String shortNameChn;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    
    @ApiModelProperty(value = "部门集合")
    List<BaseSelectEntity> departmentData;

    @ApiModelProperty(value = "员工集合")
    private List<StaffVo> staffVoList;
}
