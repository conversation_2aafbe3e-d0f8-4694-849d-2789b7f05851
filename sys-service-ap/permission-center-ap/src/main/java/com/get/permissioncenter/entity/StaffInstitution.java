package com.get.permissioncenter.entity;

import com.get.core.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@TableName("r_staff_institution")
@ApiModel(value="StaffInstitution对象", description="")
public class StaffInstitution extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

}
