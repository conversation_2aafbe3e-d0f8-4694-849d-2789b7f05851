package com.get.permissioncenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2024/1/22
 * @TIME: 14:50
 * @Description:
 **/
@Data
public class StaffCourseLevelTypeConfigDto extends BaseVoEntity {
    @ApiModelProperty(value = "类型：1-课程等级；2-课程类型")
    @NotNull(message = "类型不能为空", groups = {Add.class, Update.class})
    private Integer type;

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "员工Id，=0为默认值")
    private Long fkStaffId;

    @ApiModelProperty(value = "自定义名称")
    @NotBlank(message = "自定义名称不能为空", groups = {Add.class, Update.class})
    private String customName;

    @ApiModelProperty(value = "类型表名：u_major_level/u_course_type_group")
    private String fkTableName;

    @ApiModelProperty(value = "表Ids：1,2,3")
    @NotBlank(message = "表Ids不能为空", groups = {Add.class, Update.class})
    private String fkTableIds;

    @ApiModelProperty(value = "是否应用")
    private Boolean isChecked;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;
}
