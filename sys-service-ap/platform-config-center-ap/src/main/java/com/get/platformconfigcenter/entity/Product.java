package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel("")
@TableName("m_product")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("国家管家id")
    private Long fkCountryButlerId;

    @ApiModelProperty("服务范畴id")
    private Long fkServiceCategoryId;

    @ApiModelProperty("服务id")
    private Long fkServiceId;

    @ApiModelProperty("0-单项服务，1-套餐服务，3-按需报价类型")
    private Integer productType;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("产品价格")
    private BigDecimal price;

    @ApiModelProperty("排序，数字大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("服务计量单位id")
    private Long fkServiceUnitTypeId;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    private String gmtModifiedUser;
}
