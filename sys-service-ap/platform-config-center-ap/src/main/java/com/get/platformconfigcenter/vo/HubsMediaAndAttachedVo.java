package com.get.platformconfigcenter.vo;

import com.get.platformconfigcenter.entity.HubsMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Eric
 * @Date: 2024/9/3
 * @Description:
 * @Version 1.0
 */
@Data
public class HubsMediaAndAttachedVo extends HubsMediaAndAttached {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;
    @ApiModelProperty(value = "文件类型描述")
    private String typeValue;
    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;

}
