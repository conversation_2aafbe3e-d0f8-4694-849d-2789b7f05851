package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13 12:54
 */
@Data
@ApiModel("代理配置返回类")
public class AgentInfoVo extends BaseEntity {

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "隐私条例名称")
    private String fkPrivacyPolicyName;

    /**
     * 菜单栏logo
     */
    @ApiModelProperty(value = "菜单栏logo")
    private List<MediaAndAttachedVo> topLogoMediaAttachedVos;

    /**
     * 页面底栏logo
     */
    @ApiModelProperty(value = "页面底栏logo")
    private List<MediaAndAttachedVo> bottomLogoMediaAttachedVos;

    /**
     * 轮播图
     */
    @ApiModelProperty(value = "轮播图")
    private List<MediaAndAttachedVo> indexBannerMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理移动端首页Banner")
    private List<MediaAndAttachedVo> mobileIndexMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理平板端首页Banner")
    private List<MediaAndAttachedVo> padIndexMediaAttachedVos;


    /**
     * 资讯轮播图
     */
    @ApiModelProperty(value = "资讯轮播图")
    private List<MediaAndAttachedVo> newsBannerMediaAttachedVos;
    @ApiModelProperty(value = "资讯移动端轮播图")
    private List<MediaAndAttachedVo> mobileNewsBannerMediaAttachedVos;
    @ApiModelProperty(value = "资讯平板轮播图")
    private List<MediaAndAttachedVo> padNewsBannerMediaAttachedVos;

    /**
     * DSE模块Banner
     */
    @ApiModelProperty(value = "DSE模块Banner")
    private List<MediaAndAttachedVo> dseBannerMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理移动端DSE模块Banner")
    private List<MediaAndAttachedVo> mobileDseMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理平板端DSE模块Banner")
    private List<MediaAndAttachedVo> padDseMediaAttachedVos;

    /**
     * DSE模块广告Banner
     */
    @ApiModelProperty(value = "DSE模块广告Banner")
    private List<MediaAndAttachedVo> advertsBannerMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理移动端DSE模块广告Banner")
    private List<MediaAndAttachedVo> mobileDseAdvertsMediaAttachedVos;
    @ApiModelProperty(value = "MSO平台代理平板端DSE模块广告Banner")
    private List<MediaAndAttachedVo> padDseAdvertsMediaAttachedVos;






    /**
     * 推荐学校列表
     */
    @ApiModelProperty(value = "推荐学校列表")
    private List<Long> institutionIdList;

    @ApiModelProperty(value = "DSE推荐学校ids")
    private List<Long> dseInstitutionIdList;

    @ApiModelProperty(value = "DSE推荐新闻ids")
    private List<Long> dseNewsIdList;

    @ApiModelProperty(value = "网站推荐新闻ids")
    private List<Long> websiteNewsIdList;

    @ApiModelProperty(value = "首页推荐新闻ids")
    private List<Long> indexNewsIdList;

    @ApiModelProperty(value = "学校名字")
    private String institutionNameStr;

    @ApiModelProperty(value = "DSE推荐学校名字")
    private String dseInstitutionNameStr;

    @ApiModelProperty(value = "DSE推荐新闻名字")
    private String dseNewsNameStr;

    @ApiModelProperty(value = "首页推荐新闻名称")
    private String websiteNewsNameStr;

    @ApiModelProperty(value = "首页推荐新闻名字")
    private String indexNewsNameStr;

    @ApiModelProperty(value = "桥梁学校")
    private List<BridgeInstitutionVo> bridgeInstitutionDtoList;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;


    @ApiModelProperty(value = "公开对象名")
    private String publicLevelName;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 系统自动生成8位唯一码（创建唯一链接入口使用）
     */
    @ApiModelProperty(value = "系统自动生成8位唯一码（创建唯一链接入口使用）")
    @Column(name = "key_code")
    private String keyCode;
    /**
     * 访问域名（配置入口域名，唯一标识）
     */
    @ApiModelProperty(value = "访问域名（配置入口域名，唯一标识）")
    @Column(name = "host")
    private String host;
    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    @Column(name = "language_code")
    private String languageCode;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    @Column(name = "website_title")
    private String websiteTitle;

    @ApiModelProperty(value = "代理联系邮箱，多个用分号隔开")
    @Column(name = "email")
    private String email;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Column(name = "contact_tel")
    private String contactTel;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    @Column(name = "whatsapp")
    private String whatsapp;
    /**
     * facebook
     */
    @ApiModelProperty(value = "facebook")
    @Column(name = "facebook")
    private String facebook;
    /**
     * instagram
     */
    @ApiModelProperty(value = "instagram")
    @Column(name = "instagram")
    private String instagram;
    /**
     * 私隐政策Id
     */
    @ApiModelProperty(value = "私隐政策Id")
    @Column(name = "fk_privacy_policy_id")
    private Long fkPrivacyPolicyId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 关于我们（首页关于我们内容）
     */
    @ApiModelProperty(value = "关于我们（首页关于我们内容）")
    @Column(name = "about_us")
    private String aboutUs;
    /**
     * 联系我们（网站底部内容）
     */
    @ApiModelProperty(value = "联系我们（网站底部内容）")
    @Column(name = "contact_us")
    private String contactUs;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
}
