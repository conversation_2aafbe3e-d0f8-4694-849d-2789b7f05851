package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_sitemap")
public class Sitemap extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 父Id
     */
    @ApiModelProperty(value = "父Id")
    @Column(name = "fk_parent_sitemap_id")
    private Long fkParentSitemapId;
    /**
     * 菜单名称
     */
    @ApiModelProperty(value = "菜单名称")
    @Column(name = "menu_name")
    private String menuName;
    /**
     * 菜单key(必填，可用作逻辑定位)
     */
    @ApiModelProperty(value = "菜单key(必填，可用作逻辑定位)")
    @Column(name = "menu_key")
    private String menuKey;
    /**
     * 菜单链接类型：BLANK不需要链接/URL网址链接/COMPONENT模块链接/NEWS_INFO新闻资讯/COUNTRY_INFO国家资讯
     */
    @ApiModelProperty(value = "菜单链接类型：BLANK不需要链接/URL网址链接/COMPONENT模块链接/NEWS_INFO新闻资讯/COUNTRY_INFO国家资讯")
    @Column(name = "menu_link_type")
    private String menuLinkType;
    /**
     * 国家Id（不必选，国家资讯才需要选择）
     */
    @ApiModelProperty(value = "国家Id（不必选，国家资讯才需要选择）")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 信息类型Id（国家资讯类型/新闻资讯类型）
     */
    @ApiModelProperty(value = "信息类型Id（国家资讯类型/新闻资讯类型）")
    @Column(name = "fk_info_type_id")
    private Long fkInfoTypeId;
    /**
     * 菜单链接(或前端调用的模块入口key)
     */
    @ApiModelProperty(value = "菜单链接(或前端调用的模块入口key)")
    @Column(name = "menu_link_entrance")
    private String menuLinkEntrance;
    @ApiModelProperty(value = "菜单链接参数")
    @Column(name = "menu_link_params")
    private String menuLinkParams;
    @ApiModelProperty(value = "菜单链接描述")
    @Column(name = "menu_link_description")
    private String menuLinkDescription;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    @Column(name = "web_title")
    private String webTitle;
    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    @Column(name = "web_meta_description")
    private String webMetaDescription;
    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    @Column(name = "web_meta_keywords")
    private String webMetaKeywords;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Column(name = "effective_start_time")
    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @UpdateWithNull
    private Date effectiveStartTime;

    @Column(name = "effective_end_time")
    @ApiModelProperty(name = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @UpdateWithNull
    private Date effectiveEndTime;



}