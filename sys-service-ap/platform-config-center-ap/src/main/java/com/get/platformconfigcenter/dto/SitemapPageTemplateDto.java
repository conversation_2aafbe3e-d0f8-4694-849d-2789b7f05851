package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 16:59
 * @Description:
 **/
@Data
@ApiModel(value = "sitemap页面模板VO")
public class SitemapPageTemplateDto extends BaseVoEntity {
    /**
     * 页面模板名称
     */
    @ApiModelProperty(value = "页面模板名称")
    private String name;

    /**
     * 配置json
     */
    @ApiModelProperty(value = "配置json")
    private String configJson;

    /**
     * 页面模板图片
     */
    @ApiModelProperty(value = "页面模板图片")
    private List<MediaAndAttachedDto> mediaAttachedVos;
}
