package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 15:47
 * @Description:
 **/
@Data
@TableName("m_sitemap_page_template")
public class SitemapPageTemplate extends BaseEntity implements Serializable {
    /**
     * 页面模板名称
     */
    @ApiModelProperty(value = "页面模板名称")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "配置json")
    @Column(name = "config_json")
    private String configJson;
}
