package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2021/5/21 17:58
 */
@Data
public class AppFormConfigDivisionVo extends BaseEntity {
    /**
     * 申请单采集配置Id
     */
    @ApiModelProperty(value = "申请单采集配置Id")
    @Column(name = "fk_app_form_config_id")
    private Long fkAppFormConfigId;
    /**
     * 申请单划分Id
     */
    @ApiModelProperty(value = "申请单划分Id")
    @Column(name = "fk_app_form_division_id")
    private Long fkAppFormDivisionId;
}
