package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_app_form_division")
public class AppFormDivision extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 划分Key（必填）
     */
    @ApiModelProperty(value = "划分Key（必填）")
    @Column(name = "division_key")
    private String divisionKey;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    @Column(name = "is_required")
    private Boolean isRequired;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}