package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_agent_info")
public class AgentInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 系统自动生成8位唯一码（创建唯一链接入口使用）
     */
    @ApiModelProperty(value = "系统自动生成8位唯一码（创建唯一链接入口使用）")
    @Column(name = "key_code")
    private String keyCode;
    /**
     * 访问域名（配置入口域名，唯一标识）
     */
    @ApiModelProperty(value = "访问域名（配置入口域名，唯一标识）")
    @Column(name = "host")
    private String host;
    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    @Column(name = "language_code")
    private String languageCode;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    @Column(name = "website_title")
    private String websiteTitle;

    @ApiModelProperty(value = "代理联系邮箱，多个用分号隔开")
    @Column(name = "email")
    private String email;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Column(name = "contact_tel")
    private String contactTel;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    @Column(name = "whatsapp")
    private String whatsapp;
    /**
     * facebook
     */
    @ApiModelProperty(value = "facebook")
    @Column(name = "facebook")
    private String facebook;
    /**
     * instagram
     */
    @ApiModelProperty(value = "instagram")
    @Column(name = "instagram")
    private String instagram;
    /**
     * 私隐政策Id
     */
    @ApiModelProperty(value = "私隐政策Id")
    @Column(name = "fk_privacy_policy_id")
    private Long fkPrivacyPolicyId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 关于我们（首页关于我们内容）
     */
    @ApiModelProperty(value = "关于我们（首页关于我们内容）")
    @Column(name = "about_us")
    private String aboutUs;
    /**
     * 联系我们（网站底部内容）
     */
    @ApiModelProperty(value = "联系我们（网站底部内容）")
    @Column(name = "contact_us")
    private String contactUs;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
}