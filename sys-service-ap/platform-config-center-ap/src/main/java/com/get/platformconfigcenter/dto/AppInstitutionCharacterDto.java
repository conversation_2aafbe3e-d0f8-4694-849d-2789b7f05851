package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 课程动态表单VO
 *
 * <AUTHOR>
 * @date 2021/5/20 15:51
 */
@Data
public class AppInstitutionCharacterDto extends BaseVoEntity {

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;


}
