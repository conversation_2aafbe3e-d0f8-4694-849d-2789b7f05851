package com.get.platformconfigcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2024/9/4 10:15
 * @verison: 1.0
 * @description:
 */
@Data
public class SubmitOrderVo {

    @ApiModelProperty("订单id")
    private Long id;

    @ApiModelProperty("订单日期")
    private Date CreateTime;

    @ApiModelProperty("国家管家id")
    private Long fkCountryButlerId;

    @ApiModelProperty("学生名字")
    private String studentName;

    @ApiModelProperty("学生入读学校")
    private String studentSchool;

    @ApiModelProperty("支付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("应付")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付状态：0 '待支付', 1 '已支付', 3-用户取消，4-系统取消")
    private Integer paymentStatus;

    @ApiModelProperty("支付状态名称")
    private String paymentStatusName;


}
