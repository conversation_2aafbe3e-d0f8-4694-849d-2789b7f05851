package com.get.platformconfigcenter.vo;

import com.get.platformconfigcenter.entity.SaleOrderItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Eric
 * @Date: 2024/9/23
 * @Description:
 * @Version 1.0
 */
@Data
public class SaleOrderItemVo extends SaleOrderItem {

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品服务名称")
    private String serviceNameChn;

    @ApiModelProperty(value = "产品详情")
    private ProductDetailVo productDetailDto;

}
