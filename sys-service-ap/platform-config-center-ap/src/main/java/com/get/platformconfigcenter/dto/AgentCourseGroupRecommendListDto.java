package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AgentCourseGroupRecommendListDto extends BaseVoEntity implements Serializable {


    @ApiModelProperty(value = "代理id")
    @NotNull(message = "代理id不能为空")
    private Long fkAgentId;

    @ApiModelProperty(value = "课程类型组别Id")
    private Long fkCourseTypeGroupId;


}
