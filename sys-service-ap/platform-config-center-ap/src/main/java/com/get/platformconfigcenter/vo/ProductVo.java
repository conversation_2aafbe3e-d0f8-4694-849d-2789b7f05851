package com.get.platformconfigcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.platformconfigcenter.entity.Product;
import com.get.platformconfigcenter.entity.ProductIntroduction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel(value = "产品Dto")
public class ProductVo extends Product {

    @ApiModelProperty(value = "产品价格/规格")
    @JsonProperty("priceDto")
    private String priceDto;

    @ApiModelProperty(value = "产品服务中文名称")
    @JsonProperty("serviceName")
    private String serviceName;

    @ApiModelProperty(value = "产品计量单位")
    @JsonProperty("productUnitType")
    private String productUnitType;

    @ApiModelProperty(value = "产品服务范畴")
    private String categoryName;

    @ApiModelProperty(value = "产品介绍项")
    @JsonProperty("productIntroductionList")
    private List<ProductIntroduction> productIntroductions;

    @ApiModelProperty(value = "产品图标")
    @JsonProperty("productIcon")
    private String productIcon;

}
