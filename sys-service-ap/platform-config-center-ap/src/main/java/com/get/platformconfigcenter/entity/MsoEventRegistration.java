package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: EventRegistration
 * @Author: Eric
 * @Date: 2023/5/23 18:10
 * @Version: 1.0
 */
@Data
@TableName("m_event_registration")
public class MsoEventRegistration extends BaseEntity  {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名，涉及目标类型，如：m_competition，m_competition_item")
    private String fkTableName;

    @ApiModelProperty(value = "表Id，涉及目标Id")
    private Long fkTableId;

    @ApiModelProperty(value = "用户Id")
    private Long fkUserId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "移动电话")
    private String mobile;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "qq")
    private String qq;

    @ApiModelProperty(value = "微信号")
    private String wechat;

    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;

    @ApiModelProperty(value = "最高学历")
    private String educationLevel;

    @ApiModelProperty(value = "留学年份")
    private String studyAbroadYear;

    @ApiModelProperty(value = "意向国家")
    private String intendedCountry;

    @ApiModelProperty(value = "意向课程")
    private String intendedCourse;

    @ApiModelProperty(value = "活动消息来源")
    private String eventChannel;

    @ApiModelProperty(value = "参加人数")
    private Integer participantsCount;

    @ApiModelProperty(value = "是否MSO会员，0否/1是")
    private Boolean isMember;

    @ApiModelProperty(value = "isAttend")
    private Boolean isAttend;

    @ApiModelProperty(value = "登记编号")
    private String registrationCode;

    @ApiModelProperty("关系：本人/父母/亲戚/朋友")
    private String relation;



}
