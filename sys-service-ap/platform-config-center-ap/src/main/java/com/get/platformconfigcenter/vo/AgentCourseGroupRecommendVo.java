package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName: AgentCourseGroupRecommend    // 类名，会自动填充
 * @Author: Eric          // 创建者
 * @Date: 2022/11/15 17:56   // 时间
 * @Version: 1.0     // 版本
 */
@Data
@ApiModel("代理配置课程大类推荐")
public class AgentCourseGroupRecommendVo extends BaseEntity implements Serializable {


    @ApiModelProperty(value = "课程大类名字")
    private String courseGroupName;

    @ApiModelProperty(value = "推荐学校ids")
    private Set<Long> InstitutionIdList;

    @ApiModelProperty(value = "推荐新闻ids")
    private Set<Long> newsIdList;

    @ApiModelProperty("推荐课程ids")
    private Set<Long> courseIdList;

    @ApiModelProperty(value = "课程类型组别Id")
    private Long fkCourseTypeGroupId;

    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;

    @ApiModelProperty("课程大类推荐学校，逗号分开")
    private Map<Long,String> institutionName;

    @ApiModelProperty(value = "代理资讯类型id")
    private Set<Long> informationList;

    @ApiModelProperty(value = "代理推荐新闻id")
    private Set<Long> examList;


}
