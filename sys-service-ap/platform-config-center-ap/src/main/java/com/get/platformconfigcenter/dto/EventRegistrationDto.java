package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: EventRegistrationDto
 * @Author: Eric
 * @Date: 2023/5/30 14:34
 * @Version: 1.0
 */
@Data
public class EventRegistrationDto extends BaseVoEntity {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名，涉及目标类型，如：m_competition，m_competition_item")
    private String fkTableName;

    @ApiModelProperty(value = "表Id，涉及目标Id")
    private Long fkTableId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否MSO会员，0否/1是")
    private Boolean isMember;

    @ApiModelProperty(value = "isAttend")
    private Boolean isAttend;

    @ApiModelProperty(value = "登记编号")
    private String registrationCode;

}
