package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 附件类型VO
 *
 * <AUTHOR>
 * @date 2021/5/20 15:51
 */
@Data
public class StudentAttachmentDto extends BaseVoEntity {

    /**
     * 文件类型Key（必填）
     */
    @ApiModelProperty(value = "文件类型Key（必填）")
    @NotBlank(message = "文件类型Key不能为空", groups = {Add.class, Update.class})
    private String typeKey;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    @NotBlank(message = "文件类型Key不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 附件备注
     */
    @ApiModelProperty(value = "附件备注")
    private String remark;

    /**
     * 可以上传文件类型：枚举，多选，格式如：pdf,jpg,jpeg
     */
    @ApiModelProperty(value = "可以上传文件类型：枚举，多选，格式如：pdf,jpg,jpeg")
    @NotBlank(message = "文件类型不能为空", groups = {Add.class, Update.class})
    private String fileExtension;

    /**
     * 文件大小限制（M单位）
     */
    @ApiModelProperty(value = "文件大小限制（M单位）")
    private Integer sizeLimit;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 是否加申上传要求：0否/1是
     */
    @ApiModelProperty(value = "是否加申上传要求：0否/1是")
    @Column(name = "is_multiple_required")
    private Boolean isMultipleRequired;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    @NotNull(message = "是否必不能为空", groups = {Add.class, Update.class})
    private Boolean isRequired;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 附件类型模板
     */
    @ApiModelProperty(value = "附件类型模板")
    private MediaAndAttachedDto attachmentTypeTemplate;
}
