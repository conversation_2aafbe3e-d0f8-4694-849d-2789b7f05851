package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * Created by <PERSON>.
 * Time: 10:03
 * Date: 2021/8/16
 * Description: 代理菜单返回类
 */
@Data
public class AgentSiteMapVo extends BaseEntity {
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 菜单key(必填，可用作逻辑定位)
     */
    @ApiModelProperty(value = "菜单key(必填，可用作逻辑定位)")
    @Column(name = "fk_menu_key")
    private String fkMenuKey;
}
