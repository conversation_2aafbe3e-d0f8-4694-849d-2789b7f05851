package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_issue_to_rpa_field_mapping")
public class IssueToRpaFieldMapping extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;
    /**
     * issue表名
     */
    @ApiModelProperty(value = "issue表名")
    @Column(name = "issue_table_name")
    private String issueTableName;
    /**
     * issue字段名
     */
    @ApiModelProperty(value = "issue字段名")
    @Column(name = "issue_field_name")
    private String issueFieldName;
    /**
     * issue标题名
     */
    @ApiModelProperty(value = "issue标题名")
    @Column(name = "issue_title_name")
    private String issueTitleName;
    /**
     * rpa字段名
     */
    @ApiModelProperty(value = "rpa字段名")
    @Column(name = "rpa_field_name")
    private String rpaFieldName;
}