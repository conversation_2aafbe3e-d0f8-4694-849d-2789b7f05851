package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 课程动态表单详情配置VO
 *
 * <AUTHOR>
 * @date 2021/5/20 15:51
 */
@Data
public class AppInstitutionCharacterItemDto extends BaseVoEntity {
    /**
     * 学校特殊申请信息Id
     */
    @ApiModelProperty(value = "学校特殊申请信息Id")
    private Long fkAppInstitutionCharacterId;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    @NotBlank(message = "字段名称不能为空", groups = {Add.class, Update.class})
    private String title;

    /**
     * 数据字段名
     */
    @ApiModelProperty(value = "数据字段名")
    @NotBlank(message = "数据字段名不能为空", groups = {Add.class, Update.class})
    private String fieldName;

    /**
     * 输入类型：0文本输入框/1下拉框/2日期选择框/3数字输入框
     */
    @ApiModelProperty(value = "输入类型：0文本输入框/1下拉框/2日期选择框/3数字输入框")
    @NotNull(message = "控件类型不能为空", groups = {Add.class, Update.class})
    private Integer inputType;

    /**
     * 数据类型：0字符串/1数字
     */
    @ApiModelProperty(value = "数据类型：0字符串/1数字")
    @NotNull(message = "数据类型不能为空", groups = {Add.class, Update.class})
    private Integer dataType;

    /**
     * 下拉数据源，录入格式如：[AAA][BBB][CCC]
     */
    @ApiModelProperty(value = "下拉数据源，录入格式如：[AAA][BBB][CCC]")
    private String dataSource;

    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    private Integer maxLength;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    private Boolean isRequired;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

}
