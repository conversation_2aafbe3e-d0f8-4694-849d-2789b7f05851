package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * 课程动态表单返回类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:38
 */
@Data
public class AppInstitutionCharacteVo extends BaseEntity {
    /**
     * 国家名字
     */
    @ApiModelProperty(value = "国家名字")
    @Column(name = "fk_area_country_id")
    private String fkAreaCountryName;

    /**
     * 学校名字
     */
    @ApiModelProperty(value = "学校名字")
    private String fkInstitutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkInstitutionCourseName;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

}
