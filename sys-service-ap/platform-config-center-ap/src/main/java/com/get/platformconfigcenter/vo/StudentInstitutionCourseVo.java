package com.get.platformconfigcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/7/6 18:32
 */
@Data
public class StudentInstitutionCourseVo extends BaseEntity {


    @ApiModelProperty("学校中文名")
    private String schoolName;

    @ApiModelProperty("课程名字")
    private String courseName;

    @ApiModelProperty("课程等级名字")
    private String MajorLeveName;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 申请国家id
     */
    @ApiModelProperty(value = "申请国家id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    @Column(name = "fk_institution_faculty_id")
    @UpdateWithNull
    private Long fkInstitutionFacultyId;


    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    @Column(name = "fk_institution_zone_id")
    @UpdateWithNull
    private Long fkInstitutionZoneId;

    /**
     * 课程等级id
     */
    @ApiModelProperty(value = "课程等级id")
    @Column(name = "fk_major_level_id")
    @UpdateWithNull
    private Long fkMajorLevelId;
    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 开学日期
     */
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @ApiModelProperty(value = "开学日期")
    @Column(name = "opening_time")
    private Date openingTime;

    /**
     * 课程名称（当下拉没有时填写）
     */
    @ApiModelProperty(value = "课程名称（当下拉没有时填写）")
    @Column(name = "institution_course_name")
    @UpdateWithNull
    private String institutionCourseName;

    /**
     * 课程官网链接
     */
    @ApiModelProperty(value = "课程官网链接")
    @Column(name = "institution_course_website")
    @UpdateWithNull
    private String institutionCourseWebsite;

    /**
     * 信息json
     */
    @ApiModelProperty(value = "信息json")
    @Column(name = "info_json")
    @UpdateWithNull
    private String infoJson;

    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    @ApiModelProperty(value = "状态：申请步骤状态")
    @Column(name = "status_step")
    private Integer statusStep;

    /**
     * 创建用户id
     */
    @ApiModelProperty("创建用户id")
    private Long gmtCreateUserId;

    @ApiModelProperty("提交时间")
    private Date submitTime;

    /**
     * 状态：0禁用（删除）/1激活
     */
    @ApiModelProperty(value = "状态：0禁用（删除）/1激活")
    private Integer status;

    @ApiModelProperty(value = "修改备注")
    private String modifyRemark;

}
