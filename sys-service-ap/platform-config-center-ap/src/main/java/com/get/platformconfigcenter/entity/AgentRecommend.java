package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_agent_recommend")
public class AgentRecommend extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 目标类型，枚举类型Key，如：INDEX_INSTITUTION（首页学校推荐8个）
     */
    @ApiModelProperty(value = "目标类型，枚举类型Key，如：INDEX_INSTITUTION（首页学校推荐8个）")
    @Column(name = "target_type")
    private String targetType;
    /**
     * 目标Id
     */
    @ApiModelProperty(value = "目标Id")
    @Column(name = "target_id")
    private Long targetId;

    @ApiModelProperty(value = "目标值")
    @Column(name = "target_value")
    private String targetValue;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}