package com.get.platformconfigcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.platformconfigcenter.entity.ProductNotices;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel(value = "产品Dto")
public class ProductDetailVo extends ProductVo {


    @ApiModelProperty(value = "产品详情背景图标")
    @JsonProperty("productBackgroundIcon")
    private String productIndexIcon;


    @ApiModelProperty(value = "产品服务项目详情")
    @JsonProperty("productItemList")
    private List<ProductItemVo> productItemList;

    @ApiModelProperty(value = "产品注意事项")
    @JsonProperty("productNoticesList")
    private List<ProductNotices> productNotices;


}
