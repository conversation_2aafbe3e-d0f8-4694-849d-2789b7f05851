package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_privacy_policy")
public class PrivacyPolicy extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    @Column(name = "policy_content")
    private String policyContent;
}