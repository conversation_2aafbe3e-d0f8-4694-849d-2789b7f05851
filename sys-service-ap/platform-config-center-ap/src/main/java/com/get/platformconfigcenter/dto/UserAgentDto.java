package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/10/26 17:23
 */
@Data
public class UserAgentDto extends BaseVoEntity {
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;

    @ApiModelProperty(value = "代理id（业务代理id")
    private Long fkAgentId;

    @ApiModelProperty("角色类型：0普通用户/1运营管理员")
    private Integer roleType;

    @ApiModelProperty("是否允许查看子代理数据：0否/1是")
    private Boolean isSubagentPermission;

    @ApiModelProperty("是否允许查看BMS数据：0否/1是")
    private Boolean isBmsPermission;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}
