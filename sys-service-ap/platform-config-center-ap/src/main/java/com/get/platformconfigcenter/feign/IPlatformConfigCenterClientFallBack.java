package com.get.platformconfigcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.platformconfigcenter.vo.*;
import com.get.platformconfigcenter.entity.UserInfo;
import com.get.platformconfigcenter.dto.UserInfoDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class  IPlatformConfigCenterClientFallBack implements IPlatformConfigCenterClient {

    @Override
    public Result<Map<Long, String>> getPrivacyPolicyTitlesByIds(Set<Long> ids) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getUserNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<List<UserInfo>> getUserInfoBySearch(UserInfoDto userInfoDto) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.fail("数据获取失败");
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.fail("数据获取失败");
    }



//    @Override
//    public Result<String> getRobotStatusById(Long orderId) {
//        return Result.fail("数据获取失败");
//    }

//    @Override
//    public Map<Long, String> getRobotStatusByIds(Set<Long> orderIds) {
//        return null;
//    }

//    @Override
//    public Map<Long, AplOrderVo> getRobotStatusAndMsgByIds(Set<Long> orderIds) {
//        return null;
//    }

//    @Override
//    public Result<Boolean> deleteValidateCourse(Long courseId) {
//        return Result.fail("操作失败");
//    }

//    @Override
//    public Result<List<UserInfoVo>> getUserByAgentId(Long fkAgentId, Long fkCompanyId) {
//        return Result.fail("数据获取失败");
//    }

//    @Override
//    public List<MediaAndAttachedVo> getStudentFiles(Long id) {
//        return null;
//    }

//    @Override
//    public Result<Map<Long, List<UserAgentVo>>> getUsersByAgentIds(Set<Long> fkAgentIds) {
//        return Result.fail("数据获取失败");
//    }

    @Override
    public Map<Long, UserInfo> getUserByStudentIds(Set<Long> issueIds) {
        return null;
    }

//    @Override
//    public Map<Long, UserInfo> getUserByofferItemIssueCourseIds(Set<Long> offerItemIssueCourseIds) {
//        return null;
//    }

//    @Override
//    public Result<Boolean> insertIssueAgentUser(Long fkAgentId, Long fkCompanyId, Long fkUserId) {
//        return Result.fail("操作失败");
//    }

//    @Override
//    public List<Long> getIssueStuIdsByAgentId(Long fkAgentId) {
//        return null;
//    }

//    @Override
//    public Result<Boolean> removeIssueRelationByAgentId(Long fkAgentId) {
//        return null;
//    }

//    @Override
//    public Result<Boolean> updateIssueStudentInstitutionCourseWithNull(StudentInstitutionCourse studentInstitutionCourse) {
//        return null;
//    }

//    @Override
//    public Result<Boolean> updateIssueStudentInstitutionCourse(StudentInstitutionCourse studentInstitutionCourse) {
//        return null;
//    }

//    @Override
//    public StudentInstitutionCourseVo getIssueStudentInstitutionCourseById(Long fkIssueCourseId) {
//        return null;
//    }
//    @Override
//    public Boolean updateIssueUserContactPersonTypeKeyByPersonId(Long fkContactPersonId,String fkContactPersonTypeKey) {
//        return false;
//    }
}
