package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel("")
@TableName("m_sale_order")
public class SaleOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单id，如果不同国家产品，系统自动拆单")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private Long fkUserId;

    @ApiModelProperty("国家管家id")
    private Long fkCountryButlerId;

    @ApiModelProperty("订单编号，规则：HKG20240826194523000001")
    private String orderNum;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("订单总价格")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付状态：0 '待支付', 1 '已支付', 3-用户取消，4-系统取消")
    private Integer paymentStatus;

}
