package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@TableName("m_sale_order_user_info")
public class SaleOrderUserInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("学生订单必填信息id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("订单id")
    private Long fkSaleOrderId;

    @ApiModelProperty("学生名字")
    private String studentName;

    @ApiModelProperty("学生联系电话")
    private String studentMobie;

    @ApiModelProperty("学生护照号码")
    private String studentPassport;

    @ApiModelProperty("学生入读学校")
    private String studentSchool;

    @ApiModelProperty("代理名字")
    private String agentName;

    @ApiModelProperty("代理电话")
    private String agentMobile;

    @ApiModelProperty("目标服务日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date serviceDate;


}
