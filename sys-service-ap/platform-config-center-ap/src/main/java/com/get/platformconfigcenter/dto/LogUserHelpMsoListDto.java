package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/7 15:47
 */
@Data
public class LogUserHelpMsoListDto extends BaseVoEntity {

    /**
     * 姓名（中文）
     */
    @ApiModelProperty(value = "姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    private String nameEn;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;
}
