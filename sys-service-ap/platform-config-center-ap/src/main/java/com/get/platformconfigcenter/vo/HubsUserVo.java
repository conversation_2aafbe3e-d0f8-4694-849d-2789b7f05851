package com.get.platformconfigcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2024/11/21 10:03
 * @verison: 1.0
 * @description:
 */
@Data
public class HubsUserVo {

    @ApiModelProperty("用户id")
    private Long id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("移动电话")
    private String mobile;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;



}
