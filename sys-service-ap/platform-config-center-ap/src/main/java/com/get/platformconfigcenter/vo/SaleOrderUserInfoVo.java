package com.get.platformconfigcenter.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.platformconfigcenter.entity.SaleOrderUserInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@TableName("m_sale_order_user_info")
public class SaleOrderUserInfoVo extends SaleOrderUserInfo {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("支付状态：0 '待支付', 1 '已支付', 3-用户取消，4-系统取消")
    private Integer paymentStatus;

    @ApiModelProperty("支付状态名称")
    private String paymentStatusName;

    @ApiModelProperty("订单总价格")
    private BigDecimal totalAmount;

    @ApiModelProperty("订单币种")
    private String fkCurrencyTypeNum;

}
