package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel("")
@TableName("m_country_butler")
public class CountryButler extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("国家管家id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty("国家管家名称")
    private String name;

    @ApiModelProperty("国家管家英文名称")
    private String nameEn;

    @ApiModelProperty("是否激活 0否，1是")
    private Boolean isActive;

    @ApiModelProperty("排序，数字大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    private String gmtModifiedUser;
}
