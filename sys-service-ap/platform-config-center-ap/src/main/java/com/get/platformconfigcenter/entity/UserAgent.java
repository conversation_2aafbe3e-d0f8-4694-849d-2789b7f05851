package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2022/3/7
 * @TIME: 14:54
 * @Description:
 **/
@Data
@TableName("r_user_agent")
public class UserAgent extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;

    @ApiModelProperty(value = "代理id（业务代理id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty("角色类型：0普通用户/1运营管理员")
    private Integer roleType;

    @ApiModelProperty("是否允许查看子代理数据：0否/1是")
    @Column(name = "is_subagent_permission")
    private Boolean isSubagentPermission;

    @ApiModelProperty("是否允许查看BMS数据：0否/1是")
    @Column(name = "is_bms_permission")
    private Boolean isBmsPermission;

    @ApiModelProperty("公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty("是否允许查看申请状态（默认否）：0否/1是")
    @Column(name = "is_show_app_status_permission")
    private Boolean isShowAppStatusPermission;

}
