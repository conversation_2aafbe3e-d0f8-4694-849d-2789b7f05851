package com.get.platformconfigcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "菜单树VO")
public class SitemapDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 父Id
     */
    @ApiModelProperty(value = "父Id")
    private Long fkParentSitemapId;
    /**
     * 菜单名称
     */
    @ApiModelProperty(value = "菜单名称")
    private String menuName;
    /**
     * 菜单key(必填，可用作逻辑定位)
     */
    @ApiModelProperty(value = "菜单key(必填，可用作逻辑定位)")
    private String menuKey;
    /**
     * 菜单链接类型：BLANK不需要链接/URL网址链接/COMPONENT模块链接/NEWS_INFO新闻资讯/COUNTRY_INFO国家资讯
     */
    @ApiModelProperty(value = "菜单链接类型：BLANK不需要链接/URL网址链接/COMPONENT模块链接/NEWS_INFO新闻资讯/COUNTRY_INFO国家资讯")
    private String menuLinkType;
    /**
     * 国家Id（不必选，国家资讯才需要选择）
     */
    @ApiModelProperty(value = "国家Id（不必选，国家资讯才需要选择）")
    private Long fkAreaCountryId;
    /**
     * 信息类型Id（国家资讯类型/新闻资讯类型）
     */
    @ApiModelProperty(value = "信息类型Id（国家资讯类型/新闻资讯类型）")
    private Long fkInfoTypeId;
    /**
     * 菜单链接(或前端调用的模块入口key)
     */
    @ApiModelProperty(value = "菜单链接(或前端调用的模块入口key)")
    private String menuLinkEntrance;
    /**
     * 菜单链接参数
     */
    @ApiModelProperty(value = "菜单链接参数")
    private String menuLinkParams;
    /**
     * 菜单链接描述
     */
    @ApiModelProperty(value = "菜单链接描述")
    private String menuLinkDescription;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    private String webTitle;
    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    private String webMetaDescription;
    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    private String webMetaKeywords;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    /**
     * 资讯类型图片
     */
    @ApiModelProperty(value = "资讯类型图片")
    private List<MediaAndAttachedDto> mediaAttachedVos;

    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @UpdateWithNull
    private Date effectiveStartTime;


    @ApiModelProperty(name = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @UpdateWithNull
    private Date effectiveEndTime;

}