package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/18 21:46
 */
@Data
public class UserAgentVo extends BaseEntity {
    @ApiModelProperty("用戶名稱")
    private String userName;

    @ApiModelProperty("注册平台")
    private String fkPlatformType;

    @ApiModelProperty("电话/账号")
    private String mobile;
    @ApiModelProperty(value = "注册中心的user id")
    @Column(name = "fk_user_id")
    private Long fkUserId;

    @ApiModelProperty(value = "代理id（业务代理id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty("角色类型：0普通用户/1运营管理员")
    private Integer roleType;

    @ApiModelProperty("是否允许查看子代理数据：0否/1是")
    @Column(name = "is_subagent_permission")
    private Boolean isSubagentPermission;

    @ApiModelProperty("是否允许查看BMS数据：0否/1是")
    @Column(name = "is_bms_permission")
    private Boolean isBmsPermission;

    @ApiModelProperty("公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty("是否允许查看申请状态（默认否）：0否/1是")
    @Column(name = "is_show_app_status_permission")
    private Boolean isShowAppStatusPermission;

    @ApiModelProperty("是否强制修改密码")
    private Boolean isModifiedPs;
}
