package com.get.platformconfigcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
public class ProductItemVo implements Serializable {

    @ApiModelProperty(value = "产品服务项目id - 对应fkTableName")
    @JsonProperty("id")
    private Long id;

    @ApiModelProperty(value = "产品相关服务项表名字,当前id就是fkTableId")
    @JsonProperty("fkTableName")
    private String fkTableName;

    @ApiModelProperty(value = "产品服务项目id")
    @JsonProperty("fkTableId")
    private Long fkTableId;

    @ApiModelProperty(value = "产品名称")
    @JsonProperty("productItemTypeName")
    private String productItemTypeName;

    @ApiModelProperty(value = "产品服务项目名称")
    @JsonProperty("productItemName")
    private String productItemName;

    @ApiModelProperty(value = "产品id")
    @JsonProperty("fkProductId")
    private Long fkProductId;

    @ApiModelProperty(value = "产品服务项目图标")
    @JsonProperty("productItemIcon")
   private String productItemIcon;

    @ApiModelProperty(value = "产品服务项目Top图标")
    @JsonProperty("productItemTopIcon")
    private String productItemTopIcon;

    @ApiModelProperty("服务内容")
    @JsonProperty("serviceContent")
    private String serviceContent;

    @ApiModelProperty("服务标准")
    @JsonProperty("serviceStandard")
    private String serviceStandard;

    @ApiModelProperty("排序，数字大到小排列")
    @JsonProperty("viewOrder")
    private Integer viewOrder;

    @ApiModelProperty(value = "产品子id,和productDetailDtoSub匹配")
    @JsonProperty("fkProductIdSub")
    private Long  fkProductIdSub;

    @ApiModelProperty("如果绑定的是产品，则有产品服务项目明细")
    @JsonProperty("productDetailDtoSub")
    private ProductDetailVo productDetailDtoSub;

    @ApiModelProperty("服务项单位")
    @JsonProperty("fkServiceUnitType")
    private String fkServiceUnitType;

    @ApiModelProperty(value = "产品服务单元名字")
    private String fkServiceUnitTypeName;

    @ApiModelProperty(value = "产品服务项目总计")
    private Integer serviceCountNumber;

    @ApiModelProperty("创建时间")
    @JsonProperty("gmtCreate")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    @JsonProperty("gmtModified")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    private String gmtModifiedUser;


}
