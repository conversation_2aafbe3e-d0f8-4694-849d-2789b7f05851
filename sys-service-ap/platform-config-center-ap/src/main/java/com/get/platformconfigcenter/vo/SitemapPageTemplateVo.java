package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 17:08
 * @Description:
 **/
@Data
public class SitemapPageTemplateVo extends BaseEntity {
    /**
     * 媒体附件-页面模板图片
     */
    @ApiModelProperty(value = "媒体附件-页面模板图片")
    private List<MediaAndAttachedVo> mediaAndAttachedDtoList;

    /**
     * 页面模板名称
     */
    @ApiModelProperty(value = "页面模板名称")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "配置json")
    @Column(name = "config_json")
    private String configJson;
}
