package com.get.platformconfigcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * 平台用户意向记录返回类
 *
 * <AUTHOR>
 * @date 2021/9/7 15:55
 */
@Data
public class LogUserHelpMsoVo extends BaseEntity {
    /**
     * 姓名（中文）
     */
    @ApiModelProperty(value = "姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    private String nameEn;

    /**
     * 姓（拼音）
     */
    @ApiModelProperty(value = "姓（拼音）")
    private String familyNamePy;

    /**
     * 名（拼音）
     */
    @ApiModelProperty(value = "名（拼音）")
    private String firstNamePy;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private String gender;

    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * 初次记录时间
     */
    @ApiModelProperty(value = "初次记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date firstGmtCreate;

    /**
     * 最新记录时间
     */
    @ApiModelProperty(value = "最新记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date lastGmtCreate;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 帮助Id
     */
    @ApiModelProperty(value = "帮助Id")
    @Column(name = "fk_help_id")
    private Long fkHelpId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "help_title")
    private String helpTitle;

}

