package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AgentCourseGroupRecommendDto extends BaseVoEntity implements Serializable {

    /**
     * 代理Id
     */
    @NotNull(message = "代理id不能为空",groups = {Add.class,Update.class})
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @NotNull(message = "课程类型组别Id不能为空",groups = {Add.class,Update.class})
    @ApiModelProperty(value = "课程类型组别Id")
    private Long fkCourseTypeGroupId;

    @ApiModelProperty(value = "推荐学校ids")
    private List<Long> institutionIdList;

    @ApiModelProperty(value = "推荐新闻ids")
    private List<Long> newsIdList;

    @ApiModelProperty("推荐课程ids")
    private List<Long> courseIdList;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "代理资讯类型id")
    private List<Long> informationList;

    @ApiModelProperty(value = "代理推荐新闻id")
    private List<Long> examList;


}
