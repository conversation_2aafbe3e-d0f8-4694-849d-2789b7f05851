package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 代理配置 VO类
 *
 * <AUTHOR>
 * @date 2021/5/13 10:11
 */
@Data
public class AgentInfoDto extends BaseVoEntity implements Serializable {

    @NotBlank(message = "系统KEY不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "系统KEY")
    private String keyCode;

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id", required = true)
    @NotNull(message = "代理不能为空", groups = {Add.class, Update.class})
    private Long fkAgentId;

    /**
     * 代理名字
     */
    @ApiModelProperty(value = "代理名字", required = true)
    private String fkAgentName;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活", required = true)
    @NotNull(message = "是否生效不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 访问域名（配置入口域名，唯一标识）
     */
    @ApiModelProperty(value = "访问域名（配置入口域名，唯一标识）")
    private String host;

    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题", required = true)
    @NotNull(message = "网页标题不能为空", groups = {Add.class, Update.class})
    private String websiteTitle;

    @ApiModelProperty(value = "代理联系邮箱，多个用分号隔开", required = true)
    @NotNull(message = "代理联系邮箱，多个用分号隔开", groups = {Add.class, Update.class})
    private String email;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactTel;

    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;

    /**
     * facebook
     */
    @ApiModelProperty(value = "facebook")
    private String facebook;

    /**
     * instagram
     */
    @ApiModelProperty(value = "instagram")
    private String instagram;

    /**
     * 私隐政策Id
     */
    @ApiModelProperty(value = "私隐政策Id")
    private Long fkPrivacyPolicyId;

    /**
     * 关于我们（首页关于我们内容）
     */
    @ApiModelProperty(value = "关于我们（首页关于我们内容）")
    private String aboutUs;

    /**
     * 联系我们（网站底部内容）
     */
    @ApiModelProperty(value = "联系我们（网站底部内容）")
    private String contactUs;

    /**
     * 菜单栏logo
     */
    @ApiModelProperty(value = "菜单栏logo")
    private List<MediaAndAttachedDto> topLogoMediaAttachedVos;

    /**
     * 页面底栏logo
     */
    @ApiModelProperty(value = "页面底栏logo")
    private List<MediaAndAttachedDto> bottomLogoMediaAttachedVos;

    /**
     * 轮播图
     */
    @ApiModelProperty(value = "轮播图")
    private List<MediaAndAttachedDto> indexBannerMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理移动端首页Banner")
    private List<MediaAndAttachedDto> mobileIndexMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理平板端首页Banner")
    private List<MediaAndAttachedDto> padIndexMediaAttachedVos;

    /**
     * 资讯轮播图
     */
    @ApiModelProperty(value = "资讯轮播图")
    private List<MediaAndAttachedDto> newsBannerMediaAttachedVos;

    @ApiModelProperty(value = "资讯移动端轮播图")
    private List<MediaAndAttachedDto> mobileNewsBannerMediaAttachedVos;

    @ApiModelProperty(value = "资讯平板轮播图")
    private List<MediaAndAttachedDto> padNewsBannerMediaAttachedVos;

    /**
     * DSE模块Banner
     */
    @ApiModelProperty(value = "DSE模块Banner")
    private List<MediaAndAttachedDto> dseBannerMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理移动端DSE模块Banner")
    private List<MediaAndAttachedDto> mobileDseMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理移动端DSE模块Banner")
    private List<MediaAndAttachedDto> padDseMediaAttachedVos;

    /**
     * DSE模块广告Banner
     */
    @ApiModelProperty(value = "DSE模块广告Banner")
    private List<MediaAndAttachedDto> advertsBannerMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理移动端DSE模块广告Banner")
    private List<MediaAndAttachedDto> mobileDseAdvertsMediaAttachedVos;

    @ApiModelProperty(value = "MSO平台代理平板端DSE模块广告Banner")
    private List<MediaAndAttachedDto> padDseAdvertsMediaAttachedVos;


    @ApiModelProperty(value = "推荐学校ids")
    private List<Long> institutionIdList;

    @ApiModelProperty(value = "DSE推荐学校ids")
    private List<Long> dseInstitutionIdList;

    @ApiModelProperty(value = "DSE推荐新闻ids")
    private List<Long> dseNewsIdList;

    @ApiModelProperty(value = "首页推荐新闻ids")
    private List<Long> indexNewsIdList;

    @ApiModelProperty(value = "网站推荐新闻")
    private List<Long> websiteNewsIdList;


    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    @ApiModelProperty(value = "桥梁学校")
    private List<BridgeInstitutionVo> bridgeInstitutionVo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BridgeInstitutionVo{
        @ApiModelProperty(value = "桥梁学校推荐id")
        private Long bridgeInstitutionId;

        @ApiModelProperty(value = "关联直升学校")
        private List<Long> verticalAscentInstitutionIdList;
    }
}