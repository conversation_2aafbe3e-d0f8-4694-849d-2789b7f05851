package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_permission_role_type_user")
public class IssueRPermissionRoleTypeUser extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "代理id（业务代理id）")
    private Long fkAgentId;

    @ApiModelProperty(value = "联系人类型Key")
    private String fkContactPersonTypeKey;

    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;
}
