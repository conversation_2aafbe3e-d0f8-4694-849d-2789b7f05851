package com.get.platformconfigcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.platformconfigcenter.vo.*;
import com.get.platformconfigcenter.entity.UserInfo;
import com.get.platformconfigcenter.dto.UserInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限 Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_PLATFORM_CONFIG_CENTER
)
public interface IPlatformConfigCenterClient {
    String API_PREFIX = "/feign";


    /**
     * feign调用 通过城市ids 查找对应的城市名称map
     */
    String GET_PRIVACY_POLICY_TITLES_BY_IDS = API_PREFIX + "/get-privacy-policy-titles-by-ids";
    /**
     * feign调用 根据userid获取名称
     */
    String GET_USER_NAMES_BY_USER_IDS = API_PREFIX + "/get-user-names-by-user-ids";
    /**
     * feign调用 根据userid获取名称（微信昵称）
     */
    String GET_USER_NICK_NAMES_BY_USER_IDS = API_PREFIX + "/get-user-nick-names-by-user-ids";
    /**
     * feign调用 根据userid获取手机号
     */
    String GET_MOBILE_BY_USER_IDS = API_PREFIX + "/get-mobile-by-user-ids";
    /**
     * feign调用 根据userid获取对象
     */
    String GET_USER_INFO_DTO_BY_IDS = API_PREFIX + "/get-user-info-dto-by-ids";
    /**
     * 条件查询用户
     */
    String GET_USER_INFO_BY_SEARCH = API_PREFIX + "/get-user-info-by-search";
    /**
     * 根据名称模糊搜索用户ids
     */
    String GET_USER_IDS_BY_PARAM = API_PREFIX + "/get-user-ids-by-param";
    /**
     * 根据ids获取人员对应的城市名称
     */
    String GET_CITY_NAMES_BY_USER_IDS = API_PREFIX + "/get-city-names-by-user-ids";
    /**
     * 根据名称模糊或者手机号搜索用户ids
     */
    String GET_USER_IDS_BY_NAME_OR_MOBILE = API_PREFIX + "/get-user-ids-by-name-or-mobile";
    /**
     * 获取申请状态
     */
    String GET_ROBOT_STATUS_BY_ID = API_PREFIX + "/get-robot-status-by-ids";
    /**
     * 获取申请状态
     */
    String GET_ROBOT_STATUS_BY_IDS = API_PREFIX + "/get-robot-status-by-ids";
    /**
     * 获取申请状态和错误信息
     */
    String GET_ROBOT_STATUS_AND_MSG_BY_IDS = API_PREFIX + "/get-robot-status-and-msg-by-ids";
    /**
     * 验证课程删除逻辑验证
     */
    String DELETE_VALIDATE_COURSE = API_PREFIX + "/delete-validate-course";
    /**
     * 根据代理获取ISSUE用户
     */
    String GET_USER_BY_AGENT_ID = API_PREFIX + "/get-user-by-agent-id";
    String GET_STUDENT_FILES = API_PREFIX + "/get-student-files";
    /**
     * 根据代理获取用户信息
     */
    String GET_USERS_BY_AGENT_IDS = API_PREFIX + "/get-users-by-agent-ids";
    String GET_USER_BY_ISSUE_IDS = API_PREFIX + "/get-user-by-issue-ids";
    String GET_USER_BY_OFFER_ITEM_ISSUE_COURSE_IDS = API_PREFIX + "/get-user-by-offer-item-issue-course-ids";
    String INSERT_ISSUE_AGENT_USER = API_PREFIX + "/insert-issue-agent-user";
    String GET_ISSUE_STU_IDS_BY_AGENT_ID = API_PREFIX + "/get-issue-stu-ids-by-agent-id";
    String REMOVE_ISSUE_RELATION_BY_AGENT_ID = API_PREFIX + "/remove-issue-relation-by-agent-id";
    String UPDATE_ISSUE_STUDENT_INSTITUTION_COURSE_WITH_NULL = API_PREFIX + "/update-issue-student-institution-course-with-null";
    String UPDATE_ISSUE_STUDENT_INSTITUTION_COURSE = API_PREFIX + "/update-issue-student-institution-course";
    String GET_ISSUE_STUDENT_INSTITUTION_COURSE_BY_ID = API_PREFIX + "/get-issue-student-institution-course-by-id";
    String UPDATE_ISSUE_USER_CONTACT_PERSON_TYPE_KEY_BY_PERSON_ID = API_PREFIX + "/update-issue-user-contact-person-type-key-by-person-id";

    /**
     * feign调用 通过城市ids 查找对应的城市名称map
     * @param ids
     * @return
     */
    @PostMapping(GET_PRIVACY_POLICY_TITLES_BY_IDS)
    Result<Map<Long, String>> getPrivacyPolicyTitlesByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 根据userid获取名称
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getUserNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * feign调用 根据userid获取名称（微信昵称）
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_NICK_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getUserNickNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * feign调用 根据userid获取手机号
     * @param userIds
     * @return
     */
    @PostMapping(GET_MOBILE_BY_USER_IDS)
    Result<Map<Long, String>> getMobileByUserIds(@RequestBody Set<Long> userIds);

    /**
     * feign调用 根据userid获取对象
     * @param userIds
     * @return
     */
    @PostMapping(GET_USER_INFO_DTO_BY_IDS)
    Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(@RequestBody Set<Long> userIds);

    /**
     * 条件查询用户
     * @param userInfoDto
     * @return
     */
    @PostMapping(GET_USER_INFO_BY_SEARCH)
    Result<List<UserInfo>> getUserInfoBySearch(@RequestBody UserInfoDto userInfoDto);

    /**
     * 根据名称模糊搜索用户ids
     * @param userName
     * @param fkAreaCityId
     * @param bdName
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_PARAM)
    Result<Set<Long>> getUserIdsByParam(@RequestParam(value = "userName", required = false) String userName,
                                        @RequestParam(value = "fkAreaCityId", required = false) Long fkAreaCityId,
                                        @RequestParam(value = "bdName", required = false) String bdName);

    /**
     * 根据ids获取人员对应的城市名称
     * @param userIds
     * @return
     */
    @PostMapping(GET_CITY_NAMES_BY_USER_IDS)
    Result<Map<Long, String>> getCityNamesByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 根据名称模糊或者手机号搜索用户ids
     * @param userName
     * @param phoneNumber
     * @return
     */
    @PostMapping(GET_USER_IDS_BY_NAME_OR_MOBILE)
    Result<Set<Long>> getUserIdsByNameOrMobile(@RequestParam(value = "userName", required = false) String userName,
                                               @RequestParam(value = "phoneNumber", required = false) String phoneNumber);

    /**
     * 获取申请状态
     * @param orderId
     * @return
     */
//    @GetMapping(GET_ROBOT_STATUS_BY_ID)
//    Result<String> getRobotStatusById(@RequestParam("orderId") Long orderId);

    /**
     * 获取申请状态
     * @param orderIds
     * @return
     */
//    @PostMapping(GET_ROBOT_STATUS_BY_IDS)
//    Map<Long, String> getRobotStatusByIds(@RequestBody Set<Long> orderIds);

//    @PostMapping(GET_ROBOT_STATUS_AND_MSG_BY_IDS)
//    Map<Long, AplOrderVo> getRobotStatusAndMsgByIds(@RequestBody Set<Long> orderIds);

    /**
     * 验证课程删除逻辑验证
     * @param courseId
     * @return
     */

//    @GetMapping(DELETE_VALIDATE_COURSE)
//    Result<Boolean> deleteValidateCourse(@RequestParam(value = "courseId") Long courseId);

    /**
     * 根据代理获取ISSUE用户
     * @param fkAgentId
     * @param fkCompanyId
     * @return
     */
//    @GetMapping(GET_USER_BY_AGENT_ID)
//    Result<List<UserInfoVo>> getUserByAgentId(@RequestParam("fkAgentId") Long fkAgentId, @RequestParam("fkCompanyId")Long fkCompanyId);

//    @GetMapping(GET_STUDENT_FILES)
//    List<MediaAndAttachedVo> getStudentFiles(@RequestParam("id") Long id);

    /**
     * 根据代理获取用户信息
     *
     * @param fkAgentIds
     * @return
     */
//    @PostMapping(GET_USERS_BY_AGENT_IDS)
//    Result<Map<Long,List<UserAgentVo>>> getUsersByAgentIds(@RequestBody Set<Long> fkAgentIds);

    /**
     * 通过学生id获取用户信息
     * @param issueIds
     * @return
     */
    @PostMapping(GET_USER_BY_ISSUE_IDS)
    Map<Long, UserInfo> getUserByStudentIds(@RequestParam("issueIds")Set<Long> issueIds);
    /**
     * 通过offerId获取用户信息
     * @param offerItemIssueCourseIds
     * @return
     */
//    @PostMapping(GET_USER_BY_OFFER_ITEM_ISSUE_COURSE_IDS)
//    Map<Long, UserInfo> getUserByofferItemIssueCourseIds(@RequestParam("offerItemIssueCourseIds") Set<Long> offerItemIssueCourseIds);

    /**
     * 代理添加r_user_agent数据
     * @param
     * @return
     */
//    @PostMapping(INSERT_ISSUE_AGENT_USER)
//    Result<Boolean>
//    insertIssueAgentUser(@RequestParam("fkAgentId") Long fkAgentId, @RequestParam("fkCompanyId")Long fkCompanyId,@RequestParam("fkUserId")Long fkUserId);

//    @PostMapping(GET_ISSUE_STU_IDS_BY_AGENT_ID)
//    List<Long> getIssueStuIdsByAgentId(@RequestParam("fkAgentId")Long fkAgentId);

//    @PostMapping(REMOVE_ISSUE_RELATION_BY_AGENT_ID)
//    Result<Boolean> removeIssueRelationByAgentId(@RequestParam("fkAgentId")Long fkAgentId);

//    @PostMapping(UPDATE_ISSUE_STUDENT_INSTITUTION_COURSE_WITH_NULL)
//    Result<Boolean> updateIssueStudentInstitutionCourseWithNull(@RequestBody StudentInstitutionCourse studentInstitutionCourse);

//    @PostMapping(UPDATE_ISSUE_STUDENT_INSTITUTION_COURSE)
//    Result<Boolean> updateIssueStudentInstitutionCourse(@RequestBody StudentInstitutionCourse studentInstitutionCourse);

//    @PostMapping(GET_ISSUE_STUDENT_INSTITUTION_COURSE_BY_ID)
//    StudentInstitutionCourseVo getIssueStudentInstitutionCourseById(@RequestParam("fkIssueCourseId")Long fkIssueCourseId);

//    @PostMapping(UPDATE_ISSUE_USER_CONTACT_PERSON_TYPE_KEY_BY_PERSON_ID)
//    Boolean updateIssueUserContactPersonTypeKeyByPersonId(@RequestParam("fkContactPersonId")Long fkContactPersonId,@RequestParam("fkContactPersonTypeKey")String fkContactPersonTypeKey);
}
