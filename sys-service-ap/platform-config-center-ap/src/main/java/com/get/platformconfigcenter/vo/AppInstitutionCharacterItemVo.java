package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * 课程动态表单详情配置返回类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:38
 */
@Data
public class AppInstitutionCharacterItemVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 学校特殊申请信息Id
     */
    @ApiModelProperty(value = "学校特殊申请信息Id")
    @Column(name = "fk_app_institution_character_id")
    private Long fkAppInstitutionCharacterId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    @Column(name = "field_name")
    private String fieldName;
    /**
     * 输入类型：0文本输入框/1下拉框/2日期选择框/3数字输入框
     */
    @ApiModelProperty(value = "输入类型：0文本输入框/1下拉框/2日期选择框/3数字输入框")
    @Column(name = "input_type")
    private Integer inputType;
    /**
     * 数据类型：0字符串/1数字
     */
    @ApiModelProperty(value = "数据类型：0字符串/1数字")
    @Column(name = "data_type")
    private Integer dataType;
    /**
     * 下拉数据源，录入格式如：[AAA][BBB][CCC]
     */
    @ApiModelProperty(value = "下拉数据源，录入格式如：[AAA][BBB][CCC]")
    @Column(name = "data_source")
    private String dataSource;
    /**
     * 最大字符限制数
     */
    @ApiModelProperty(value = "最大字符限制数")
    @Column(name = "max_length")
    private Integer maxLength;
    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    @Column(name = "is_required")
    private Boolean isRequired;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
