package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 信息收集内容配置Vo
 *
 * <AUTHOR>
 * @date 2021/5/21 11:58
 */
@Data
public class AppFormConfigDto extends BaseVoEntity {

    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    private Long fkMajorLevelId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 资料板块
     */
    @ApiModelProperty(value = "资料板块")
    private List<AppFormConfigDivisionDto> appFormConfigDivisionVoList;

    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private List<AppFormConfigAttachmentDto> appFormConfigAttachmentVoList;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
}
