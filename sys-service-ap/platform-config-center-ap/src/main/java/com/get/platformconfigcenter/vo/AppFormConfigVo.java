package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 信息收集内容配置返回类
 *
 * <AUTHOR>
 * @date 2021/5/21 11:57
 */
@Data
public class AppFormConfigVo extends BaseEntity {
    /**
     * 资料板块
     */
    @ApiModelProperty(value = "资料板块")
    private List<AppFormConfigDivisionVo> appFormConfigDivisionDtoList;

    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private List<AppFormConfigAttachmentVo> appFormConfigAttachmentDtoList;

    /**
     * 专业等级名字
     */
    @ApiModelProperty(value = "专业等级名字")
    private String fkMajorLevelName;

    /**
     * 国家名字
     */
    @ApiModelProperty(value = "国家名字")
    private String fkAreaCountryName;

    /**
     * 学校名字
     */
    @ApiModelProperty(value = "学校名字")
    private String fkInstitutionName;
    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}
