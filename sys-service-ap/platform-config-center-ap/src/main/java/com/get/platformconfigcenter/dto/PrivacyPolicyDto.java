package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: Hardy
 * @create: 2021/5/13 15:50
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "私隐条例VO")
public class PrivacyPolicyDto extends BaseVoEntity {
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    private String title;

    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    private String policyContent;
}
