package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel("")
@TableName("m_sale_order_item")
public class SaleOrderItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单子项id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("订单id")
    private Long fkSaleOrderId;

    @ApiModelProperty("产品id")
    private Long fkProductId;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("产品原价")
    private BigDecimal originalPrice;

    @ApiModelProperty("交易价格")
    private BigDecimal discountPrice;

    @ApiModelProperty("购买产品时，产品所拥有的产品属性JSON")
    private String productAttributesJson;
}
