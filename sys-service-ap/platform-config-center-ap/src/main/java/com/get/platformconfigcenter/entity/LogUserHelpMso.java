package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("log_user_help_mso")
public class LogUserHelpMso extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 帮助Id
     */
    @ApiModelProperty(value = "帮助Id")
    @Column(name = "fk_help_id")
    private Long fkHelpId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "help_title")
    private String helpTitle;
}