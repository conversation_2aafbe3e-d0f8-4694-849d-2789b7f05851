package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_agent_sitemap")
public class AgentSiteMap extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 菜单key(必填，可用作逻辑定位)
     */
    @ApiModelProperty(value = "菜单key(必填，可用作逻辑定位)")
    @Column(name = "fk_menu_key")
    private String fkMenuKey;
}