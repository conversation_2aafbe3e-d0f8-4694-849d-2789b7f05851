package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author:$ Eric
 * @date: 2022-11-04 16:47
 * @description:
 */
@Data
@TableName("m_agent_course_group_recommend")
public class AgentCourseGroupRecommend extends BaseEntity implements Serializable {

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty(value = "课程类型组别Id")
    @Column(name = "fk_course_type_group_id")
    private Long fkCourseTypeGroupId;

    @ApiModelProperty(value = "目标类型，枚举类型Key，如：INSTITUTION/NEWS（学校推荐/新闻推荐）")
    @Column(name = "target_type")
    private String targetType;

    @ApiModelProperty(value = "目标Id")
    @Column(name = "target_id")
    private Long targetId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;


}
