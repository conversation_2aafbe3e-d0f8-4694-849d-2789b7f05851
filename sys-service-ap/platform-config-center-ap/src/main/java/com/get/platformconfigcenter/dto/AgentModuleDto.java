package com.get.platformconfigcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description：国家资讯\代理模块 VO类
 * @Param
 * @Date 15:19 2021/5/11
 * <AUTHOR>
 */
@Data
public class AgentModuleDto extends BaseVoEntity implements Serializable {

    /**
     * 资讯类型名称
     */
    @ApiModelProperty(value = "资讯类型名称", required = true)
    @NotNull(message = "资讯类型名称不能为空", groups = {Add.class, Update.class})
    private String columnTitle;

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id", required = true)
    @NotNull(message = "代理不能为空", groups = {Add.class, Update.class})
    private Long fkAgentId;

    /**
     * 模块枚举：COUNTRY_CHANNEL国家频道/INFO_CHANNEL_COUNTRY资讯频道_国家/INFO_CHANNEL_NEWS资讯频道_新闻
     */
    @ApiModelProperty(value = "模块枚举：COUNTRY_CHANNEL国家频道/INFO_CHANNEL_COUNTRY资讯频道_国家/INFO_CHANNEL_NEWS资讯频道_新闻", required = true)
    @NotNull(message = "所属模块不能为空", groups = {Add.class, Update.class})
    private String moduleKey;

    /**
     * 国家Id（不必选，新闻资讯不需要选国家）
     */
    @ApiModelProperty(value = "国家Id（不必选，新闻资讯不需要选国家）")
    private Long fkAreaCountryId;

    /**
     * 信息类型Id（国家资讯类型/新闻资讯类型）（资讯类型和组件名称，2选一必填）
     */
    @ApiModelProperty(value = "信息类型Id（国家资讯类型/新闻资讯类型）（资讯类型和组件名称，2选一必填）")
    private Long fkInfoTypeId;

    /**
     * 页面组件名称（资讯类型和组件名称，2选一必填）
     */
    @ApiModelProperty(value = "页面组件名称（资讯类型和组件名称，2选一必填）")
    private String componentName;

    /**
     * 资讯类型图片
     */
    @ApiModelProperty(value = "资讯类型图片", required = true)
    @NotNull(message = "资讯类型图片不能为空", groups = {Add.class, Update.class})
    private List<MediaAndAttachedDto> mediaAttachedVos;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
