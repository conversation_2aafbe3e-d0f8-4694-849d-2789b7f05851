package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_student_attachment")
public class StudentAttachment extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 文件类型Key（必填）
     */
    @ApiModelProperty(value = "文件类型Key（必填）")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    @Column(name = "name")
    private String name;
    /**
     * 附件备注
     */
    @ApiModelProperty(value = "附件备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 可以上传文件类型：枚举，多选，格式如：pdf,jpg,jpeg
     */
    @ApiModelProperty(value = "可以上传文件类型：枚举，多选，格式如：pdf,jpg,jpeg")
    @Column(name = "file_extension")
    private String fileExtension;
    /**
     * 文件大小限制（M单位）
     */
    @ApiModelProperty(value = "文件大小限制（M单位）")
    @Column(name = "size_limit")
    private Integer sizeLimit;
    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    @Column(name = "is_required")
    private Boolean isRequired;
    /**
     * 是否加申上传要求：0否/1是
     */
    @ApiModelProperty(value = "是否加申上传要求：0否/1是")
    @Column(name = "is_multiple_required")
    private Boolean isMultipleRequired;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}