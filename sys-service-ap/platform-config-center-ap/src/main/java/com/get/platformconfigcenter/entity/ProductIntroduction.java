package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-09-03 10:27
 */
@Data
@ApiModel("")
@TableName("m_product_introduction")
public class ProductIntroduction implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品说明id")
    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty("id")
    private Long id;

    @ApiModelProperty("产品id")
    @JsonProperty("fkProductId")
    private Long fkProductId;

    @ApiModelProperty("产品说明")
    @JsonProperty("description")
    private String description;

    @ApiModelProperty("排序，数字大到小排列")
    @JsonProperty("viewOrder")
    private Integer viewOrder;

    @ApiModelProperty("创建时间")
    @JsonProperty("gmtCreate")
    private Date gmtCreate;

    @ApiModelProperty("创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    @JsonProperty("gmtModified")
    private Date gmtModified;

    @ApiModelProperty("修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    private String gmtModifiedUser;
}
