package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_user_institution_collection")
public class UserInstitutionCollection extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}