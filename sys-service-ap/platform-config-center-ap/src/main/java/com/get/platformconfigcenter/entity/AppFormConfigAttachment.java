package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_app_form_config_attachment")
public class AppFormConfigAttachment extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 申请单采集配置Id
     */
    @ApiModelProperty(value = "申请单采集配置Id")
    @Column(name = "fk_app_form_config_id")
    private Long fkAppFormConfigId;
    /**
     * 学生附件Id
     */
    @ApiModelProperty(value = "学生附件Id")
    @Column(name = "fk_student_attachment_id")
    private Long fkStudentAttachmentId;
}