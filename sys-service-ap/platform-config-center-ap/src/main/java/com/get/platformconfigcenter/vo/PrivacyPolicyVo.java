package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2021/5/14 15:49
 * @verison: 1.0
 * @description:
 */
@Data
public class PrivacyPolicyVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    @Column(name = "policy_content")
    private String policyContent;
}
