package com.get.platformconfigcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Author: Eric
 * @Date: 2024/9/23
 * @Description:
 * @Version 1.0
 */
@Data
public class AppHubsSearchOrderDto {
    @ApiModelProperty("用户id")
    private Long id;

    @ApiModelProperty("姓名(hubs注册用户列表专用)")
    private String name;

    @ApiModelProperty("移动电话")
    private String mobile;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;


    @ApiModelProperty(value = "名称")
    private String stuName;

    @ApiModelProperty(value = "下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private String createTime;

    @ApiModelProperty(value = "国家管家id")
    private Long fkCountryButlerId;

}
