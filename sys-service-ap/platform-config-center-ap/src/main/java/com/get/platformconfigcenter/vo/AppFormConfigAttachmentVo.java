package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2021/5/21 18:00
 */
@Data
public class AppFormConfigAttachmentVo extends BaseEntity {
    /**
     * 申请单采集配置Id
     */
    @ApiModelProperty(value = "申请单采集配置Id")
    @Column(name = "fk_app_form_config_id")
    private Long fkAppFormConfigId;
    /**
     * 学生附件Id
     */
    @ApiModelProperty(value = "学生附件Id")
    @Column(name = "fk_student_attachment_id")
    private Long fkStudentAttachmentId;
}
