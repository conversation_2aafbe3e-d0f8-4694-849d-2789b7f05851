package com.get.platformconfigcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @Description：国家资讯返回类
 * @Param
 * @Date 15:15 2021/5/11
 * <AUTHOR>
 */
@Data
@ApiModel("国家资讯返回类")
public class AgentModuleVo extends BaseEntity {
    /**
     * 媒体附件-资讯类型图片
     */
    @ApiModelProperty(value = "媒体附件-资讯类型图片")
    List<MediaAndAttachedVo> mediaAndAttachedDtoList;
    @ApiModelProperty(value = "所属模块名")
    private String moduleName;
    @ApiModelProperty(value = "国家名称")
    private String countryName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 代理名字
     */
    @ApiModelProperty(value = "代理名字")
    private String fkAgentName;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 模块枚举：COUNTRY_CHANNEL国家频道/INFO_CHANNEL_COUNTRY资讯频道_国家/INFO_CHANNEL_NEWS资讯频道_新闻
     */
    @ApiModelProperty(value = "模块枚举：COUNTRY_CHANNEL国家频道/INFO_CHANNEL_COUNTRY资讯频道_国家/INFO_CHANNEL_NEWS资讯频道_新闻")
    @Column(name = "module_key")
    private String moduleKey;
    /**
     * 国家Id（不必选，新闻资讯不需要选国家）
     */
    @ApiModelProperty(value = "国家Id（不必选，新闻资讯不需要选国家）")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 信息类型Id（国家资讯类型/新闻资讯类型）（资讯类型和组件名称，2选一必填）
     */
    @ApiModelProperty(value = "信息类型Id（国家资讯类型/新闻资讯类型）（资讯类型和组件名称，2选一必填）")
    @Column(name = "fk_info_type_id")
    private Long fkInfoTypeId;
    /**
     * 页面组件名称（资讯类型和组件名称，2选一必填）
     */
    @ApiModelProperty(value = "页面组件名称（资讯类型和组件名称，2选一必填）")
    @Column(name = "component_name")
    private String componentName;
    /**
     * 栏目标题（标题和图片必填）
     */
    @ApiModelProperty(value = "栏目标题（标题和图片必填）")
    @Column(name = "column_title")
    private String columnTitle;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
