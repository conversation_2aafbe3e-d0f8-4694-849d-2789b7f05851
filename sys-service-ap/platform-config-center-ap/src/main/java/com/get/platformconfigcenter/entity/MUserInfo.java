package com.get.platformconfigcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_user_info")
@ApiModel(value="MUserInfo对象", description="")
public class MUserInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户Id")
    private Long fkUserId;

    @ApiModelProperty(value = "联系人类型Key")
    private String fkContactPersonTypeKey;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "`name`")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名")
    @Column(name = "company")
    private String company;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Column(name = "department")
    private String department;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Column(name = "title")
    private String title;

    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;

    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    @Column(name = "tel_area_code")
    private String telAreaCode;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;
    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    @Column(name = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    @Column(name = "whatsapp")
    private String whatsapp;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "身份校验（手机或邮件验证），0否/1是")
    private Boolean isIdentityChecked;

    @ApiModelProperty(value = "是否强制修改密码，0否/1是")
    private Boolean isModifiedPs;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}
