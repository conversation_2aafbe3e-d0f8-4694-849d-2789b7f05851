package com.get.utils;



import com.get.vo.*;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.UUID;

public class MailBoxUtils {
    /**
     * 登录邮箱方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 返回邮箱登录实体类
     * @throws Exception 异常抛出
     */
    public static MailBox login(String username, String password, String host) throws Exception {
        // 设置属性
        Properties properties = new Properties();
        properties.put("mail.store.protocol", "imaps");
        properties.put("mail.imap.host", host);
        properties.put("mail.imap.port", "993");
        properties.put("mail.imap.ssl.enable", "true");

        Session session = Session.getInstance(properties);
        Store store = session.getStore("imap");
//        session.setDebug(true); // 启用调试日志
        store.connect(username, password);

//        // 获取所有文件夹
//        Folder[] folders = store.getDefaultFolder().list();
        MailBox mailBox = new MailBox();
        mailBox.setSession(session);
        mailBox.setStore(store);
        return mailBox;
    }

    public static int getMailType(Message message) throws Exception {
        // 1、已读; 2、未读; 3、星标邮件;
        int type = 1;
        MimeMessage mimeMessage = (MimeMessage) message;
        // 判断邮件是否已读
        boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
        if (!isRead) {
            type = 2;
        }
        boolean isStarred = mimeMessage.isSet(Flags.Flag.FLAGGED);
        if (isStarred) {
            type = 3;
        }
        return type;
    }

    private static String processNestedMultipart(MimeMultipart multipart) throws Exception {
        int partCount = multipart.getCount();
        String content = "";
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                content = String.valueOf(bodyPart.getContent());
            }
        }
        return content;
    }

    private static List<Mail> processMailBox(Message[] messages, String folderName) throws Exception {
        List<Mail> mailList = new ArrayList<>();
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            String title = message.getSubject();
            String from = ((InternetAddress) message.getFrom()[0]).getAddress();
            List<String> toName = new ArrayList<>();
            // 获取收件人信息
            Address[] recipients = message.getAllRecipients();
            if (recipients != null) {
                for (Address recipient : recipients) {
                    toName.add(((InternetAddress) recipient).getAddress());
                }
            }
            // 获取邮件内容
            Object mailContent = mimeMessage.getContent();
            // 获取邮件正文内容和附件
            String content = "";
            List<AnnexVo> annexList = new ArrayList<>();
            if (mailContent instanceof String) {
                content = (String) mailContent;
            } else if (mailContent instanceof Multipart) {
                MimeMultipart multipart = (MimeMultipart) mailContent;
                int partCount = multipart.getCount();
                for (int j = 0; j < partCount; j++) {
                    BodyPart bodyPart = multipart.getBodyPart(j);
                    if (bodyPart.isMimeType("text/plain")) {
                        content = String.valueOf(bodyPart.getContent());
                    } else if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                        // 获取附件名称
                        String fileName = bodyPart.getFileName();
                        AnnexVo annexVo = new AnnexVo();
                        annexVo.setAnnexName(fileName);
                        annexVo.setId(j);
                        annexList.add(annexVo);
                    } else if (bodyPart.isMimeType("multipart/*")) {
                        // 处理嵌套的多部分内容
                        content = processNestedMultipart((MimeMultipart) bodyPart.getContent());
                    }
                }
            }
            Mail mail = new Mail();
            mail.setDate(mimeMessage.getReceivedDate());
            mail.setMailID(mimeMessage.getMessageID());
            mail.setTitle(title);
            mail.setContent(content);
            mail.setFrom(from);
            mail.setAnnexs(annexList);
            mail.setToName(toName);
            switch (folderName) {
                case "inbox":
                    mail.setMailType(getMailType(message));
                    mail.setAiMailType(AiSortMail.sortByTitle(title));
                    break;
                case "drafts":
                    mail.setMailType(4);
                    mail.setAiMailType(7);
                    break;
                case "Sent Messages":
                    mail.setMailType(5);
                    mail.setAiMailType(7);
                    break;
                case "Deleted Messages":
                    mail.setMailType(6);
                    mail.setAiMailType(7);
                    break;
            }
            mailList.add(mail);
        }
        return mailList;
    }

    public static List<Mail> getMailBox(MailBox mailBox, String boxName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(boxName);
        folder.open(Folder.READ_ONLY);
        Message[] messages = folder.getMessages();
        return processMailBox(messages, boxName);
    }

    public static Session buildSendSession(String emailUsername, String emailPassword, String host) throws Exception {
        // 设置邮件会话属性
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true"); // 启用TLS加密
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", "587"); // SMTP服务器端口号
        // 创建Session实例对象
        return Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailUsername, emailPassword);
            }
        });
    }

    public static int sendMail(Session session, SendMailVo sendMailVo) throws Exception {
        // 创建MimeMessage对象
        Message message = new MimeMessage(session);
        // 发件人
        message.setFrom(new InternetAddress(sendMailVo.getEmailUsername()));
        // 接收人
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
        if (sendMailVo.getCCPeople() != null) {
            // 抄送人，多个抄送人之间用，号隔开
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCCPeople()));
        }
        // 邮件标题
        message.setSubject(sendMailVo.getSubject());
        BodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setText(sendMailVo.getContent());
        // 创建多部件消息
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);
        // 添加附件
        for (String annexPath : sendMailVo.getAnnexPaths()) {
            messageBodyPart = new MimeBodyPart();
            DataSource source = new FileDataSource(annexPath);
            messageBodyPart.setDataHandler(new DataHandler(source));
            messageBodyPart.setFileName(new File(annexPath).getName());
            multipart.addBodyPart(messageBodyPart);
        }
        // 将多部件消息设置为邮件内容
        message.setContent(multipart);
        // 发送邮件
        Transport.send(message);
        return 1;
    }

    private static byte[] readFully(InputStream inputStream) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        return baos.toByteArray();
    }

    private static void saveAttachment(BodyPart bodyPart, String fileName) throws Exception {
        // 创建输出文件
        File file = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 读取附件数据
            byte[] attachmentData = readFully(bodyPart.getInputStream());
            fos.write(attachmentData);
        }
    }


    public static String downAnnex(MailBox mailBox, String foldType, DownAnnexVo downAnnexVo) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldType);
        folder.open(Folder.READ_ONLY);
        Message[] messages = folder.getMessages();
        String AbsolutePath = "annex/";
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            if (downAnnexVo.getMailId().equals(mimeMessage.getMessageID())) {
                if (mimeMessage.isMimeType("multipart/*")) {
                    MimeMultipart mimeMultipart = (MimeMultipart) mimeMessage.getContent();
                    int partCount = mimeMultipart.getCount();
                    for (int i = 0; i < partCount; i++) {
                        BodyPart bodyPart = mimeMultipart.getBodyPart(i);
                        if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                            // 获取附件名称
                            String fileName = bodyPart.getFileName();
                            if (fileName.equals(downAnnexVo.getAnnexName()) && i == downAnnexVo.getAnnexId()) {
                                AbsolutePath = AbsolutePath + UUID.randomUUID() + fileName;
                                // 保存附件到本地
                                saveAttachment(bodyPart, AbsolutePath);
                            }
                        }
                    }
                }
            }
        }
        return new File(AbsolutePath).getAbsolutePath();
    }

    public static int changeMailState(MailBox mailBox, String mailBoxType, ChangeMailStateVo changeMailStateVo) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(mailBoxType);
        folder.open(Folder.READ_WRITE);
        Message[] messages = folder.getMessages();
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            if (changeMailStateVo.getMailId().equals(mimeMessage.getMessageID())) {
                if (changeMailStateVo.getState() == 1) {
                    // 标记为已读
                    message.setFlag(Flags.Flag.SEEN, true);
                } else if (changeMailStateVo.getState() == 2) {
                    // 标记为未读
                    message.setFlag(Flags.Flag.SEEN, false);
                } else if (changeMailStateVo.getState() == 3) {
                    // 标记为星标
                    message.setFlag(Flags.Flag.FLAGGED, true);
                } else if (changeMailStateVo.getState() == 4) {
                    // 取消星标
                    message.setFlag(Flags.Flag.FLAGGED, false);
                }
                break;
            }
        }
        return 1;
    }


    /*public static void main(String[] args) throws Exception {
        // 邮箱用户名
        String username = "<EMAIL>";
        // 邮箱密码
        String password = "Huatong@000";

        String host = "imap.exmail.qq.com";
        MailBox mailBox = login(username, password, host);
        SendMailVo sendMailVo = new SendMailVo();
        sendMailVo.setEmailUsername(username);
        sendMailVo.setRecipient("<EMAIL>");
        sendMailVo.setSubject("测试自动发邮件");
        sendMailVo.setContent("测试发送邮件内容");
        sendMailVo.setAnnexPath("E:\\work\\javawork\\email\\aisMail\\error.txt");

        sendMail(mailBox, sendMailVo);


    }*/
}
