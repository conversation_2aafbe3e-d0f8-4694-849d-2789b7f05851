package com.get.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;

@Data
@TableName(value = "USERS_MAIL_MAPPER")
public class UsersMail {
    @TableId(type = IdType.UUID)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private String id;

    @ApiModelProperty(value = "系统用户名")
    @Column(name = "username")
    private String username;

    @ApiModelProperty(value = "邮箱用户名")
    @Column(name = "email_username")
    private String emailUsername;

    @ApiModelProperty(value = "邮箱密码")
    @Column(name = "email_password")
    private String emailPassword;

    @ApiModelProperty(value = "邮箱类型")
    @Column(name = "email_type")
    private String emailType;

    @ApiModelProperty(value = "是否默认邮箱 1：是；0：否")
    @Column(name = "is_default")
    private int isDefault;
}
