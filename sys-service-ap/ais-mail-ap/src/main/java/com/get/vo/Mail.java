package com.get.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Mail {
    private String mailID;
    private List<String> toName;
    private String from;
    private String title;
    private String content;
    // 1、已读; 2、未读; 3、星标邮件; 4、草稿箱; 5、已发送; 6、已删除
    private int mailType;
    // 1、新申请; 2、提交完成; 3、已录取; 4、已付学费; 5、收到签证涵; 6、获得签证; 7、其他
    private int aiMailType;
    private List<AnnexVo> annexs;
    private Date date;
}
