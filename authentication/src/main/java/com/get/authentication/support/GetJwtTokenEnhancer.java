package com.get.authentication.support;

import com.get.authentication.service.GetUserDetails;
import com.get.authentication.utils.TokenUtil;
import com.get.core.jwt.JwtUtil;
import com.get.core.jwt.props.JwtProperties;
import com.get.core.tool.utils.GeneralTool;
import lombok.AllArgsConstructor;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * jwt返回参数增强
 */
@AllArgsConstructor
public class GetJwtTokenEnhancer implements TokenEnhancer {

    private final JwtAccessTokenConverter jwtAccessTokenConverter;
    private final JwtProperties jwtProperties;

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        GetUserDetails principal = (GetUserDetails) authentication.getUserAuthentication().getPrincipal();
        //token参数增强
        Map<String, Object> info = new HashMap<>(16);
        info.put(TokenUtil.CLIENT_ID, TokenUtil.getClientIdFromHeader());
        info.put(TokenUtil.STAFFID, GeneralTool.toStr(principal.getStaffId()));
        info.put(TokenUtil.FKCOMPANYID, GeneralTool.toStr(principal.getFkCompanyId()));
//		info.put(TokenUtil.COMPANYIDS, GeneralTool.join(principal.getCompanyIds(),","));//逗号分割
//		info.put(TokenUtil.COUNTRYIDS, GeneralTool.join(principal.getCountryIds(),","));//逗号分割
        info.put(TokenUtil.OAUTH_ID, principal.getOauthId());
        info.put(TokenUtil.SESSION_ID,principal.getTokenSessionId());
//		info.put(TokenUtil.FKDEPARTMENTID, GeneralTool.toStr(principal.getFkDepartmentId()));
//		info.put(TokenUtil.FKPOSITIONID, GeneralTool.toStr(principal.getFkPositionId()));
//		info.put(TokenUtil.FKOFFICEID, GeneralTool.toStr(principal.getFkOfficeId()));
//		info.put(TokenUtil.FKSTAFFIDSUPERVISOR, GeneralTool.toStr(principal.getFkStaffIdSupervisor()));
//		info.put(TokenUtil.FKRESUMEGUID, GeneralTool.toStr(principal.getFkResumeGuid()));
        info.put(TokenUtil.LOGINID, principal.getLoginId());
        info.put(TokenUtil.LOGIN_TIME, new Date());
//		info.put(TokenUtil.NUM, principal.getNum());
        info.put(TokenUtil.NAME, principal.getName());
//		info.put(TokenUtil.NAMEEN, principal.getNameEn());
        info.put(TokenUtil.GENDER, principal.getGender());
//		info.put(TokenUtil.ISACTIVE, principal.getIsActive()==true?"true":"false");
        info.put(TokenUtil.ISADMIN, principal.getIsAdmin() ? "true" : "false");
//		info.put(TokenUtil.ISMODIFIEDRESUME, principal.getIsModifiedResume()==true?"true":"false");
//		info.put(TokenUtil.AVATAR, principal.getAvatar());
        info.put(TokenUtil.LICENSE, TokenUtil.LICENSE_NAME);
        info.put(TokenUtil.PLATFORM_TYPE, principal.getPlatformType());
        ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(info);

        //token状态设置
        if (jwtProperties.getState()) {
            OAuth2AccessToken oAuth2AccessToken = jwtAccessTokenConverter.enhance(accessToken, authentication);
            String tokenValue = oAuth2AccessToken.getValue();
            String loginId = GeneralTool.toStr(principal.getStaffId());
            JwtUtil.addAccessToken(loginId,principal.getTokenSessionId(), tokenValue, accessToken.getExpiresIn());
        }

        return accessToken;
    }
}
