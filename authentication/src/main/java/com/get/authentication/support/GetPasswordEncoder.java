package com.get.authentication.support;

import com.get.common.consts.AESConstant;
import com.get.common.utils.AESUtils;
import com.get.common.utils.MD5Utils;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 自定义密码加密
 */
public class GetPasswordEncoder implements PasswordEncoder {

    @Override
    public String encode(CharSequence rawPassword) {
        return MD5Utils.encrypt((String) rawPassword);
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        if (rawPassword != null) {
            try {
                rawPassword = AESUtils.Decrypt(rawPassword.toString(), AESConstant.AES_LOGIN_KEY);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return encodedPassword.equals(encode(rawPassword));
    }

}
