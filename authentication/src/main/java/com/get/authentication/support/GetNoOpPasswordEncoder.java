package com.get.authentication.support;

import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 无密码加密
 */
public class GetNoOpPasswordEncoder implements PasswordEncoder {

    private static final PasswordEncoder INSTANCE = new GetNoOpPasswordEncoder();

    private GetNoOpPasswordEncoder() {
    }

    /**
     * Get the singleton {@link GetNoOpPasswordEncoder}.
     */
    public static PasswordEncoder getInstance() {
        return INSTANCE;
    }

    @Override
    public String encode(CharSequence rawPassword) {
        return rawPassword.toString();
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        return rawPassword.toString().equals(encodedPassword);
    }

}
