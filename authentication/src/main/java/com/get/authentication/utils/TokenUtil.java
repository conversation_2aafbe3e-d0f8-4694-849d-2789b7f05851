package com.get.authentication.utils;

import com.get.core.start.constant.TokenConstant;
import com.get.core.tool.utils.Charsets;
import com.get.core.tool.utils.StringPool;
import com.get.core.tool.utils.WebUtil;
import lombok.SneakyThrows;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;

import java.util.Base64;
import java.util.Calendar;
import java.util.UUID;

public class TokenUtil {

    public final static String AVATAR = TokenConstant.AVATAR;
    public final static String HEADER = TokenConstant.HEADER;
    public final static String STAFFID = TokenConstant.STAFFID;
    public final static String FKCOMPANYID = TokenConstant.FKCOMPANYID;
    public final static String COMPANYIDS = TokenConstant.COMPANYIDS;
    public final static String COUNTRYIDS = TokenConstant.COUNTRYIDS;
    public final static String FKDEPARTMENTID = TokenConstant.FKDEPARTMENTID;
    public final static String FKPOSITIONID = TokenConstant.FKPOSITIONID;
    public final static String FKOFFICEID = TokenConstant.FKOFFICEID;
    public final static String FKSTAFFIDSUPERVISOR = TokenConstant.FKSTAFFIDSUPERVISOR;
    public final static String FKRESUMEGUID = TokenConstant.FKRESUMEGUID;
    public final static String LOGINID = TokenConstant.LOGINID;
    public final static String LOGIN_TIME = TokenConstant.LOGIN_TIME;
    public final static String NUM = TokenConstant.NUM;
    public final static String NAME = TokenConstant.NAME;
    public final static String NAMEEN = TokenConstant.NAMEEN;
    public final static String GENDER = TokenConstant.GENDER;
    public final static String SESSION_ID = TokenConstant.SESSION_ID;
    public final static String ISACTIVE = TokenConstant.ISACTIVE;
    public final static String ISADMIN = TokenConstant.ISADMIN;
    public final static String ISMODIFIEDRESUME = TokenConstant.ISMODIFIEDRESUME;
    public final static String OAUTH_ID = TokenConstant.OAUTH_ID;
    public final static String CLIENT_ID = TokenConstant.CLIENT_ID;
    public final static String DETAIL = TokenConstant.DETAIL;
    public final static String LICENSE = TokenConstant.LICENSE;
    public final static String LICENSE_NAME = TokenConstant.LICENSE_NAME;

    public final static String PLATFORM_TYPE = "platformType";


    public final static String CAPTCHA_HEADER_KEY = "Captcha-Key";
    public final static String CAPTCHA_HEADER_CODE = "Captcha-Code";
    public final static String CAPTCHA_NOT_CORRECT = "验证码错误";
    public final static String SVR = "请进行二次验证";
    public final static String CAP_ERROR = "请输入正确的验证码";
    public final static String USER_TYPE_HEADER_KEY = "User-Type";
    public final static String DEFAULT_USER_TYPE = "web";
    public final static String USER_NOT_FOUND = "用户名或密码错误";
    public final static String USER_HAS_NO_ROLE = "未获得用户的角色信息";
    public final static String USER_HAS_TOO_MANY_FAILS = "登录错误次数过多,请稍后再试";
    public final static String USER_HAS_NOT_ACTIVE = "用户尚未激活";
    public final static String HEADER_KEY = "Authorization";
    public final static String HEADER_PREFIX = "Basic ";
    public final static String DEFAULT_AVATAR = "";
    public final static String PASSWORD_KEY = "password";
    public final static String GRANT_TYPE_KEY = "grant_type";
    public final static String REFRESH_TOKEN_KEY = "refresh_token";

    /**
     * 解码
     */
    @SneakyThrows
    public static String[] extractAndDecodeHeader() {
        String header = WebUtil.getRequest().getHeader(TokenUtil.HEADER_KEY);
        if (header == null || !header.startsWith(TokenUtil.HEADER_PREFIX)) {
            throw new UnapprovedClientAuthenticationException("请求头中无client信息");
        }

        byte[] base64Token = header.substring(6).getBytes(Charsets.UTF_8_NAME);

        byte[] decoded;
        try {
            decoded = Base64.getDecoder().decode(base64Token);
        } catch (IllegalArgumentException var7) {
            throw new BadCredentialsException("Failed to decode basic authentication token");
        }

        String token = new String(decoded, Charsets.UTF_8_NAME);
        int index = token.indexOf(StringPool.COLON);
        if (index == -1) {
            throw new BadCredentialsException("Invalid basic authentication token");
        } else {
            return new String[]{token.substring(0, index), token.substring(index + 1)};
        }
    }

    /**
     * 获取请求头中的客户端id
     */
    public static String getClientIdFromHeader() {
        String[] tokens = extractAndDecodeHeader();
        return tokens[0];
    }
    /**
     * 获取token过期的时间
     * (次日凌晨3点)
     *
     * @return expire
     */
    public static int getTokenValiditySecond() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 3);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    /**
     * 获取refreshToken过期的时间
     *
     * @return expire
     */
    public static int getRefreshTokenValiditySeconds() {
        return 60 * 60 * 24 * 15;
    }

}
