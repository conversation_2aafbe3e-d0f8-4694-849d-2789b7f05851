package com.get.authentication.service;

import com.alibaba.fastjson.JSONObject;
import com.get.authentication.handle.ExtCaptchaApplicationService;
import com.get.authentication.support.ExtImageCaptchaVo;
import com.get.authentication.utils.TokenUtil;
import com.get.common.cache.CacheNames;
import com.get.common.consts.AESConstant;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.common.utils.MD5Utils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.feign.ILogClient;
import com.get.core.log.model.LogLogin;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.WebUtil;
import com.get.permissioncenter.vo.StaffInfoVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.management.ServiceNotFoundException;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GetUserDetailsServiceImpl implements UserDetailsService {

    @Value("${login.fail-count}")
    private int failCount;

    private final IPermissionCenterClient permissionCenterClient;
//    private final ISchoolGateCenterClient schoolGateCenterClient;
    private final GetRedis getRedis;

    @Resource
    private ILogClient logClient;

    @Autowired
    private ExtCaptchaApplicationService extCaptchaApplicationService;

    /**
     * 校验验证码
     * @param request
     * @return
     */
    private boolean validCaptcha(HttpServletRequest request){
        try {
            BufferedReader reader = request.getReader();
            StringBuilder builder = new StringBuilder();
            String line = reader.readLine();
            while(line != null){
                builder.append(line);
                line = reader.readLine();
            }
            reader.close();

            String reqBody = builder.toString();
            ExtImageCaptchaVo extImageCaptchaVo = JSONObject.parseObject(reqBody, ExtImageCaptchaVo.class);
            //校验滑块参数
            boolean matching = extCaptchaApplicationService.matching(extImageCaptchaVo.getId(), extImageCaptchaVo.getImageCaptchaTrack());
            extCaptchaApplicationService.remove(extImageCaptchaVo.getId());
            return matching;
        }catch (Exception e){
             log.info("验证失败");
        }
        return false;
    }

    @Override
    @SneakyThrows
    public GetUserDetails loadUserByUsername(String userName) {
        HttpServletRequest request = WebUtil.getRequest();
        String password = request.getParameter(TokenUtil.PASSWORD_KEY);
        String avatarLogin = request.getParameter("avatarLogin");
        String isAvatarLogin = request.getParameter("isAvatarLogin");
        String verifyCode = request.getParameter("verifyCode");
        String platformType = request.getParameter("platformType");
        String isValidCaptcha = request.getParameter("isValidCaptcha");

        //增加AES解密密码
        if (GeneralTool.isNotEmpty(password)) {
            password = AESUtils.Decrypt(password, AESConstant.AES_LOGIN_KEY);
        }
        if(GeneralTool.isNotEmpty(platformType) && ProjectKeyEnum.SCHOOL_GATE.key.equals(platformType)){
            if (GeneralTool.isNotEmpty(password)) {
                password = MD5Utils.encrypt(password);
            }

            // 远程调用返回员工数据，这里的密码需要和注册时的密码保持一致
//            Result<SchoolGateStaffDto> result = schoolGateCenterClient.staffInfo(userName, password, isAvatarLogin);
//            if (result.isSuccess()) {
//                SchoolGateStaffDto staffDto = result.getData();
//                // 员工不存在,但提示用户名与密码错误
//                if (staffDto == null || staffDto.getId() == null) {
//                    throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
//                }
//                if (GeneralTool.isEmpty(staffDto.getIsActive()) || !staffDto.getIsActive()) {
//                    throw new UsernameNotFoundException(TokenUtil.USER_HAS_NOT_ACTIVE);
//                }
//                String oauthId = "";//扩展第三方登录时使用
//                //Long staffId, String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities,                                                                                    Long fkCompanyId, String loginId,String num, String name, String nameEn, Integer gender, Boolean isActive, Boolean isAdmin, String sessionId, Boolean isModifiedResume, String oauthId, Date loginTime
//                return new GetUserDetails(staffDto.getId(), CommonUtil.getTokenSessionId(), staffDto.getLoginId(), "{get}" + password, true, true, true, true, AuthorityUtils.commaSeparatedStringToAuthorityList(String.valueOf(staffDto.getId())), null, staffDto.getLoginId(), staffDto.getNum(), staffDto.getName(), staffDto.getNameEn(), staffDto.getGender(), staffDto.getIsActive(), staffDto.getIsAdmin(), staffDto.getSessionId(), staffDto.getIsModifiedResume(), oauthId, new Date(),ProjectKeyEnum.SCHOOL_GATE.key);
//            } else {
//                throw new ServiceNotFoundException(result.getMessage());
//            }
        }
//        else if (GeneralTool.isNotEmpty(platformType) && ProjectKeyEnum.APP_SCHOOL_GATE.key.equals(platformType)){
//            if (GeneralTool.isNotEmpty(password)) {
//                password = MD5Utils.encrypt(password);
//            }
//
//            Result<UserDto> result  =   schoolGateCenterClient.loginByAuth(userName, password);
//            if (result.isSuccess()) {
//                UserDto userDto = result.getData();
//                // 员工不存在,但提示用户名与密码错误
//                if (userDto == null || userDto.getId() == null) {
//                    throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
//                }
//                String oauthId = "";//扩展第三方登录时使用
//                return new GetUserDetails(userDto.getId(),CommonUtil.getTokenSessionId(),userDto.getLoginId(),"{get}"+password,true, true, true, true,
//                        AuthorityUtils.commaSeparatedStringToAuthorityList(String.valueOf(userDto.getId())),null,userDto.getLoginId(),
//                        userDto.getNum(),userDto.getName(),userDto.getNameEn(),userDto.getGender(),userDto.getIsActive(),false,null,null,oauthId,new Date(),ProjectKeyEnum.APP_SCHOOL_GATE.key);
//            }else {
//                throw new ServiceNotFoundException(result.getMessage());
//            }
//        }

        else{
            //1为验证码登录
            boolean captchaFlag = "1".equals(isValidCaptcha);
            //校验验证码
            if (captchaFlag && !validCaptcha(request)) {
                throw new GetServiceException("0003");
            }
            //        System.out.println("=====>是否超管登录：" + isAvatarLogin);
//		String grantType = request.getParameter(TokenUtil.GRANT_TYPE_KEY);//判断是否刷新token会用

            // 判断登录是否锁定，错误超过5次则锁定30分钟
            int count = getFailCount(userName);
            if (!captchaFlag) {
                if (count >= failCount) {
                    throw new GetServiceException("0004");
                }
            }
            if (GeneralTool.isNotEmpty(password)) {
                password = MD5Utils.encrypt(password);
            }
            // 远程调用返回员工数据，这里的密码需要和注册时的密码保持一致
            Result<StaffInfoVo> result = permissionCenterClient.staffInfo(userName, password, isAvatarLogin, avatarLogin);
            if (result.isSuccess()) {
                StaffInfoVo staffInfoVo = result.getData();
                // 员工不存在,但提示用户名与密码错误
                if (staffInfoVo == null || staffInfoVo.getId() == null) {
                    if (count >= failCount) {
                        throw new GetServiceException("0004");
                    }
                    setFailCount(userName, count);
                    this.saveLoginLog(request, userName, staffInfoVo);
                    throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
                }
                String ipWhiteList = staffInfoVo.getIpWhiteList();
                if (StringUtils.isNotBlank(ipWhiteList)) {
                    List<String> list = Arrays.asList(ipWhiteList.split(";"));
                    String ip = WebUtil.getIP();
                    if (!list.contains(ip)) {
                        throw new InvalidGrantException(LocaleMessageUtils.getMessage("permissions_insufficient"));
                    }
                }
                Boolean required = staffInfoVo.getIsVcodeRequired();
                if (required != null && required && !"1".equals(isAvatarLogin)) {
                    String key  = CacheNames.USER_CACHE + ":smsCode" + ":"+ staffInfoVo.getId() + ":";
                    Object o = getRedis.getValueOps().get(key);
                    if (!GeneralTool.isNull(o)) {
                        if (String.valueOf(o).equals(verifyCode)) {
                            getRedis.del(key);
                        }else {
                            if (count >= failCount) {
                                throw new GetServiceException("0004");
                            }
                            setFailCount(userName, count);
                            throw new InvalidGrantException("验证码错误");
                        }
                    }else {
                        throw new InvalidGrantException(TokenUtil.SVR);
                    }
                }

                if (GeneralTool.isEmpty(staffInfoVo.getIsActive()) || !staffInfoVo.getIsActive()) {
                    this.saveLoginLog(request, userName, staffInfoVo);
                    throw new UsernameNotFoundException(TokenUtil.USER_HAS_NOT_ACTIVE);
                }
                //登录成功写入日志
                this.saveLoginLog(request, userName, staffInfoVo);
                String oauthId = "";//扩展第三方登录时使用
                //登陆成功清除失败记录
                getRedis.del(CacheNames.cacheKey(CacheNames.USER_FAIL_KEY, userName));
                //Long staffId, String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities,                                                                                    Long fkCompanyId, String loginId,String num, String name, String nameEn, Integer gender, Boolean isActive, Boolean isAdmin, String sessionId, Boolean isModifiedResume, String oauthId, Date loginTime
                return new GetUserDetails(staffInfoVo.getId(),staffInfoVo.getTokenSessionId(), staffInfoVo.getLoginId(), "{get}" + password, true, true, true, true, AuthorityUtils.commaSeparatedStringToAuthorityList(String.valueOf(staffInfoVo.getId())), staffInfoVo.getFkCompanyId(), staffInfoVo.getLoginId(), staffInfoVo.getNum(), staffInfoVo.getName(), staffInfoVo.getNameEn(), staffInfoVo.getGender(), staffInfoVo.getIsActive(), staffInfoVo.getIsAdmin(), staffInfoVo.getSessionId(), staffInfoVo.getIsModifiedResume(), oauthId, new Date(),null);
            } else {
                this.saveLoginLog(request, userName, null);
                throw new ServiceNotFoundException(result.getMessage());
            }
        }

        return null;
    }

    private int getFailCount(String username) {
        return GeneralTool.toInt(getRedis.get(CacheNames.cacheKey(CacheNames.USER_FAIL_KEY, username)), 0);
    }

    private void setFailCount(String username, int count) {
        getRedis.setEx(CacheNames.cacheKey(CacheNames.USER_FAIL_KEY, username), count + 1, Duration.ofMinutes(30));
    }

    private void saveLoginLog(HttpServletRequest request, String userName, StaffInfoVo staffInfoVo) {
        //通过feign写入登录日志
        LogLogin logLogin = new LogLogin();
        logLogin.setIp(WebUtil.getIP(request));
        logLogin.setLoginTime(new Date());
        logLogin.setStaffLoginId(userName);
        logLogin.setGmtCreate(new Date());
        if (staffInfoVo != null) {
            logLogin.setSessionId(staffInfoVo.getTokenSessionId());
            logLogin.setFkStaffId(staffInfoVo.getId());
            logLogin.setStaffName(staffInfoVo.getName());
            logLogin.setGmtCreateUser(userName);
            logLogin.setFkCompanyId(staffInfoVo.getFkCompanyId());
        }
        logClient.saveLogLogin(logLogin);
    }

    public UserDetails loadUserByUserId(String userId) {
        System.out.println("登陆成功");
        return null;
    }
}
