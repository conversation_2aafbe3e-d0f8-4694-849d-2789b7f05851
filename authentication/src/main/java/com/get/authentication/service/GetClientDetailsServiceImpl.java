package com.get.authentication.service;

import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * 客户端信息：校验客户端头部的auth信息
 */
@Component
public class GetClientDetailsServiceImpl extends JdbcClientDetailsService {

    public GetClientDetailsServiceImpl(DataSource dataSource) {
        super(dataSource);
    }

    /**
     * 缓存客户端信息
     */
    @Override
    public ClientDetails loadClientByClientId(String clientId) {
        try {
            return super.loadClientByClientId(clientId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }
}
