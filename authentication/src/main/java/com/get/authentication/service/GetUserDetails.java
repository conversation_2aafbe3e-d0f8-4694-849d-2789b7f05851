package com.get.authentication.service;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * OAuth用户信息
 */
@Getter
public class GetUserDetails extends User {
    private static final long serialVersionUID = 1L;
    /**
     * 后端权限
     */
    Set<String> apiKeys;
    /**
     * 前端资源
     */
    List<String> resourceKeys;
    /**
     * 员工关联国家的id
     */
    List<Long> countryIds;
    /**
     * 员工当前公司及下属公司的id
     */
    List<Long> companyIds;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    private Long staffId;
    /**
     * 公司Id
     */
    @ApiModelProperty("公司Id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty("部门Id")
    private Long fkDepartmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty("职位Id")
    private Long fkPositionId;
    /**
     * 办公室Id
     */
    @ApiModelProperty("办公室Id")
    private Long fkOfficeId;
    /**
     * 直属上司Id（公司架构）
     */
    @ApiModelProperty("直属上司Id（公司架构）")
    private Long fkStaffIdSupervisor;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty("简历guid(人才中心)")
    private String fkResumeGuid;
    /**
     * 登陆用户Id
     */
    @ApiModelProperty("登陆用户Id")
    private String loginId;
    /**
     * 登陆时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("登陆时间")
    private Date loginTime;
    /**
     * 登陆用户密码
     */
    @ApiModelProperty("登陆用户密码")
    private String loginPs;
    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String num;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty("英文名")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty("性别：0女/1男")
    private Integer gender;
    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("生日")
    private Date birthday;
    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String identityCard;
    /**
     * 住址电话
     */
    @ApiModelProperty("住址电话")
    private String homeTel;
    /**
     * 工作电话
     */
    @ApiModelProperty("工作电话")
    private String workTel;
    /**
     * 手机区号
     */
    @ApiModelProperty("手机区号")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty("移动电话")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty("Email")
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty("QQ号")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty("微信号")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty("whatsapp号")
    private String whatsapp;
    /**
     * 紧急联系方式
     */
    @ApiModelProperty("紧急联系方式")
    private String emergencyContact;
    /**
     * 职责
     */
    @ApiModelProperty("职责")
    private String jobDescription;
    /**
     * 工资生效日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("工资生效日期")
    private Date salaryEffectiveDate;
    /**
     * 基本工资
     */
    @ApiModelProperty("基本工资")
    private BigDecimal salaryBase;
    /**
     * 绩效工资
     */
    @ApiModelProperty("绩效工资")
    private BigDecimal salaryPerformance;
    /**
     * 岗位津贴
     */
    @ApiModelProperty("岗位津贴")
    private BigDecimal allowancePosition;
    /**
     * 餐饮津贴
     */
    @ApiModelProperty("餐饮津贴")
    private BigDecimal allowanceCatering;
    /**
     * 交通津贴
     */
    @ApiModelProperty("交通津贴")
    private BigDecimal allowanceTransportation;
    /**
     * 通讯津贴
     */
    @ApiModelProperty("通讯津贴")
    private BigDecimal allowanceTelecom;
    /**
     * 其他津贴
     */
    @ApiModelProperty("其他津贴")
    private BigDecimal allowanceOther;
    /**
     * 入职时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("入职时间")
    private Date entryDate;
    /**
     * 转正时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("转正时间")
    private Date passProbationDate;
    /**
     * 离职时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("离职时间")
    private Date leaveDate;
    /**
     * 是否在职，0否/1是
     */
    @ApiModelProperty("是否在职，0否/1是")
    private Boolean isOnDuty;
    /**
     * 是否领取离职证明，0否/1是
     */
    @ApiModelProperty("是否领取离职证明，0否/1是")
    private Boolean isGetLeavingCertificate;
    /**
     * 是否已经停保，0否/1是
     */
    @ApiModelProperty("是否已经停保，0否/1是")
    private Boolean isStopSocialInsurance;
    /**
     * 停保年月
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    @ApiModelProperty("停保年月")
    private String stopSocialInsuranceMonth;
    /**
     * 年假基数
     */
    @ApiModelProperty("年假基数")
    private BigDecimal annualLeaveBase;
    /**
     * 补休基数
     */
    @ApiModelProperty("补休基数")
    private BigDecimal compensatoryLeaveBase;
    /**
     * 是否强制修改密码，0否/1是
     */
    @ApiModelProperty("是否强制修改密码，0否/1是")
    private Boolean isModifiedPs;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 是否超级管理员：0否/1是
     */
    @ApiModelProperty("是否超级管理员：0否/1是")
    private Boolean isAdmin;
    /**
     * 用户当前登陆会话id
     */
    private String sessionId;
    /**
     * 是否可编辑简历，0否/1是
     */
    private Boolean isModifiedResume;

    /**
     * tokenSessionId
     */
    private String tokenSessionId;
    /**
     * 第三方认证ID
     */
    private String oauthId;

    /**
     * 平台类型
     */
    private String platformType;

    public GetUserDetails(Long staffId,String tokenSessionId, String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities, Long fkCompanyId, String loginId, String num, String name, String nameEn, Integer gender, Boolean isActive, Boolean isAdmin, String sessionId, Boolean isModifiedResume, String oauthId, Date loginTime,String platformType) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
        this.staffId = staffId;
        this.fkCompanyId = fkCompanyId;
        this.tokenSessionId = tokenSessionId;
//		this.fkDepartmentId = fkDepartmentId;
//		this.fkPositionId = fkPositionId;
//		this.fkOfficeId = fkOfficeId;
//		this.fkStaffIdSupervisor = fkStaffIdSupervisor;
//		this.fkResumeGuid = fkResumeGuid;
        this.loginId = loginId;
//		this.loginPs = loginPs;
        this.num = num;
        this.name = name;
        this.nameEn = nameEn;
        this.gender = gender;
//		this.birthday = birthday;
//		this.identityCard = identityCard;
//		this.homeTel = homeTel;
//		this.workTel = workTel;
//		this.mobileAreaCode = mobileAreaCode;
//		this.mobile = mobile;
//		this.email = email;
//		this.zipCode = zipCode;
//		this.address = address;
//		this.qq = qq;
//		this.wechat = wechat;
//		this.whatsapp = whatsapp;
//		this.emergencyContact = emergencyContact;
//		this.jobDescription = jobDescription;
//		this.salaryEffectiveDate = salaryEffectiveDate;
//		this.salaryBase = salaryBase;
//		this.salaryPerformance = salaryPerformance;
//		this.allowancePosition = allowancePosition;
//		this.allowanceCatering = allowanceCatering;
//		this.allowanceTransportation = allowanceTransportation;
//		this.allowanceTelecom = allowanceTelecom;
//		this.allowanceOther = allowanceOther;
//		this.entryDate = entryDate;
//		this.passProbationDate = passProbationDate;
//		this.leaveDate = leaveDate;
//		this.isOnDuty = isOnDuty;
//		this.isGetLeavingCertificate = isGetLeavingCertificate;
//		this.isStopSocialInsurance = isStopSocialInsurance;
//		this.stopSocialInsuranceMonth = stopSocialInsuranceMonth;
//		this.annualLeaveBase = annualLeaveBase;
//		this.compensatoryLeaveBase = compensatoryLeaveBase;
//		this.isModifiedPs = isModifiedPs;
        this.isActive = isActive;
        this.isAdmin = isAdmin;
        this.sessionId = sessionId;
        this.isModifiedResume = isModifiedResume;
        this.oauthId = oauthId;
//		this.apiKeys = apiKeys;
//		this.resourceKeys = resourceKeys;
//		this.countryIds = countryIds;
//		this.companyIds = companyIds;
        this.loginTime = loginTime;
        this.platformType = platformType;
    }
}
