package com.get.authentication.endpoint;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.generator.ImageCaptchaGenerator;
import cloud.tianai.captcha.generator.common.model.dto.ImageCaptchaInfo;
import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import com.get.authentication.handle.ExtCaptchaApplicationService;
import com.get.authentication.handle.ExtImageCaptchaApplication;
import com.get.common.cache.CacheNames;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.jwt.JwtUtil;
import com.get.core.jwt.props.JwtProperties;
import com.get.core.log.feign.ILogClient;
import com.get.core.log.model.LogLogin;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.start.constant.TokenConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.support.Kv;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.StringUtil;
import com.get.core.tool.utils.WebUtil;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.permissioncenter.vo.StaffInfoVo;
import com.get.permissioncenter.vo.StaffVo;
//import com.get.schoolGateCenter.dto1.SchoolGateStaffDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
//import com.get.schoolGateCenter.dto1.UserDto;
import com.wf.captcha.SpecCaptcha;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import static com.get.common.cache.CacheNames.BIZ_CACHE;
import static com.get.core.cache.constant.CacheConstant.HELP_DESCRIPTION_CODE;
import static com.get.core.cache.constant.CacheConstant.USER_CACHE;

@Slf4j
@RestController
@AllArgsConstructor
public class GetTokenEndPoint {
    //授权用户IAE访问桶网址
    public final static String OSS_FILES_IAE_PRD_URL = "https://hti-ais-files-prd-1301376564.cos.ap-shanghai.myqcloud.com";
    //授权用户HTI访问桶网址
    public final static String OSS_FILES_HTI_PRD_URL = "https://hti-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com";
    //授权用户访问桶网址
    public final static String OSS_FILES_DEV_URL = "https://hti-ais-files-dev-1301376564.cos.ap-shanghai.myqcloud.com";
//    public final static String OSS_FILES_PRD_URL = "https://get-bms-files-prd-1304425382.cos.ap-shanghai.myqcloud.com";

    public final static String OSS_FILES_PRD_URL = "https://hti-ais-files-prd-1301376564.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_FILES_TEST_URL = "https://get-bms-files-test-1304425382.cos.ap-shanghai.myqcloud.com";

    //公开用户访问桶网址
    public final static String OSS_IMAGES_DEV_URL = "https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com";
    //public final static String OSS_IMAGES_PRD_URL = "https://get-bms-images-prd-1304425382.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_IMAGES_PRD_URL = "https://hti-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_IMAGES_TEST_URL = "https://get-bms-images-test-1304425382.cos.ap-shanghai.myqcloud.com";
    private final GetRedis getRedis;
    private final IPermissionCenterClient permissionCenterClient;
    private final JwtProperties jwtProperties;
    private final ILogClient logClient;

    //获取用户信息,暂时无用
    @GetMapping("/oauth/user-info")
    @VerifyPermission(IsVerify = false)
    public Result<Authentication> currentUser(Authentication authentication) {
        return Result.data(authentication);
    }

    //获取用户信息
    @GetMapping("/oauth/getUser")
    @VerifyPermission(IsVerify = false)
    public Result<StaffInfoVo> getUser() {
        StaffInfo staffInfo = null;
        try {
            staffInfo = SecureUtil.getStaffInfo();
        }catch (Exception e)
        {
            e.printStackTrace();
            log.error("getUser获取用户信息异常："+e.getMessage());
            return Result.fail(ResultCode.SESSION_EXPIRED, ResultCode.SESSION_EXPIRED.getMessage());
        }
        if (GeneralTool.isEmpty(staffInfo) || GetAuthInfo.getStaffId() == null) {
//            Result.fail("没有找到当前用户");
            return Result.fail(ResultCode.SESSION_EXPIRED, ResultCode.SESSION_EXPIRED.getMessage());
        }

        StaffInfoVo staffInfoVo = BeanCopyUtils.objClone(staffInfo, StaffInfoVo::new);
        staffInfoVo.setId(staffInfo.getStaffId());
        Object customColumnJson = CacheUtil.get(USER_CACHE, String.valueOf(GetAuthInfo.getStaffId()), ProjectKeyEnum.CUSTOM_COLUMN_JSON.key);
        staffInfoVo.setCustomColumnJson(null == customColumnJson ? "" : customColumnJson.toString());
        if (GeneralTool.isNotEmpty(SecureUtil.getApiKeys())) {
            Set<String> apiKeysSet = new HashSet<>(SecureUtil.getApiKeys());
            staffInfoVo.setApiKeys(apiKeysSet);
        }
        if (GeneralTool.isNotEmpty(SecureUtil.getApiResourceKeys())) {
            staffInfoVo.setResourceKeys(SecureUtil.getApiResourceKeys());
        }
        if (GeneralTool.isNotEmpty(SecureUtil.getCompanyIds())) {
            staffInfoVo.setCompanyIds(SecureUtil.getCompanyIds());
        }
        if (GeneralTool.isNotEmpty(SecureUtil.getCountryIds())) {
            staffInfoVo.setCountryIds(SecureUtil.getCountryIds());
        }
        if (GeneralTool.isNotEmpty(SecureUtil.getInstitutionIds())) {
            staffInfoVo.setInstitutionIds(SecureUtil.getInstitutionIds());
        }
        List<HelpInfoVo> list = CacheUtil.get(BIZ_CACHE, String.valueOf(GetAuthInfo.getStaffId()), HELP_DESCRIPTION_CODE, List.class);
        if (GeneralTool.isNotEmpty(list)){
            staffInfoVo.setHelpInfo(list);
        }

        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        System.out.println("=====>当前运行环境：" + profile);
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);
                staffInfoVo.setBmsHtiPublicFilesUrl(OSS_IMAGES_PRD_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);
            }else if (profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);//IAE环境下使用私密桶地址为IAE桶url
                staffInfoVo.setBmsHtiPublicFilesUrl(OSS_FILES_HTI_PRD_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);//IAE环境下使用公开桶地址为GET桶url
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
            }else if (profile.equals(AppConstant.TEST_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_TEST_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_TEST_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
                staffInfoVo.setBmsHtiPublicFilesUrl(OSS_IMAGES_TEST_URL);
            } else {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_DEV_URL);
                staffInfoVo.setBmsHtiPublicFilesUrl(OSS_IMAGES_DEV_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_DEV_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
            }
        }
        return Result.data(staffInfoVo);
    }

    //获取用户信息
    @GetMapping("/oauth/getSchoolGateUser")
    @VerifyPermission(IsVerify = false)
    public Result<StaffInfoVo> getSchoolGateUser() {
        StaffInfo staffInfo = (StaffInfo)CacheUtil.get("get:gateUser", String.valueOf(GetAuthInfo.getStaffId()), "staffInfo:code:", StaffInfo.class);
        if (GeneralTool.isEmpty(staffInfo) || GetAuthInfo.getStaffId() == null) {
            Result.fail("没有找到当前用户");
        }
        StaffInfoVo staffInfoVo = BeanCopyUtils.objClone(staffInfo, StaffInfoVo::new);
        staffInfoVo.setId(staffInfo.getStaffId());

        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        System.out.println("=====>当前运行环境：" + profile);
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)|| profile.equals(AppConstant.TW_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);
            }else if (profile.equals(AppConstant.IAE_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);//IAE环境下使用私密桶地址为IAE桶url
                staffInfoVo.setBmsHtiPublicFilesUrl(OSS_FILES_HTI_PRD_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);//IAE环境下使用公开桶地址为GET桶url
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
            }else if (profile.equals(AppConstant.TEST_CODE)) {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_TEST_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_TEST_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
            } else {
                staffInfoVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);
                staffInfoVo.setBmsPublicFilesUrl(OSS_IMAGES_DEV_URL);
                staffInfoVo.setBmsPrivateFilesShareUrl(OSS_FILES_DEV_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
            }
        }
        return Result.data(staffInfoVo);
    }

    //获取用户信息
    @GetMapping("/oauth/getAppSchoolGateUser")
    @VerifyPermission(IsVerify = false)
    public Result<StaffInfoVo> getAppSchoolGateUser() {
//        System.out.println("用户"+GetAuthInfo.getStaffId());
//        UserDto staffInfo = (UserDto)CacheUtil.get("get:AppGateUser", String.valueOf(GetAuthInfo.getStaffId()), "appStaffInfo:code:", UserDto.class);
//        if (GeneralTool.isEmpty(staffInfo) || GetAuthInfo.getStaffId() == null) {
//            Result.fail("没有找到当前用户");
//        }
//        StaffInfoVo staffInfoDto = BeanCopyUtils.objClone(staffInfo, StaffInfoVo::new);
//        staffInfoDto.setId(staffInfo.getId());

//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        System.out.println("=====>当前运行环境：" + profile);
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                staffInfoDto.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);
//                staffInfoDto.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);
//                staffInfoDto.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);
//            }else if (profile.equals(AppConstant.IAE_CODE)) {
//                staffInfoDto.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);//IAE环境下使用私密桶地址为IAE桶url
//                staffInfoDto.setBmsHtiPublicFilesUrl(OSS_FILES_HTI_PRD_URL);
//                staffInfoDto.setBmsPublicFilesUrl(OSS_IMAGES_PRD_URL);//IAE环境下使用公开桶地址为GET桶url
//                staffInfoDto.setBmsPrivateFilesShareUrl(OSS_FILES_PRD_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
//            }else if (profile.equals(AppConstant.TEST_CODE)) {
//                staffInfoDto.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);
//                staffInfoDto.setBmsPublicFilesUrl(OSS_IMAGES_TEST_URL);
//                staffInfoDto.setBmsPrivateFilesShareUrl(OSS_FILES_TEST_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
//            } else {
//                staffInfoDto.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);
//                staffInfoDto.setBmsPublicFilesUrl(OSS_IMAGES_DEV_URL);
//                staffInfoDto.setBmsPrivateFilesShareUrl(OSS_FILES_DEV_URL);//IAE环境下共享桶url为GET桶url，目前仅在学校模块使用
//            }
//        }
//        return Result.data(staffInfoDto);
        return null;
    }




    //获取验证码
    @GetMapping("/oauth/getCap")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public Kv captcha() {
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        String verCode = specCaptcha.text().toLowerCase();
        String key = StringUtil.randomUUID();
        // 存入redis并设置过期时间为30分钟
        getRedis.setEx(CacheNames.CAPTCHA_KEY + key, verCode, Duration.ofMinutes(30));
        // 将key和base64返回给前端
        return Kv.create().set("key", key).set("image", specCaptcha.toBase64());
    }
    @Autowired
    private ExtCaptchaApplicationService extCaptchaApplicationService;

    @Resource
    private ImageCaptchaGenerator imageCaptchaGenerator;

    /**
     * Author Cream
     * Description : //获取滑块验证码
     * Date 2023/8/22 9:57
     * Params:
     * Return
     */
    @GetMapping("/oauth/getCaptcha")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ResponseBo<CaptchaResponse<ImageCaptchaVO>> getCaptcha(){
        ImageCaptchaInfo imageCaptchaInfo = imageCaptchaGenerator.generateCaptchaImage(CaptchaTypeConstant.SLIDER);
        ExtImageCaptchaApplication extImageCaptchaApplication = (ExtImageCaptchaApplication)extCaptchaApplicationService;
        CaptchaResponse<ImageCaptchaVO> imageCaptchaVOCaptchaResponse = extImageCaptchaApplication.afterGenerateSliderCaptcha(imageCaptchaInfo);
        return new ResponseBo<>(imageCaptchaVOCaptchaResponse);
    }

    /**
     * Author Cream
     * Description : //校验滑块验证码
     * Date 2023/8/22 9:57
     * Params:
     * Return
     */
    @PostMapping("/oauth/checkCaptcha")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ResponseBo<Boolean> checkCaptcha(@RequestParam("id") String id,
                                @RequestBody ImageCaptchaTrack imageCaptchaTrack) {
        return new ResponseBo<>(extCaptchaApplicationService.matching(id, imageCaptchaTrack),System.currentTimeMillis());
    }

    /**
     * Author Cream
     * Description : // 登陆成功清除失败记录
     * Date 2023/8/22 9:57
     * Params:
     * Return
     */
    @PostMapping("/oauth/clearLoginFailRecord")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<Boolean> clearLoginFailRecord(Long userId){
        StaffVo data = permissionCenterClient.getStaffById(userId).getData();
        if (GeneralTool.isNotEmpty(data)) {
            String loginId = data.getLoginId();
            getRedis.del(CacheNames.cacheKey(CacheNames.USER_FAIL_KEY, loginId));
        }
        return new ResponseBo<>();
    }

    //下线
    @GetMapping("/oauth/logout")
    @VerifyPermission(IsVerify = false)
    public Result<?> logout() {
        UserInfo user = GetAuthInfo.getUser();
        if (user != null && jwtProperties.getState()) {
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.removeAccessToken(String.valueOf(user.getStaffId()), user.getSessionId(),token);
            LogLogin logLogin = new LogLogin();
            logLogin.setFkStaffId(SecureUtil.getStaffId());
            logLogin.setSessionId(user.getSessionId());
            logClient.updateLogLogin(logLogin);
        }
        return Result.data("success");
    }

    //清理缓存111111
    @GetMapping("/oauth/clearCa")
    public Kv clearCache() {
//		CacheUtil.clear(BIZ_CACHE);
//		CacheUtil.clear(USER_CACHE);
//        CacheUtil.clear(USER_CACHE, Boolean.FALSE);
//		CacheUtil.clear(SYS_CACHE);
//		CacheUtil.clear(MENU_CACHE);
        return Kv.create().set("success", "true").set("msg", "success");
    }

}
