package com.get.authentication.constant;

/**
 * 授权常量
 */
public interface AuthConstant {

    /**
     * 密码加密
     */
    String ENCRYPT = "{get}";


    /**
     * m_permission_client表字段
     */
    String CLIENT_FIELDS = "client_id, CONCAT('{noop}',client_secret) as client_secret, resource_ids, scope, authorized_grant_types, " +
            "web_server_redirect_uri, authorities, access_token_validity, " +
            "refresh_token_validity, additional_information, autoapprove";

    /**
     * m_permission_client查询语句
     */
    String BASE_STATEMENT = "select " + CLIENT_FIELDS + " from m_permission_client";

    /**
     * m_permission_client查询排序
     */
    String DEFAULT_FIND_STATEMENT = BASE_STATEMENT + " order by client_id";

    /**
     * 查询client_id
     */
    String DEFAULT_SELECT_STATEMENT = BASE_STATEMENT + " where client_id = ?";

}
