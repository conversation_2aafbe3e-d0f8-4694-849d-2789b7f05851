package com.get.authentication.config;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.generator.common.constant.SliderCaptchaConstant;
import cloud.tianai.captcha.resource.common.model.dto.Resource;
import cloud.tianai.captcha.resource.impl.DefaultResourceStore;
import cloud.tianai.captcha.resource.impl.provider.ClassPathResourceProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static cloud.tianai.captcha.generator.impl.StandardSliderImageCaptchaGenerator.DEFAULT_SLIDER_IMAGE_TEMPLATE_PATH;

/**
 * <AUTHOR>
 */
@Component
public class CaptchaResourceConfig extends DefaultResourceStore {

    public CaptchaResourceConfig(){
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/1.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/2.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/3.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/4.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/5.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/6.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/7.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/8.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/9.jpg"));
        addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "captcha/10.jpg"));

    }
}
