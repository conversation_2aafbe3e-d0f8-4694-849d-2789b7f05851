package com.get.authentication.config;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 * 自定义登录成功配置
 */
@Configuration
@AllArgsConstructor
@EnableResourceServer
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    @Override
    @SneakyThrows
    public void configure(HttpSecurity http) {
        http.headers().frameOptions().disable();
        http.formLogin()
                .and()
                .authorizeRequests()
                .antMatchers(
                        "/actuator/**",
                        "/oauth/getCap",
                        "/oauth/getUser",
                        "/oauth/clearLoginFailRecord",
                        "/oauth/getSchoolGateUser",
                        "/oauth/getAppSchoolGateUser",
                        "/oauth/logout",
                        "/oauth/clearCa",
                        "/oauth/rd/**",
                        "/oauth/getCaptcha",
                        "/oauth/checkCaptcha",
                        "/oauth/cb/**",
                        "/oauth/rk/**",
                        "/oauth/rr/**",
                        "/token/**",
                        "/mobile/**",
                        "/wx/oauth/**",
                        "/wx/wxCpTp/**",
                        "/oauth/token/**",
                        "/v2/api-docs").permitAll()
                .anyRequest().authenticated().and()
                .csrf().disable();
    }

}
