package com.get.authentication.handle;

import cloud.tianai.captcha.common.exception.ImageCaptchaException;
import cloud.tianai.captcha.generator.ImageCaptchaGenerator;
import cloud.tianai.captcha.generator.common.model.dto.GenerateParam;
import cloud.tianai.captcha.generator.common.model.dto.ImageCaptchaInfo;
import cloud.tianai.captcha.resource.ImageCaptchaResourceManager;
import cloud.tianai.captcha.spring.application.CaptchaImageType;
import cloud.tianai.captcha.spring.application.DefaultImageCaptchaApplication;
import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.autoconfiguration.ImageCaptchaProperties;
import cloud.tianai.captcha.spring.exception.CaptchaValidException;
import cloud.tianai.captcha.spring.store.CacheStore;
import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import cloud.tianai.captcha.validator.ImageCaptchaValidator;
import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import cloud.tianai.captcha.validator.impl.SimpleImageCaptchaValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @Author:猫猫吉祥
 * @Date: 2023/8/30  12:01
 */
public class ExtImageCaptchaApplication implements ExtCaptchaApplicationService {
    private static final Logger log = LoggerFactory.getLogger(DefaultImageCaptchaApplication.class);
    private ImageCaptchaGenerator template;
    private ImageCaptchaValidator imageCaptchaValidator;
    private CacheStore cacheStore;
    private final ImageCaptchaProperties prop;
    private long defaultExpire = 20000L;

    public ExtImageCaptchaApplication(ImageCaptchaGenerator template, ImageCaptchaValidator imageCaptchaValidator, CacheStore cacheStore, ImageCaptchaProperties prop) {
        this.prop = prop;
        this.setImageCaptchaTemplate(template);
        this.setImageCaptchaValidator(imageCaptchaValidator);
        this.setCacheStore(cacheStore);
        Long defaultExpire = (Long)prop.getExpire().get("default");
        if (defaultExpire != null && defaultExpire > 0L) {
            this.defaultExpire = defaultExpire;
        }

    }

    @Override
    public CaptchaResponse<ImageCaptchaVO> generateCaptcha() {
        return this.generateCaptcha("SLIDER");
    }

    @Override
    public CaptchaResponse<ImageCaptchaVO> generateCaptcha(String type) {
        ImageCaptchaInfo slideImageInfo = this.getImageCaptchaTemplate().generateCaptchaImage(type);
        return this.afterGenerateSliderCaptcha(slideImageInfo);
    }

    @Override
    public CaptchaResponse<ImageCaptchaVO> generateCaptcha(GenerateParam param) {
        ImageCaptchaInfo slideImageInfo = this.getImageCaptchaTemplate().generateCaptchaImage(param);
        return this.afterGenerateSliderCaptcha(slideImageInfo);
    }

    @Override
    public CaptchaResponse<ImageCaptchaVO> generateCaptcha(CaptchaImageType captchaImageType) {
        return this.generateCaptcha("SLIDER", captchaImageType);
    }

    @Override
    public CaptchaResponse<ImageCaptchaVO> generateCaptcha(String type, CaptchaImageType captchaImageType) {
        GenerateParam param = new GenerateParam();
        if (CaptchaImageType.WEBP.equals(captchaImageType)) {
            param.setBackgroundFormatName("webp");
            param.setSliderFormatName("webp");
        } else {
            param.setBackgroundFormatName("jpeg");
            param.setSliderFormatName("png");
        }

        param.setType(type);
        return this.generateCaptcha(param);
    }

    public CaptchaResponse<ImageCaptchaVO> afterGenerateSliderCaptcha(ImageCaptchaInfo slideImageInfo) {
        if (slideImageInfo == null) {
            throw new ImageCaptchaException("生成滑块验证码失败，验证码生成为空");
        } else {
            String id = this.generatorId();
            Map<String, Object> validData = this.getImageCaptchaValidator().generateImageCaptchaValidData(slideImageInfo);
            this.cacheVerification(id, slideImageInfo.getType(), validData);
            ImageCaptchaVO verificationVO = new ImageCaptchaVO();
            verificationVO.setBackgroundImage(slideImageInfo.getBackgroundImage());
            verificationVO.setSliderImage(slideImageInfo.getSliderImage());
            verificationVO.setBackgroundImageWidth(slideImageInfo.getBgImageWidth());
            verificationVO.setBackgroundImageHeight(slideImageInfo.getBgImageHeight());
            verificationVO.setSliderImageWidth(slideImageInfo.getSliderImageWidth());
            verificationVO.setSliderImageHeight(slideImageInfo.getSliderImageHeight());
            verificationVO.setData(slideImageInfo.getData());
            return CaptchaResponse.of(id, verificationVO);
        }
    }

    @Override
    public boolean matching(String id, ImageCaptchaTrack imageCaptchaTrack) {
        Map<String, Object> cachePercentage = this.getVerification(id);
        return cachePercentage != null && this.getImageCaptchaValidator().valid(imageCaptchaTrack, cachePercentage);
    }

    @Override
    public boolean matching(String id, Float percentage) {
        Map<String, Object> cachePercentage = this.getVerification(id);
        if (cachePercentage == null) {
            return false;
        } else {
            ImageCaptchaValidator imageCaptchaValidator = this.getImageCaptchaValidator();
            if (!(imageCaptchaValidator instanceof SimpleImageCaptchaValidator)) {
                return false;
            } else {
                SimpleImageCaptchaValidator simpleImageCaptchaValidator = (SimpleImageCaptchaValidator)imageCaptchaValidator;
                Float oriPercentage = simpleImageCaptchaValidator.getFloatParam("percentage", cachePercentage);
                Float tolerant = simpleImageCaptchaValidator.getFloatParam("tolerant", cachePercentage, simpleImageCaptchaValidator.getDefaultTolerant());
                return simpleImageCaptchaValidator.checkPercentage(percentage, oriPercentage, tolerant);
            }
        }
    }

    protected String generatorId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    protected Map<String, Object> getVerification(String id) {
        return this.getCacheStore().getCache(this.getKey(id));
    }

    @Override
    public void remove(String id){
        this.getCacheStore().getAndRemoveCache(this.getKey(id));
    }

    protected void cacheVerification(String id, String type, Map<String, Object> validData) {
        Long expire = (Long)this.prop.getExpire().getOrDefault(type, this.defaultExpire);
        if (!this.getCacheStore().setCache(this.getKey(id), validData, expire, TimeUnit.MILLISECONDS)) {
            log.error("缓存验证码数据失败， id={}, validData={}", id, validData);
            throw new CaptchaValidException(type, "缓存验证码数据失败");
        }
    }

    protected String getKey(String id) {
        return this.prop.getPrefix().concat(":").concat(id);
    }

    @Override
    public ImageCaptchaResourceManager getImageCaptchaResourceManager() {
        return this.getImageCaptchaTemplate().getImageResourceManager();
    }

    @Override
    public void setImageCaptchaValidator(ImageCaptchaValidator imageCaptchaValidator) {
        this.imageCaptchaValidator = imageCaptchaValidator;
    }

    @Override
    public void setImageCaptchaTemplate(ImageCaptchaGenerator imageCaptchaGenerator) {
        this.template = imageCaptchaGenerator;
    }

    @Override
    public void setCacheStore(CacheStore cacheStore) {
        this.cacheStore = cacheStore;
    }

    @Override
    public ImageCaptchaValidator getImageCaptchaValidator() {
        return this.imageCaptchaValidator;
    }

    @Override
    public ImageCaptchaGenerator getImageCaptchaTemplate() {
        return this.template;
    }

    @Override
    public CacheStore getCacheStore() {
        return this.cacheStore;
    }
}
