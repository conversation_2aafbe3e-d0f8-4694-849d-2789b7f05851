<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hti-java-ais</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>authentication</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.RELEASE</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>cloud.tianai.captcha</groupId>
            <artifactId>tianai-captcha-springboot-starter</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-db</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cloud</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-metrics</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-redis</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cache</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-swagger</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.3.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.2.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
        </dependency>
        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>permission-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-jdbc</artifactId>
                    <groupId>org.apache.tomcat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>sys-log</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>help-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <!-- 链路追踪、服务监控 -->
        <!--<dependency>
            <groupId>com.common</groupId>
            <artifactId>comm-trace</artifactId>
        </dependency>-->
        <!-- 解决Java11中可能出现的无法运行的问题 -->
        <!--<dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>-->
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!--打包docker镜像的docker服务器-->
                        <dockerHost>${docker-url}</dockerHost>
                        <!--镜像名，这里用工程名 -->
                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}</imageName>
                        <!--nexus3 hosted 仓库地址-->
                        <registryUrl>${registry-url}</registryUrl>
                        <!-- ca认证正书-->
                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                        <!--TAG,这里用工程版本号-->
                        <imageTags>
                            <!-- 指定镜像标签,可以排至多个标签 -->
                            <imageTag>${nexus.version}</imageTag>
                            <imageTag>latest</imageTag>
                        </imageTags>
                        <!--是否强制覆盖已有镜像-->
                        <forceTags>true</forceTags>
                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                        <dockerDirectory>${project.artifactId}/src/main/docker</dockerDirectory>
                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                        </buildArgs>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                        <serverId>nexus-docker-prod</serverId>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar" />
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>