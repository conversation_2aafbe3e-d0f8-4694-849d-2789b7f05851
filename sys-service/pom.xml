<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hti-java-ais</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sys-service</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>系统微服务集合</description>

    <modules>
        <module>sys-log</module>
        <module>sys-swagger</module>
        <module>permission-center</module>
        <module>workflow-center</module>
        <module>platform-config-center</module>
        <module>xxljob</module>
        <module>xxljob-admin</module>
        <module>file-center</module>
        <module>help-center</module>
        <module>websocket</module>
        <module>seata-demo</module>
        <module>sys-boot-admin</module>
        <module>mybatisplus-generator</module>
    </modules>


    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>common</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-metrics</artifactId>
            <version>${get.project.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.get</groupId>-->
<!--            <artifactId>authority-ap</artifactId>-->
<!--            <version>${get.project.version}</version>-->
<!--        </dependency>-->

    </dependencies>

</project>